<template>
  <div class="tab-container">
    <ElForm
      ref="formRef"
      :model="form"
      :rules="partnerSearchRules"
      inline>
      <ElFormItem
        label="生态合作伙伴:"
        prop="partnerName">
        <ElInput
          v-model="form.partnerName"
          clearable
          style="width: 240px;"
          placeholder="输入合作伙伴名称查询" />
      </ElFormItem>
      <ElFormItem
        label="标签:"
        prop="tag">
        <ElInput
          v-model="form.tag"
          clearable
          style="width: 240px;"
          placeholder="输入标签名称查询" />
      </ElFormItem>
      <ElFormItem>
        <ElButton
          type="primary"
          @click="handleSearch">
          查询
        </ElButton>
        <ElButton
          type="default"
          @click="handleReset">
          重置
        </ElButton>
      </ElFormItem>
    </ElForm>

    <div class="partner-list">
      <div
        v-for="item in partnerList"
        :key="item.companyBid"
        class="partner-cart"
        @click="router.push(`/ecology/partner/detail?companyBid=${item.companyBid}
        &auditRecordBid=${item.auditRecordBid}`)">
        <div class="partner-content">
          <div class="partner-logo">
            <img :src="item.logo">
          </div>
          <div class="partner-content__right">
            <div class="header">
              <h3>{{ item.companyName }}</h3>
              <div style="display: flex; flex-wrap: wrap; margin-top: 5px;">
                <!-- 循环显示前showTagsNum个标签 -->
                <div
                  v-for="(tag, index) in showTags(item)"
                  :key="index"
                  style="display: flex; margin-right: 6px; margin-bottom: 6px;">
                  <ElTag :type="getTagType(item, index)">
                    {{ tag }}
                  </ElTag>
                </div>
                <!-- 当总标签数超过showTagsNum个时，显示剩余数量 -->
                <div
                  v-if="item.totalTags.length > showTagsNum"
                  style="display: flex; margin-right: 6px; margin-bottom: 6px;">
                  <ElTooltip
                    placement="top">
                    <ElTag type="info">
                      +{{ item.totalTags.length - showTagsNum }}
                    </ElTag>
                    <template #content>
                      <span>
                        {{ joinedTags(item) }}
                      </span>
                    </template>
                  </ElTooltip>
                </div>
              </div>
              <div
                class="introduction">
                {{ item.introduction }}
              </div>
              <div class="product">
                主营产品:<span style="text-decoration: underline;">{{ item.businessProducts }}</span>
              </div>
            </div>
            <div class="footer">
              <span>历史合作</span>
              <span> <i
                       class="iconfont icon-ClockCircleOutlined"
                       style="margin-right: 2px;font-size: 11px;" />
                {{ formatData(item.updateTime) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pagination">
      <ElPagination
        :current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        layout="total, prev, pager, next, sizes, jumper"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script setup>
import * as partnerApi from '@/api/ecology/partner.js'
import { usePagination } from '@/utils/hooks.js'
import { formatData } from '@/views/ecology/partner/config.js'
import { partnerSearchRules } from '@/views/ecology/partner/rules.js'

const { pagination } = usePagination()

const formRef = ref(null)
// 伙伴搜索表单
const form = ref({
  type: '',
  partnerName: '',
  tag: '',
})
// 伙伴列表
const partnerList = ref([])

const router = useRouter()

// 当前分类数节点
const currentNodeKey = defineModel('currentNodeKey')

/** 显示标签的数据 */
const showTagsNum = ref(3)
function showTags(item) {
  return item.totalTags.slice(0, showTagsNum.value)
}
// 根据索引确定标签类型
function getTagType(item, index) {
  const primaryLength = item.primaryTags.length
  const secondaryLength = item.secondaryTags.length
  if (index < primaryLength) {
    return 'warning'
  } else if (index < primaryLength + secondaryLength) {
    return 'primary'
  } else {
    return 'success'
  }
}
function joinedTags(item) {
  const tagsToJoin = item.totalTags.slice(showTagsNum.value, item.totalTags.length)
  return tagsToJoin.join('、')
}
// 获取伙伴列表
async function getPartnerList() {
  try {
    const res = await partnerApi.getPartnerList({
      ...form.value,
      ...pagination,
    })
    partnerList.value = res.data.records.map((item) => {
      const parsedClass = JSON.parse(item.partnerClass)
      // 一级标签
      const primaryTags = []
      const secondaryTags = []
      const tertiaryTags = []
      const totalTags = []
      parsedClass.forEach((subItem) => {
        if (typeof subItem === 'object') {
          if (subItem[0]) {
            if (!primaryTags.includes(subItem[0])) {
              primaryTags.push(subItem[0])
              totalTags.push(subItem[0])
            }
          }
          if (subItem[1]) {
            if (!secondaryTags.includes(subItem[1])) {
              secondaryTags.push(subItem[1])
              totalTags.push(subItem[1])
            }
          }
          if (subItem[2]) {
            tertiaryTags.push(subItem[2])
            totalTags.push(subItem[2])
          }
        } else {
          totalTags.push(subItem)
          if (!primaryTags.includes(totalTags[0]))
            primaryTags.push(totalTags[0])
          if (!secondaryTags.includes(totalTags[1]))
            secondaryTags.push(totalTags[1])
          if (!tertiaryTags.includes(totalTags[2]))
            tertiaryTags.push(totalTags[2])
        }
      })
      return {
        ...item,
        primaryTags,
        secondaryTags,
        tertiaryTags,
        totalTags,
      }
    })
    pagination.total = Number(res.data.total)
  } catch (error) {
    console.log(error)
  }
}
// 搜索
async function handleSearch() {
  await formRef.value.validate()
  pagination.pageNum = 1
  getPartnerList()
}

// 重置搜索表单
function handleReset() {
  pagination.pageNum = 1
  form.value.type = null
  currentNodeKey.value = null
  formRef.value.resetFields()
  getPartnerList()
}
onMounted(() => {
  getPartnerList()
})
defineExpose({
  form,
  pagination,
  getPartnerList,
})
function handleCurrentChange(value) {
  pagination.pageNum = value
  getPartnerList()
}
function handleSizeChange(value) {
  pagination.pageSize = value
  getPartnerList()
}
</script>

<style scoped lang="scss">
.tab-container {
  display: flex;
  flex: 1;
  flex-direction: column;

  .partner-list {
    display: grid;
    flex: 1;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    overflow: auto;

    .partner-cart {
      display: flex;
      max-height: 200px;
      border: 1px solid rgb(0 0 0 / 6%);
      border-radius: 8px;
      background: #fff;
      cursor: pointer;

      .partner-content {
        display: flex;
        flex: 1;
        margin: 20px 16px;

        .partner-logo {
          width: 64px;
          height: 48px;

          img {
            object-fit: contain;
            width: 100%;
            height: 100%;
          }
        }
      }

      .partner-content__right {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 18px;

        .header {
          flex: 1;

          h3 {
            color: rgb(0 0 0 / 88%);
            font-weight: 600;
            line-height: 24px;
          }

          .label {
            display: flex;
            height: 22px;
            margin-top: 6px;

            .el-col {
              border-radius: 4px;
              font-weight: 400;
              font-size: 12px;
              line-height: 20px;
            }
          }

          .introduction {
            display: -webkit-box;
            overflow: hidden;
            height: 32px;
            margin-top: 16px;
            margin-bottom: 8px;
            color: rgb(0 0 0 / 45%);
            font-size: 12px;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }

          .product {
            color: rgb(0 0 0 / 45%);
            font-size: 12px;
          }
        }

        .footer {
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          color: rgb(0 0 0 / 25%);
          font-size: 12px;
          line-height: 20px;
        }
      }
    }
  }

  .pagination {
    display: flex;
    flex: 0 0 62px;
    justify-content: flex-end;
    align-items: center;
  }
}

:deep(.el-pager li.is-active) {
  border: 1px solid;
  border-radius: 6px;
}
</style>
