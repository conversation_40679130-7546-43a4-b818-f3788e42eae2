<template>
  <div class="main_container">
    <ElForm
      label-position="top"
      :model="formData"
      :rules="rules">
      <Title is-hidden>
        项目文档
      </Title>
      <ElRow
        :gutter="20"
        style="margin-top: 32px">
        <ElCol :span="4">
          <ElFormItem
            label="项目名称"
            prop="projectName">
            <ElInput
              v-model="formData.projectBasicInfo.projectName"
              :disabled="formType === 'view'"
              placeholder="请输入项目名称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="2">
          <ElFormItem label="项目选择">
            <ElButton
              :disabled="formType === 'view'"
              :icon="Link"
              type="primary"
              @click="project_dialog_options.onOpen">
              关联项目
            </ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="项目编号"
            prop="projectBasicInfo.projectCode">
            <ElInput
              v-model="formData.projectBasicInfo.projectCode"
              disabled
              placeholder="项目编号自动生成" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="项目归属部门"
            prop="projectBasicInfo.deliveryEntity">
            <ElInput
              v-model="formData.projectBasicInfo.deliveryEntity"
              disabled
              placeholder="自动关联项目归属部门" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="项目经理"
            prop="projectBasicInfo.projectManager">
            <ElInput
              v-model="formData.projectBasicInfo.projectManager"
              disabled
              placeholder="自动关联项目经理" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="市场经理"
            prop="projectBasicInfo.marketingManager">
            <ElInput
              v-model="formData.projectBasicInfo.marketingManager"
              disabled
              placeholder="自动关联市场经理" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="解决方案经理"
            prop="projectBasicInfo.solutionsManager">
            <ElInput
              v-model="formData.projectBasicInfo.solutionsManager"
              disabled
              placeholder="自动关联解决方案经理" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="执行经理"
            prop="projectBasicInfo.executiveManager">
            <ElInput
              v-model="formData.projectBasicInfo.executiveManager"
              disabled
              placeholder="自动关联执行经理" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="工程类型"
            prop="projectBasicInfo.engineeringType">
            <ElInput
              v-model="formData.projectBasicInfo.engineeringType"
              disabled
              placeholder="工程类型" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="验收日期"
            prop="projectBasicInfo.acceptanceDate">
            <ElDatePicker
              v-model="formData.projectBasicInfo.acceptanceDate"
              disabled
              type="date"
              placeholder="选择验收日期"
              style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="提交人"
            prop="applicationInfo.applicant">
            <ElInput
              v-model="formData.applicationInfo.applicant"
              disabled
              placeholder="自动关联提交人" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="提交日期"
            prop="applicationInfo.applyDate">
            <ElDatePicker
              v-model="formData.applicationInfo.applyDate"
              disabled
              type="date"
              placeholder="选择提交日期"
              style="width: 100%" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <Title
        is-hidden
        style="margin-top: 32px">
        文档提交信息
      </Title>
      <ElRow
        :gutter="20"
        class="mt-8">
        <ElCol :span="6">
          已提交电子文档个数
          <div class="docs-tips-item border-green">
            <span>{{ formData.electronicSubmittedCount || 0 }}</span>
          </div>
        </ElCol>
        <ElCol :span="6">
          缺失电子文档个数
          <div class="docs-tips-item border-red">
            <span>{{ formData.electronicMissingCount || 0 }}</span>
          </div>
        </ElCol>
        <ElCol :span="6">
          待整改电子文档个数
          <div class="docs-tips-item border-orange">
            <span>{{ formData.electronicPendingCount || 0 }}</span>
          </div>
        </ElCol>
      </ElRow>
      <ElRow
        :gutter="20"
        class="mt-8">
        <ElCol :span="6">
          纸质文档入档个数
          <div class="docs-tips-item border-green">
            <span>{{ formData.paperArchivedCount || 0 }}</span>
          </div>
        </ElCol>
        <ElCol :span="6">
          缺失纸质文档个数
          <div class="docs-tips-item border-red">
            <span>{{ formData.paperMissingCount || 0 }}</span>
          </div>
        </ElCol>
      </ElRow>
      <Title
        is-hidden
        style="margin-top: 32px; margin-bottom: 16px">
        文档清单
      </Title>
      <ElRow
        :gutter="20"
        style="margin-top: 16px; margin-bottom: 16px">
        <ElCol
          :span="2"
          style="line-height: 30px">
          项目文档模板
        </ElCol>
        <ElCol :span="6">
          <ElSelect
            v-model="currentTemplate"
            :disabled="formType === 'view'"
            placeholder="请选择"
            @change="handleTemplateChange">
            <ElOption
              v-for="item in projectDocsTemplateList"
              :key="item.bid"
              :value="item.bid"
              :label="item.name" />
          </ElSelect>
        </ElCol>
      </ElRow>
      <ElTabs v-model="activeName">
        <ElTabPane
          label="售前阶段"
          name="first">
          <DocsTable
            ref="firstRef"
            :form-type="formType"
            :roles="currentRoles"
            :data-source="phaseData['售前阶段']" />
        </ElTabPane>
        <ElTabPane
          label="交付阶段"
          name="second">
          <DocsTable
            ref="secondRef"
            :form-type="formType"
            :roles="currentRoles"
            :data-source="phaseData['交付阶段']" />
        </ElTabPane>
        <ElTabPane
          label="采购阶段"
          name="third">
          <DocsTable
            ref="thridRef"
            :form-type="formType"
            :roles="currentRoles"
            :data-source="phaseData['采购阶段']" />
        </ElTabPane>
      </ElTabs>
      <ElRow
        :gutter="20"
        class="mt-8">
        <ElCol :span="24">
          <ElFormItem
            label="文档提交备注"
            prop="projectName">
            <ElInput
              v-model="formData.projectBasicInfo.remark"
              :disabled="formType === 'view'"
              type="textarea"
              :rows="4"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow
        :gutter="20"
        class="mt-8">
        <!-- <ElCol :span="24">
        <ElFormItem
          label="附件"
          prop="projectName">
          <ElInput
            type="textarea"
            :rows="4"
            v-model="formData.projectBasicInfo.remark"
            placeholder="请输入" />
        </ElFormItem>
      </ElCol> -->
        <ElCol :span="24">
          <ElFormItem
            label="附件"
            prop="attachments">
            <ElUpload
              v-model:file-list="formData.attachments"
              :disabled="formType === 'view'"
              style="width: 100%"
              :action="uploadUrl"
              drag
              multiple
              :on-preview="on_preview_or_downFile">
              <ElIcon class="el-icon--upload">
                <UploadFilled />
              </ElIcon>
              <div class="el-upload__text">
                拖动文件或者 <em>点击此处上传</em>
              </div>
            </ElUpload>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ProjectDialog
        v-model="project_dialog_options.visible"
        type="项目文档"
        @checked="project_dialog_options.checked" />
    </ElForm>
  </div>
</template>

<script setup>
import { singleQueryPreProjectInfo } from '@/api/presales/pre-project.js'
import { 
  createProjectDocs, 
  getDocsDetail, 
  getDocsSelectList, 
  getDocsTemplateDetail, 
  submitProjectDocs, 
  updateProjectDocs 
} from '@/api/system/docs.js'
import ProjectDialog from '@/components/dialog/project-dialog.vue'
import Title from '@/components/Title/index.vue'
import useUserStore from '@/store/modules/user.js'
import { UploadFilled } from '@element-plus/icons-vue'
import { uploadUrl } from '@wflow-pro/api/request'
import { dayjs, ElLoading, ElMessage } from 'element-plus'
import { klona } from 'klona'
import { onMounted, watch } from 'vue'
import DocsTable from './docs-table.vue'

const { id, formType } = defineProps({
  id: {
    type: String,
    default: '',
  },
  formType: {
    type: String,
    default: '',
  },
})

const currentRoles = ref([])

const userStore = useUserStore()
const formData = reactive({
  projectBasicInfo: {
    projectName: '',
    projectCode: '',
    deliveryEntity: '',
    deliveryEntityId: '',
    projectManager: '',
    projectManagerId: '',
    marketingManager: '',
    marketingManagerId: '',
    solutionsManager: '',
    solutionsManagerId: '',
    executiveManager: '',
    executiveManagerId: '',
    engineeringType: '',
    remark: '',
    docTemplateId: null,
    docTemplate: '',
    acceptanceDate: '',
  },
  applicationInfo: {
    applicant: userStore.nickName,
    applicantId: userStore.id,
    applyDate: dayjs().format('YYYY-MM-DD'),
  },
  electronicSubmittedCount: 0,
  electronicPendingCount: 0,
  electronicMissingCount: 0,
  paperArchivedCount: 0,
  paperMissingCount: 0,
  formList: [
    {
      classification: '',
      cuttingRequirements: '',
      docName: '',
      duplicatesRemark: '',
      electronicDocuments: '',
      electronicDocumentsCheckDate: '',
      electronicDocumentsCommit: '',
      electronicDocumentsExaminer: '',
      electronicDocumentsExaminerId: '',
      electronicDocumentsRemark: '',
      paperOriginal: '',
      paperOriginalCommit: '',
      paperOriginalCount: '',
      paperOriginalEntryDate: '',
      paperOriginalRecipient: '',
      paperOriginalRecipientId: '',
      paperOriginalRegistrant: '',
      paperOriginalRegistrantId: '',
      paperOriginalRemark: '',
      projectPhase: '',
      remark: '',
      sortField: 0,
      storageLocation: '',
      submitDept: '',
      submitDeptId: '',
    },
  ],
  attachments: [], // 附件
})
const projectDocsTemplateList = ref([])
const currentTemplate = ref('')
const activeName = ref('first')

const firstRef = ref(null)
const secondRef = ref(null)
const thridRef = ref(null)

const phaseData = reactive({
  售前阶段: [],
  交付阶段: [],
  采购阶段: [],
})

watch(
  () => phaseData,
  (newVal) => {
    let electronicSubmittedCount = 0
    let electronicMissingCount = 0
    let electronicPendingCount = 0
    let paperArchivedCount = 0
    let paperMissingCount = 0
    Object.values(newVal).forEach((docs) => {
      docs.forEach((doc) => {
        // 电子文档统计
        switch (doc.electronicDocumentsCommit) {
          case '已提交':
            electronicSubmittedCount++
            break
          case '未提交':
            electronicMissingCount++
            break
          case '待整改':
            electronicPendingCount++
            break
        }
        switch (doc.paperOriginalCommit) {
          case '已提交':
            paperArchivedCount++
            break
          case '未提交':
            paperMissingCount++
            break
        }
      })
    })
    formData.electronicSubmittedCount = electronicSubmittedCount
    formData.electronicMissingCount = electronicMissingCount
    formData.electronicPendingCount = electronicPendingCount
    formData.paperArchivedCount = paperArchivedCount
    formData.paperMissingCount = paperMissingCount
  },
  { deep: true },
)

async function handleTemplateChange() {
  try {
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '模版导入中,请等待',
    })
    setTimeout(() => {
      loadingInstance.close()
    }, 2000)
    const { data } = await getDocsTemplateDetail(currentTemplate.value)

    // 按阶段分类文档
    phaseData['售前阶段'] = data.projectDocTemplate.filter(
      d => d.projectPhase === '售前阶段',
    )
    phaseData['交付阶段'] = data.projectDocTemplate.filter(
      d => d.projectPhase === '交付阶段',
    )
    phaseData['采购阶段'] = data.projectDocTemplate.filter(
      d => d.projectPhase === '采购阶段',
    )
    activeName.value = 'first'
  } catch (error) {
    console.error('Error fetching project detail:', error)
    // ... error handling ...
  }
}

onMounted(async () => {
  const { data } = await getDocsSelectList()
  projectDocsTemplateList.value = data.map(item => ({
    bid: item.bid,
    id: item.id,
    name: item.projectDocName,
  }))
  const { user, roles } = await useUserStore().getInfo()
  // 市场拓展部 207
  // 经营管理部 227
  // 综合办公室 203
  // 采购管理部 224
  // 企业服务事业部 240
  // 集成交付部 211
  if (roles.includes('admin')) {
    currentRoles.value = [1, 2]
  } else if (user.deptId === 227) {
    currentRoles.value = [1]
  } else if (user.deptId === 203) {
    currentRoles.value = [2]
  }

  if (id) {
    try {
      const res = await getDocsDetail(id)
      console.log(res)
      const data = res.data
      console.log(data)
      data.docList.forEach((item) => {
        phaseData[item.projectPhase] = item.formList
      })
      formData.projectBasicInfo.projectName = data.projectName
      formData.projectBasicInfo.projectCode = data.projectCode
      formData.projectBasicInfo.deliveryEntity = data.deliveryEntity
      formData.projectBasicInfo.deliveryEntityId = data.deliveryEntityId
      formData.projectBasicInfo.projectManager = data.projectManager
      formData.projectBasicInfo.projectManagerId = data.projectManagerId
      formData.projectBasicInfo.marketingManager = data.marketingManager
      formData.projectBasicInfo.marketingManagerId = data.marketingManagerId
      formData.projectBasicInfo.solutionsManager = data.solutionsManager
      formData.projectBasicInfo.solutionsManagerId = data.solutionsManagerId
      formData.projectBasicInfo.executiveManager = data.executiveManager
      formData.projectBasicInfo.executiveManagerId = data.executiveManagerId
      formData.projectBasicInfo.engineeringType = data.engineeringType
      currentTemplate.value = data.docTemplateId
        ? projectDocsTemplateList.value.find(
          item => item.id === data.docTemplateId,
        )?.bid
        : null
      formData.projectBasicInfo.remark = data.remark
      formData.applicationInfo.applicant = data.applicant
      formData.applicationInfo.applicantId = data.applicantId
      formData.applicationInfo.applyDate = data.createTime
      formData.projectBasicInfo.acceptanceDate = data.acceptanceDate
      formData.electronicSubmittedCount = data.electronicSubmittedCount
      formData.electronicMissingCount = data.electronicMissingCount
      formData.electronicPendingCount = data.electronicPendingCount
      formData.paperMissingCount = data.paperMissingCount
      formData.paperArchivedCount = data.paperArchivedCount
      // ===== 附件处理 =====
      if (data.attachment) {
        formData.attachments = data.attachment.split(',').map((url) => {
          const name = url.match(/([^/]+)(?=\.[^.]+$|$)/) ? url.match(/([^/]+)(?=\.[^.]+$|$)/)[0] : ''
          return { url, name }
        })
      }
    } catch (error) {
      ElMessage.error('获取文档数据失败')
      console.error('Error fetching project detail:', error)
    }
  }
})

function setValues() {
  return {
    // applicationInfo
    applicant: formData.applicationInfo.applicant,
    applicantId: formData.applicationInfo.applicantId,
    applyDate: formData.applicationInfo.applyDate,

    // projectBasicInfo
    projectName: formData.projectBasicInfo.projectName,
    projectCode: formData.projectBasicInfo.projectCode,
    deliveryEntity: formData.projectBasicInfo.deliveryEntity,
    deliveryEntityId: formData.projectBasicInfo.deliveryEntityId,
    projectManager: formData.projectBasicInfo.projectManager,
    projectManagerId: formData.projectBasicInfo.projectManagerId,
    marketingManager: formData.projectBasicInfo.marketingManager,
    marketingManagerId: formData.projectBasicInfo.marketingManagerId,
    solutionsManager: formData.projectBasicInfo.solutionsManager,
    solutionsManagerId: formData.projectBasicInfo.solutionsManagerId,
    executiveManager: formData.projectBasicInfo.executiveManager,
    executiveManagerId: formData.projectBasicInfo.executiveManagerId,
    engineeringType: formData.projectBasicInfo.engineeringType,
    docTemplateId: currentTemplate.value
      ? projectDocsTemplateList.value.find(
        item => item.bid === currentTemplate.value,
      )?.id
      : null,
    docTemplate: currentTemplate.value
      ? projectDocsTemplateList.value.find(
        item => item.bid === currentTemplate.value,
      )?.name
      : '',
    acceptanceDate: formData.projectBasicInfo.acceptanceDate,
    remark: formData.projectBasicInfo.remark,
    electronicSubmittedCount: formData.electronicSubmittedCount,
    electronicPendingCount: formData.electronicPendingCount,
    electronicMissingCount: formData.electronicMissingCount,
    paperArchivedCount: formData.paperArchivedCount,
    paperMissingCount: formData.paperMissingCount,

    attachment:
      formData.attachments.length > 0
        ? formData.attachments
          .map(item => (item.url ? item.url : item.response.data))
          ?.toString()
        : '',
  }
}

async function saveData() {
  const data = setValues()
  // data.status = 0
  const docList = []
  docList.push(getDocumentStats(firstRef.value.getTableData(), '售前阶段'))
  docList.push(getDocumentStats(secondRef.value.getTableData(), '交付阶段'))
  docList.push(getDocumentStats(thridRef.value.getTableData(), '采购阶段'))
  data.docList = docList.flat()
  data.status = 1
  console.log(data)
  if (id) {
    // 编辑
    data.id = id
    const response = await updateProjectDocs(id, data)
    console.log(response, 'response')
  } else {
    // 提交
    const response = await submitProjectDocs(data)
    console.log(response, 'response')
  }
}

async function saveDraftData() {
  const data = setValues()
  // data.status = 0
  const docList = []
  docList.push(getDocumentStats(firstRef.value.getTableData(), '售前阶段'))
  docList.push(getDocumentStats(secondRef.value.getTableData(), '交付阶段'))
  docList.push(getDocumentStats(thridRef.value.getTableData(), '采购阶段'))
  data.docList = docList.flat()
  data.status = 0
  console.log(data)
  if (id) {
    // 编辑
    data.id = id
    const response = await updateProjectDocs(id, data)
    console.log(response, 'response')
  } else {
    // 保存草稿
    const response = await createProjectDocs(data)
    console.log(response, 'response')
  }
}

function countDocumentStatus(tableData) {
  return tableData.reduce(
    (acc, item) => {
      // 电子文档统计
      acc.electronic[item.electronicDocumentsCommit]
        = (acc.electronic[item.electronicDocumentsCommit] || 0) + 1
      // 纸质文档统计
      acc.paper[item.paperOriginalCommit]
        = (acc.paper[item.paperOriginalCommit] || 0) + 1
      return acc
    },
    { electronic: {}, paper: {} },
  )
}

// 修改后的统计逻辑
function getDocumentStats(tableData, projectPhase) {
  const counts = countDocumentStatus(tableData)
  return {
    projectPhase,
    formList: tableData,
    electronicSubmittedCount: counts.electronic['已提交'] || 0,
    electronicMissingCount: counts.electronic['未提交'] || 0,
    electronicPendingCount: counts.electronic['待整改'] || 0,
    paperArchivedCount: counts.paper['已提交'] || 0,
    paperMissingCount: counts.paper['未提交'] || 0,
  }
}

defineExpose({
  onSave: saveData,
  onSaveDraft: saveDraftData,
  // 获取表单数据
  getFormData: () => {
    return readonly(formData)
  },
  // 审批流程
  saveFormData: async () => {
    try {
      // TODO:需要在此处调用保存接口，保存审批过程中特定人员修改的数据
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})

const project_dialog_options = reactive({
  visible: false,
  project_info: {
    projectManagerName: '',
    projectManagerId: '',
  },
  onOpen: () => {
    project_dialog_options.visible = true
  },
  checked: async (data) => {
    console.log(data)
    /**
     * 需要反填的字段
     * 项目名称 projectName
     * 项目编号 projectCode
     * 项目经理 projectManagerName-projectManagerId
     * 市场经理 marketManagerName-marketManagerId
     * 项目分管领导 leadershipName-leadershipId
     * 项目归属部门 deliverySubject
     * 项目状态
     * 验收日期
     */
    project_dialog_options.pre_project_info = klona(data)
    formData.projectBasicInfo.projectName = data.bolProjectName
    formData.projectBasicInfo.projectCode = data.projectCode
    formData.projectBasicInfo.deliveryEntity = data.deliveryEntity
    formData.projectBasicInfo.deliveryEntityId = data.deliveryEntityId
    formData.projectBasicInfo.projectManager = data.projectManager
    formData.projectBasicInfo.projectManagerId = data.projectManagerId
    formData.projectBasicInfo.marketingManager = data.marketingManager
    formData.projectBasicInfo.marketingManagerId = data.marketingManagerId
    formData.projectBasicInfo.solutionsManager = data.solutionsManager
    formData.projectBasicInfo.solutionsManagerId = data.solutionsManagerId
    formData.projectBasicInfo.executiveManager = data.executiveManager
    formData.projectBasicInfo.executiveManagerId = data.executiveManagerPhone
    formData.projectBasicInfo.engineeringType = data.engineeringType
    formData.projectBasicInfo.acceptanceDate = data.acceptanceDate
    // const res = await singleQueryPreProjectInfo(data.id)
    // if (res?.data?.acceptanceDate) {
    //   formData.projectBasicInfo.acceptanceDate = res.data.acceptanceDate
    // }
  },
})

const rules = {
  'applicationInfo.eolDate': [
    { required: true, message: '请选择日期', trigger: 'change' },
  ],
  'applicationInfo.reason': [
    { required: true, message: '请输入原因', trigger: 'blur' },
  ],
}
</script>

<style lang="scss" scoped>
.main_container {
  flex: 1;
  overflow: auto;
  min-width: 1500px;
  padding: 18px;
  border-radius: 10px;
  background-color: #fff;
}

.docs-tips-item {
  border: 1px solid #eee;
  height: 40px;
  line-height: 40px;
  text-align: center;
  margin-top: 10px;
}

.border-green {
  border-left: 5px solid #0bbba3;
}

.border-red {
  border-left: 5px solid #f5222d;
}

.border-orange {
  border-left: 5px solid #ff8706;
}
</style>
