<template>
  <ElDivider>项目经理核对</ElDivider>
  <ElRow :gutter="20">
    <ElCol :span="20">
      1、销售合同、采购合同或相关补充协议均已验收,质保运维期已结束,无遗留事项,收入、成本已100%结转 ?
    </ElCol>
    <ElCol :span="4">
      <ElRadioGroup
        v-model="formData.projectManagerVerification.contractCompleted"
        :disabled="formType !== 'process'">
        <ElRadio
          label="是"
          :value="1" />
        <ElRadio
          label="否"
          :value="0" />
      </ElRadioGroup>
    </ElCol>
  </ElRow>
  <ElRow :gutter="20">
    <ElCol :span="20">
      2、不存在其他还未采购的工作,已采购项目合同款项已全部支付完毕 ?
    </ElCol>
    <ElCol :span="4">
      <ElRadioGroup
        v-model="formData.projectManagerVerification.procurementPaid"
        :disabled="formType !== 'process'">
        <ElRadio
          label="是"
          :value="1" />
        <ElRadio
          label="否"
          :value="0" />
      </ElRadioGroup>
    </ElCol>
  </ElRow>
  <ElRow :gutter="20">
    <ElCol :span="20">
      3、项目组成员已完成项目相关报销 ?
    </ElCol>
    <ElCol :span="4">
      <ElRadioGroup
        v-model="formData.projectManagerVerification.reimbursementDone"
        :disabled="formType !== 'process'">
        <ElRadio
          label="是"
          :value="1" />
        <ElRadio
          label="否"
          :value="0" />
      </ElRadioGroup>
    </ElCol>
  </ElRow>
  <ElRow :gutter="20">
    <ElCol :span="20">
      4、项目电子文档资料已上传知识库 ?
    </ElCol>
    <ElCol :span="4">
      <ElRadioGroup
        v-model="formData.projectManagerVerification.docsUploaded"
        :disabled="formType !== 'process'">
        <ElRadio
          label="是"
          :value="1" />
        <ElRadio
          label="否"
          :value="0" />
      </ElRadioGroup>
    </ElCol>
  </ElRow>
  <ElRow :gutter="20">
    <ElCol :span="20">
      5、纸质材料已归集,完成电子资料刻盘,并移交综合办归档 ?
    </ElCol>
    <ElCol :span="4">
      <ElRadioGroup
        v-model="formData.projectManagerVerification.paperArchived"
        :disabled="formType !== 'process'">
        <ElRadio
          label="是"
          :value="1" />
        <ElRadio
          label="否"
          :value="0" />
      </ElRadioGroup>
    </ElCol>
  </ElRow>
</template>

<script setup>
const { formType } = defineProps({
  formType: {
    type: String,
    default: '',
  },
})
const formData = defineModel('formData', {
  type: Object,
  default: () => ({}),
})
</script>

<style lang="scss" scoped>

</style>
