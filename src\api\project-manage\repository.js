import { get, post } from '@/utils/alova.js'

const prefix = '/project'

/**
 * 获取项目库分页列表
 * @param {*} data
 * @returns
 */
export function getProjectRepoList(data) {
  return post(`${prefix}/projectLibrary/pageList`, data)
}

/**
 * 获取项目库列表
 */
export function getProjectLibraryList(data) {
  return post(`${prefix}/projectLibrary/pageList`, data)
}

/**
 * 查询单个项目库详情信息
 */
export function getProjectLibraryDetails(id) {
  return get(`${prefix}/projectLibrary/details/${id}`)
}

/**
 * 更新项目设计单位和联合体单位信息
 * @param {*} data
 * @returns
 */
export function updateProjectUnit(data) {
  return post(`${prefix}/projectLibrary/modifyUnit`, data)
}

/**
 * 修改项目团队成员
 */
export function updateModifTeam(data, config = { transformRes: false }) {
  return post(`${prefix}/projectLibrary/modifyTeam`, data, config)
}

/**
 * 获取项目关联的子项目列表
 */
export function getChildProjectList(data) {
  return post(`${prefix}/projectLibrary/child/list`, data)
}

/**
 * 关联子项目
 */
export function connectSubProjects(data, config = { transformRes: false }) {
  return post(`${prefix}/projectLibrary/relation/subProjects`, data, config)
}

/**
 * 获取外包需求列表
 */
export function getOutsourceDemandList(data) {
  return get(`/outsource/demand/list`, data)
}
/**
 * 获取外包人员进场列表
 */
export function getOutsourceStaffList(data) {
  return get(`/outsource/staff/list`, data)
}

/**
 * 关联合同
 */
export function getPsmContractList(data) {
  return get(`purchase/psmContract/list`, data)
}

/**
 * 获取项目基线里程碑列表
 * @param {*} data
 * @returns
 */
export function getBaseMilestonesList(projectCode, data) {
  return get(`${prefix}/projectLibrary/details/milestone/${projectCode}`, data)
}

/**
 * 获取项目计划里程碑列表
 * @param {*} projectCode
 * @param {*} data
 * @returns
 */
export function getPlanMilestonesList(projectCode, data) {
  return get(`${prefix}/projectLibrary/details/milestone/plan/${projectCode}`, data)
}

// 详情-项目基本信息
export function getBaseProjectDetail(params) {
  return get('/finance/financial-data-summary/base-project-detail', params)
}

/**
 * 项目验收
 */
export function getPhasedAcceptanceList(data) {
  return post(`${prefix}/phasedAcceptance/pageList`, data)
}
