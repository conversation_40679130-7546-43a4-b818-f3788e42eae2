import request from '@/utils/request'

/**
 * 客商补录列表
 */
export function getCustomerMerchantPage(data) {
  return request({
    url: '/project/merchant/page',
    method: 'post',
    data,
  })
}

/**
 * 保存和更新客商信息和银行信息
 */
export function saveCustomerMerchant(data) {
  return request({
    url: '/project/merchant/fill',
    method: 'post',
    data,
  })
}

/**
 * 保存或更新银行信息
 */
export function saveBankInfo(data) {
  return request({
    url: '/project/merchant/saveOrUpdateBankInformation',
    method: 'post',
    data,
  })
}

/**
 * 根据id获取客商详情
 */
export function onSearchCustomerInfo(id) {
  return request({
    url: `/project/merchant/detail/${id}`,
    method: 'get',
  })
}

/**
 * 获取银行信息集合
 */
export function getBankInfo(params) {
  return request({
    url: `/project/merchant/getBankInformationList`,
    method: 'get',
    params,
  })
}

/**
 * 获取联系人集合
 */
export function getMessageInfo(params) {
  return request({
    url: `/project/merchant/getContactMessageList`,
    method: 'get',
    params,
  })
}
/**
 * 获取地址信息集合
 */
export function getAddressList(params) {
  return request({
    url: `/project/merchant/getAddressList`,
    method: 'get',
    params,
  })
}
/**
 * 撤回客商录入审批
 */
export function customerRecall(id) {
  return request({
    url: `/project/merchant/recall/${id}`,
    method: 'post',
  })
}
/**
 * 保存或更新联系人信息
 */
export function saveOrUpdateContactMessage(data) {
  return request({
    url: `/project/merchant/saveOrUpdateContactMessage`,
    method: 'post',
    data,
  })
}
/**
 * 保存或更新资质(地址)信息
 */
export function saveOrUpdateAddress(data) {
  return request({
    url: `/project/merchant/saveOrUpdateAddress`,
    method: 'post',
    data,
  })
}

/**
 * 按类型和主键值逻辑删除
 * @param {*} p1
 * @param {*} p2
 * @returns
 */
export function merchantDelete(p1, p2) {
  return request({
    url: `/project/merchant/delete/${p1}/${p2}`,
    method: 'post',
  })
}

/**
 * 获取获取与客户有关的商机库列表
 */
export function getBusinessLibraryList(data) {
  return request({
    url: `/project/business/library/list`,
    method: 'post',
    data,
  })
}

/**
 * 获取与客户有关的销售合同列表
 */
export function getContractList(params) {
  return request({
    url: `/purchase/psmContract/list`,
    method: 'get',
    params,
  })
}
