<template>
  <Container>
    <template #headerRight>
      <div class="flex align-center">
        <ElForm
          :model="formData"
          size="small"
          :inline="true">
          <ElFormItem
            style="margin: 0;"
            label="年度："
            prop="year">
            <ElDatePicker
              v-model="formData.year"
              type="year"
              placeholder="请选择年度"
              value-format="YYYY"
              format="YYYY"
              :clearable="false" />
          </ElFormItem>
          <ElDivider
            style="margin: 0 12px;"
            direction="vertical" />
          <ElFormItem
            style="margin: 0;"
            label="分析主体："
            prop="analysisSubject">
            <ElCascader
              v-if="isAdmin"
              v-model="formData.analysisSubject"
              :options="analysisSubjectOptions"
              :props="{
                expandTrigger: 'hover',
              }"
              :show-all-levels="false"
              clearable />
            <ElInput
              v-else
              v-model="userStore.deptName"
              disabled
              placeholder="请选择分析主体" />
          </ElFormItem>
        </ElForm>
      </div>
    </template>
    <ElRow :gutter="12">
      <ElCol
        style="margin-bottom: 12px;"
        :span="24"
        :lg="8">
        <ChartPanel
          title="客户企业性质"
          tooltip="该图表统计对应分析主体对应年度下的所有客户按照客户类型进行统计各类型的客户合同金额和占比份额，如需查看详情信息请点击统计图外即可打开。"
          :year="formData.year"
          @click="() => {
            clientNatureRef?.showDialog()
          }">
          <template #default="{ year }">
            <ClientNature
              ref="clientNatureRef"
              :year="year"
              :is-admin="isAdmin"
              :analysis-subject="formData.analysisSubject"
              :dept-id="userStore.deptId" />
          </template>
        </ChartPanel>
      </ElCol>
      <ElCol
        style="margin-bottom: 12px;"
        :span="24"
        :lg="8">
        <ChartPanel
          title="客户地区分类"
          tooltip="该图表统计对应分析主体对应年度下的所有客户按照客户地区进行统计各地区的客户合同金额和占比份额，如需查看详情信息请点击统计图外即可打开。"
          :year="formData.year"
          @click="() => {
            clientDistrictRef?.showDialog()
          }">
          <template #default="{ year }">
            <ClientDistrict
              ref="clientDistrictRef"
              :year="year"
              :analysis-subject="formData.analysisSubject"
              :is-admin="isAdmin"
              :dept-id="userStore.deptId" />
          </template>
        </ChartPanel>
      </ElCol>
      <ElCol
        style="margin-bottom: 12px;"
        :span="24"
        :lg="8">
        <ChartPanel
          title="客户合同额"
          tooltip="该图表统计对应分析主体对应年度下的所有客户统计其客户的合同金额和占比份额、平均合同额，如需查看详情信息请点击统计图外即可打开。"
          :year="formData.year"
          @click="() => {
            clientAmountRef?.showDialog()
          }">
          <template #default="{ year }">
            <ClientAmount
              ref="clientAmountRef"
              :year="year"
              :analysis-subject="formData.analysisSubject"
              :is-admin="isAdmin"
              :dept-id="userStore.deptId" />
          </template>
        </ChartPanel>
      </ElCol>
    </ElRow>
    <ElRow :gutter="12">
      <ElCol
        style="margin-bottom: 12px;"
        :span="24"
        :md="12">
        <ChartPanel
          title="客户历年回款"
          tooltip="该图表统计对应分析主体对应年度下的所有客户统计其客户的历史签订合同总额、已开票金额、已回款金额、回款比例、未回款金额，如需查看详情信息请点击统计图外即可打开。"
          :year="formData.year"
          @click="() => {
            clientPaymentRef?.showDialog()
          }">
          <template #default="{ year }">
            <ClientPayment
              ref="clientPaymentRef"
              :year="year"
              :analysis-subject="formData.analysisSubject"
              :is-admin="isAdmin"
              :dept-id="userStore.deptId" />
          </template>
        </ChartPanel>
      </ElCol>
      <ElCol
        style="margin-bottom: 12px;"
        :span="24"
        :md="12">
        <ChartPanel
          title="市场经理销售目标"
          tooltip="该图表统计对应分析主体对应年度下的所有市场经理销售目标的年度目标、季度目标以及年度、季度累计完成情况，如需查看详情信息请点击统计图外即可打开。"
          :year="formData.year"
          @click="() => {
            managerTargetRef?.showDialog()
          }">
          <template #default="{ year }">
            <ManagerTarget
              ref="managerTargetRef"
              :year="year"
              :analysis-subject="formData.analysisSubject"
              :is-admin="isAdmin"
              :dept-id="userStore.deptId" />
          </template>
        </ChartPanel>
      </ElCol>
    </ElRow>
  </Container>
</template>

<script setup>
import Container from '@/components/Container/index.vue'
import useUserStore from '@/store/modules/user.js'
import { marketEntityOptions } from '@/views/presales/business-activation/config.js'
import ChartPanel from './chart-panel.vue'
import ClientAmount from './client-amount.vue'
import ClientDistrict from './client-district.vue'
import ClientNature from './client-nature.vue'
import ClientPayment from './client-payment.vue'
import ManagerTarget from './manager-target.vue'

const userStore = useUserStore()

const isAdmin = computed(() => {
  return userStore.roles.includes('admin') || userStore.roles.includes('operateManage')
})

const formData = reactive({
  year: new Date().getFullYear().toString(),
  analysisSubject: [],
})

const analysisSubjectOptions = ref([...marketEntityOptions])

const clientNatureRef = useTemplateRef('clientNatureRef')
const clientDistrictRef = useTemplateRef('clientDistrictRef')
const clientAmountRef = useTemplateRef('clientAmountRef')
const clientPaymentRef = useTemplateRef('clientPaymentRef')
const managerTargetRef = useTemplateRef('managerTargetRef')
</script>

<style lang="scss" scoped>

</style>
