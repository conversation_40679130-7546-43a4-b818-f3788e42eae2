<template>
  <Title style="margin-top: 20px;">
    财务决算信息
  </Title>
  <ElDescriptions
    :column="4"
    label-width="180"
    border>
    <ElDescriptionsItem label="项目总成本预算（元）">
      {{ formData.budgetAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="外采成本预算（元）">
      {{ formData.budgetOutsourcingCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="人力成本预算（元）">
      {{ formData.budgetJobLaborCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目管理费预算（元）">
      {{ formData.budgetProjectManagerCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目总成本决算（元）">
      {{ formData.actualAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="外采成本决算（元）">
      {{ formData.actualOutsourcingCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="人力成本决算（元）">
      {{ formData.actualJobLaborCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目管理费决算（元）">
      {{ formData.actualProjectManagerCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目总成本偏离值">
      <template v-if="formData.amountVariance">
        {{ formData.amountVariance > 0 ? `+${formData.amountVariance}%` : `${formData.amountVariance}%` }}
      </template>
    </ElDescriptionsItem>
    <ElDescriptionsItem label="外采成本偏离值">
      <template v-if="formData.outsourcingCostVariance">
        {{ formData.outsourcingCostVariance > 0 ? `+${formData.outsourcingCostVariance}%` : `${formData.outsourcingCostVariance}%` }}
      </template>
    </ElDescriptionsItem>
    <ElDescriptionsItem label="人力成本偏离值">
      <template v-if="formData.jobLaborCostVariance">
        {{ formData.jobLaborCostVariance > 0 ? `+${formData.jobLaborCostVariance}%` : `${formData.jobLaborCostVariance}%` }}
      </template>
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目管理费偏离值">
      <template v-if="formData.projectManagerCostVariance">
        {{ formData.projectManagerCostVariance > 0 ? `+${formData.projectManagerCostVariance}%` : `${formData.projectManagerCostVariance}%` }}
      </template>
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="补充说明"
      :span="4">
      {{ formData.supplementaryInstruction }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="财务决算相关附件"
      :span="4">
      <template v-if="formData.financialActualAttachment">
        <div
          v-for="(item, index) in formData.financialActualAttachment"
          :key="index"
          style="display: flex; align-items: center;">
          <ElIcon>
            <Document />
          </ElIcon>
          <span style="padding: 2px 0 0 2px;">
            {{ item.match(/[^/\\?#]+$/)[0] }}
          </span>
          <ElButton
            link
            type="primary"
            style="margin-left: 8px;"
            @click="handleDownload(item)">
            下载
          </ElButton>
        </div>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const formData = defineModel()

// 自定义附件下载逻辑
function handleDownload(address) {
  const link = document.createElement('a')
  link.href = address
  // 设置下载文件名
  link.download = address.match(/[^/\\?#]+$/)[0]
  document.body.appendChild(link)
  link.click()
  // 清理 DOM
  document.body.removeChild(link)
}
</script>

<style lang="scss" scoped>

</style>
