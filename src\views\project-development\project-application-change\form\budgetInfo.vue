<template>
  <Title>项目预算信息(请在此对需要变更的部分进行修改)</Title>
  <ElRow :gutter="30">
    <ElCol :span="6">
      <ElFormItem label="预算金额（万元）">
        <ElInput
          v-model="form.baseInfo.projectChangeDraft.budgetAmount"
          disabled
          placeholder="填报后自动计算" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="外采成本（万元）">
        <ElInput
          v-model="form.baseInfo.projectChangeDraft.budgetOutsourcingCostAmount"
          disabled
          placeholder="填报后自动计算" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="人力成本（万元）">
        <ElInput
          v-model="form.baseInfo.projectChangeDraft.budgetJobLaborCostAmount"
          disabled
          placeholder="填报后自动计算" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目管理费（万元）">
        <ElInput
          v-model="form.baseInfo.projectChangeDraft.budgetProjectManagerCostAmount"
          disabled
          placeholder="填报后自动计算" />
      </ElFormItem>
    </ElCol>
  </ElRow>
  <ElTabs>
    <ElTabPane label="概览">
      <ElTable
        :data="data"
        border
        :span-method="objectSpanMethod">
        <ElTableColumn
          label="项目"
          prop="project" />
        <ElTableColumn
          label="分项"
          prop="itemsize" />
        <ElTableColumn
          label="含税金额（元）"
          prop="taxInclusiveAmount" />
        <ElTableColumn
          label="不含税金额（元）"
          prop="excludingTaxAmount" />
      </ElTable>
    </ElTabPane>
    <ElTabPane label="外采成本">
      <div style="float: right; margin-bottom: 10px">
        <ElButton
          type="primary"
          icon="plus"
          :disabled="form.baseInfo.isImportantModification !== '是'"
          @click="onOpen('wccbDialog')">
          新建
        </ElButton>
      </div>
      <ElTable
        border
        style="width: 100%;"
        :data="form.baseInfo.projectChangeDraft.budgetInfo.outsourcingCost || []"
        show-summary
        :summary-method="wccbSummaryMethod">
        <ElTableColumn
          v-for="(item, index) in wccbTableColumnInfo"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          align="center" />
        <ElTableColumn
          label="操作"
          prop="operate"
          align="center"
          fixed="right">
          <template #default="{ row }">
            <ElButton
              link
              type="danger"
              :disabled="form.baseInfo.isImportantModification !== '是'"
              @click="onDeleteDialog(row, 'wccb')">
              删除
            </ElButton>
            <ElButton
              link
              type="warning"
              :disabled="form.baseInfo.isImportantModification !== '是'"
              @click="onEditDialog(row, 'wccb')">
              编辑
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElTabPane>
    <ElTabPane label="人力成本">
      <div style="float: right; margin-bottom: 10px">
        <ElButton
          type="primary"
          icon="plus"
          :disabled="form.baseInfo.isImportantModification !== '是'"
          @click="onOpen('rlcbDialog')">
          新建
        </ElButton>
      </div>
      <ElTable
        border
        style="width: 100%;"
        :data="form.baseInfo.projectChangeDraft.budgetInfo.jobLaborCost || []"
        show-summary
        :summary-method="rlcbSummaryMethod">
        <ElTableColumn
          v-for="(item, index) in rlcbTableColumnInfo"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          align="center" />
        <ElTableColumn
          label="操作"
          align="center"
          fixed="right">
          <template #default="{ row }">
            <ElButton
              type="danger"
              link
              :disabled="form.baseInfo.isImportantModification !== '是'"
              @click="onDeleteDialog(row, 'rlcb')">
              删除
            </ElButton>
            <ElButton
              type="primary"
              link
              :disabled="form.baseInfo.isImportantModification !== '是'"
              @click="onEditDialog(row, 'rlcb')">
              编辑
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElTabPane>
    <ElTabPane label="项目管理费">
      <ElTable
        border
        style="width: 100%;"
        :data="form.baseInfo.projectChangeDraft.budgetInfo.manageCost || []"
        show-summary
        :summary-method="xmglfSummaryMethod">
        <ElTableColumn
          label="科目"
          prop="subject"
          align="center" />
        <ElTableColumn
          label="金额（元）"
          prop="amount"
          align="center">
          <template #default="{ row }">
            <ElInput
              v-if="row.isEdit"
              v-model.number="row.amount"
              type="number" />
            <span v-else>
              {{ row.amount }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="备注"
          prop="remark"
          align="center">
          <template #default="{ row }">
            <ElInput
              v-if="row.isEdit"
              v-model="row.remark" />
            <span v-else>
              {{ row.remark }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="操作"
          align="center"
          fixed="right">
          <template #default="{ row }">
            <ElButton
              v-if="!row.isEdit"
              :disabled="form.baseInfo.isImportantModification !== '是'"
              type="primary"
              link
              @click="row.isEdit = true">
              编辑
            </ElButton>
            <ElButton
              v-else
              type="success"
              link
              @click="row.isEdit = false">
              保存
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElTabPane>
  </ElTabs>
  <WccbDialog
    v-model="dialogOptions.wccbDialog"
    :info="dialogForm.wccbDialog"
    @confirm="onDialogConfirm"
    @cancel="resetDialogForm" />
  <RlcbDialog
    v-model="dialogOptions.rlcbDialog"
    :info="dialogForm.rlcbDialog"
    @confirm="onDialogConfirm"
    @cancel="resetDialogForm" />
  <!-- <ProductDialog
    v-model="dialogOptions.productDialog"
    @confirm="data => onConfirmProductForwccb(data)" /> -->
</template>

<script setup lang="jsx">
import Title from '@/components/Title/index.vue'
import { generateUniqueKey } from '@/utils/hooks.js'
import { toFixedAccurate } from '@/utils/math.js'
// import ProductDialog from '@/views/presales/pre-project/components/product-dialog.vue'
import RlcbDialog from '@/views/project-development/project-application/form/components/rlcb-dialog.vue'
import WccbDialog from '@/views/project-development/project-application/form/components/wccb-dialog.vue'
import { ElMessageBox } from 'element-plus'

const form = defineModel()

const { proxy } = getCurrentInstance()
const { project_subject } = proxy.useDict('project_subject')

const data = computed(() => {
  let data = [
    { project: '外采成本', itemsize: '外采成本', taxInclusiveAmount: form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.outsourcingCostSumTaxAmount || 0, excludingTaxAmount: form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.outsourcingCostSumNotTaxAmount || 0 },
    { project: '人力成本', itemsize: '自有人力成本', taxInclusiveAmount: form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.ownPersonnelCostTaxAmount || 0, excludingTaxAmount: '/' },
    { project: '人力成本', itemsize: '外包人力成本', taxInclusiveAmount: form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.outsourcedStaffCostTaxAmount || 0, excludingTaxAmount: '/' },
  ]
  if (project_subject.value && project_subject.value.length > 0) {
    const project_subject_data = form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.map(item => ({ project: '项目管理费', itemsize: item.subject, taxInclusiveAmount: item.amount, excludingTaxAmount: '/' }))
    data = [...data, ...project_subject_data]
  }
  return data
})

const wccbTableColumnInfo = [
  {
    label: '标包',
    prop: 'standardPackage',
  },
  {
    label: '子项科目',
    prop: 'subitemSubject',
  },
  {
    label: '采购名称',
    prop: 'procurementName',
  },
  {
    label: '技术规格、型号或采购内容说明',
    prop: 'specificationsModels',
  },
  {
    label: '单位',
    prop: 'unit',
  },
  {
    label: '数量',
    prop: 'quantity',
  },
  {
    label: '销售单价（元）',
    prop: 'unitSellingPrice',
  },
  // {
  //   label: '折扣',
  //   prop: 'discountRate',
  // },
  // {
  //   label: '折扣后销售单价（元）',
  //   prop: 'discountedPrice',
  // },
  {
    label: '预计采购单价（含税：元）',
    prop: 'taxPredictPurchasePrice',
  },
  {
    label: '预计采购总价（含税：元）',
    prop: 'taxPredictPurchaseTotalPrice',
  },
  {
    label: '税率%',
    prop: 'taxRate',
  },
  {
    label: '预计采购单价（不含税：元）',
    prop: 'notTaxPredictPurchasePrice',
  },
  {
    label: '预计采购总价（不含税：元）',
    prop: 'notTaxPredictPurchaseTotalPrice',
  },
  {
    label: '预计采购日期',
    prop: 'predictDate',
  },
]

const rlcbTableColumnInfo = [
  {
    label: '团队角色',
    prop: 'teamRole',
  },
  {
    label: '人员类型',
    prop: 'personType',
  },
  {
    label: '人数',
    prop: 'personNumber',
  },
  {
    label: '人员投入估算（人天）',
    prop: 'estimateHumanDays',
  },
  {
    label: '单价（元/人天）',
    prop: 'unitPrice',
  },
  {
    label: '总价（元）',
    prop: 'totalPrice',
  },
  {
    label: '备注',
    prop: 'remark',
  },
]

watchEffect(() => {
  // 当数据为空时说明可能详情数据没有请求回来或者请求回来为空，此时可以初始化项目管理费表格
  if (form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.length <= 0) {
    form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost = project_subject.value.map(item => ({ subject: item.value, remark: '', amount: 0, uuid: generateUniqueKey() }))
  }
})

function objectSpanMethod({ rowIndex, columnIndex }) {
  if (columnIndex === 0) {
    switch (rowIndex) {
      case 0: return [1, 1] // 第1行不向下也不向右合并
      case 1: return [2, 1] // 第2行向下合并1行，不向右合并
      case 3: return [data.value.length - 1, 1] // 第4行向下合并项目管理费所配置的字典length-1行，不向右合并
      default: return [0, 0] // 第3行被第2行合并，第4行以后的数据被第4行向下所合并，所以需要隐藏
    }
  }
}

function wccbSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map(item => Number(item[column.property]))
    if (column.property === 'taxPredictPurchaseTotalPrice' || column.property === 'notTaxPredictPurchaseTotalPrice') {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
    }
  })
  // 这里将计算后的外采成本同步到表单数据
  form.value.baseInfo.projectChangeDraft.budgetOutsourcingCostAmount = toFixedAccurate(sums[8] / 10000, 6)
  form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.outsourcingCostSumTaxAmount = sums[8]
  form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.outsourcingCostSumNotTaxAmount = sums[11]
  return sums
}

const dialogOptions = ref({
  wccbDialog: false,
  productDialog: false,
  rlcbDialog: false,
})

const dialogForm = ref({
  wccbDialog: {},
  rlcbDialog: {},
})

function onOpen(type, operate) {
  if (operate === undefined) {
    dialogForm.value[type] = {}
  }
  dialogOptions.value[type] = true
}

function onDialogConfirm({ formData, dialogType }) {
  // 修改
  if (formData?.id || formData?.uuid) {
    // 外采成本
    if (dialogType === 'wccb') {
      const index = form.value.baseInfo.projectChangeDraft.budgetInfo.outsourcingCost.findIndex((item) => {
        if (item.uuid) {
          return item.uuid === formData.uuid
        }
        if (item.id) {
          return item.id === formData.id
        }
        return false
      })
      form.value.baseInfo.projectChangeDraft.budgetInfo.outsourcingCost.splice(
        index,
        1,
        formData,
      )
    }
    // 人力成本
    if (dialogType === 'rlcb') {
      const index = form.value.baseInfo.projectChangeDraft.budgetInfo.jobLaborCost.findIndex((item) => {
        if (item.uuid) {
          return item.uuid === formData.uuid
        }
        if (item.id) {
          return item.id === formData.id
        }
        return false
      })
      form.value.baseInfo.projectChangeDraft.budgetInfo.jobLaborCost.splice(
        index,
        1,
        formData,
      )
    }
    // 项目管理费
    if (dialogType === 'xmglf') {
      const index = form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.findIndex((item) => {
        if (item.uuid) {
          return item.uuid === formData.uuid
        }
        if (item.id) {
          return item.id === formData.id
        }
        return false
      })
      form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.splice(
        index,
        1,
        formData,
      )
      // 修改了项目管理费，同步更新概览
    }
    resetDialogForm()
  } else {
    // 外采成本
    if (dialogType === 'wccb') {
      form.value.baseInfo.projectChangeDraft.budgetInfo.outsourcingCost.push({
        ...formData,
        uuid: generateUniqueKey(),
      })
    }
    // 人力成本
    if (dialogType === 'rlcb') {
      form.value.baseInfo.projectChangeDraft.budgetInfo.jobLaborCost.push({
        ...formData,
        uuid: generateUniqueKey(),
      })
    }
    // 项目管理费
    if (dialogType === 'xmglf') {
      form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.push({
        ...formData,
        uuid: generateUniqueKey(),
      })
    }
  }
}

function resetDialogForm() {
  Object.keys(dialogForm.value).forEach((key) => {
    dialogForm.value[key] = {}
  })
}

function onEditDialog(row, type) {
  dialogForm.value[`${type}Dialog`] = row
  onOpen(`${type}Dialog`, 'edit')
}

function onDeleteDialog(formData, type) {
  ElMessageBox({
    title: '警告',
    type: 'warning',
    message: '确定要删除该条记录吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        // 外采成本
        if (type === 'wccb') {
          const index = form.value.baseInfo.projectChangeDraft.budgetInfo.outsourcingCost.findIndex((item) => {
            if (item.uuid) {
              return item.uuid === formData.uuid
            }
            if (item.id) {
              return item.id === formData.id
            }
            return false
          })
          form.value.baseInfo.budgetInfo.projectChangeDraft.outsourcingCost.splice(
            index,
            1,
          )
        }
        // 人力成本
        if (type === 'rlcb') {
          const index = form.value.baseInfo.projectChangeDraft.budgetInfo.jobLaborCost.findIndex((item) => {
            if (item.uuid) {
              return item.uuid === formData.uuid
            }
            if (item.id) {
              return item.id === formData.id
            }
            return false
          })
          form.value.baseInfo.projectChangeDraft.budgetInfo.jobLaborCost.splice(
            index,
            1,
          )
        }
        // 项目管理费
        if (type === 'xmglf') {
          const index = form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.findIndex((item) => {
            if (item.uuid) {
              return item.uuid === formData.uuid
            }
            if (item.id) {
              return item.id === formData.id
            }
            return false
          })
          form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.splice(
            index,
            1,
          )
        }
        done()
      } else {
        done()
      }
    },
  })
}

// function onConfirmProductForwccb(data) {
//   form.value.baseInfo.projectChangeDraft.budgetInfo.outsourcingCost = data.map(item => ({ procurementName: item.solutionName, unitSellingPrice: item.productPackage, unit: item.unit, uuid: generateUniqueKey() }))
// }

function rlcbSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      // 通过<br/>实现换行，达到2行的效果,
      // 注意需要安装jsx依赖，并在script标签声明lang="jsx"
      sums[index] = (
        <p>
          汇总
          <br />
          <br />
          <br />
          <br />
          合计
        </p>
      )
    }
    if (column.property === 'personType') {
      sums[index] = (
        <p>
          自有
          <br />
          <br />
          外包
          <br />
          <br />
          <br />
        </p>
      )
    }
    if (column.property === 'personNumber') {
      // 自有人数
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.personNumber)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.personNumber)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
    }

    if (column.property === 'estimateHumanDays') {
      // 自有人数
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.estimateHumanDays)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.estimateHumanDays)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
    }

    if (column.property === 'unitPrice') {
      // 自有人数
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.unitPrice)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.unitPrice)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
    }

    if (column.property === 'totalPrice') {
      // 自有人数总价
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.totalPrice)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数总价
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.totalPrice)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
      // 这里将计算的人力成本合计同步到表单数据中
      form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.ownPersonnelCostTaxAmount = self
      form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.outsourcedStaffCostTaxAmount = out
      form.value.baseInfo.projectChangeDraft.budgetJobLaborCostAmount = toFixedAccurate((self + out) / 10000, 6)
    }
  })
  return sums
}

function xmglfSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map(item => Number(item[column.property]))
    if (column.property === 'amount') {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
    }
  })
  // 这里将计算的项目管理费合计同步到表单数据中
  form.value.baseInfo.projectChangeDraft.budgetProjectManagerCostAmount = toFixedAccurate(sums[1] / 10000, 6) // 保留6位小数
  form.value.baseInfo.projectChangeDraft.budgetInfo.budgetOverview.manageCost = form.value.baseInfo.projectChangeDraft.budgetInfo.manageCost.map(item =>
    ({ costName: item.subject, taxAmount: item.amount }),
  )
  return sums
}

watchEffect(() => {
  form.value.baseInfo.projectChangeDraft.budgetAmount = (form.value.baseInfo.projectChangeDraft.budgetOutsourcingCostAmount * 1000000 + form.value.baseInfo.projectChangeDraft.budgetJobLaborCostAmount * 1000000 + form.value.baseInfo.projectChangeDraft.budgetProjectManagerCostAmount * 1000000) / 1000000
})
</script>

<style lang="scss" scoped></style>
