import { get, post } from '@/utils/alova.js'

/**
 * 获取项目筛选列表
 */
export function getAllProjectName(params, config) {
  return get('/workhour/statisticalDashboard/getAllProjectName', params, config)
}

/**
 * 获取项目工时和成本统计
 */
export function getProjectManhourAndCost(data, config) {
  return post('/workhour/statisticalDashboard/projectCount', data, config)
}

/**
 * 获取项目人效比统计
 */
export function getProjectEffectivenessRatio(data, config) {
  return post('/workhour/statisticalDashboard/projectEfficiencyRatio', data, config)
}

/**
 * 获取部门工时和成本统计
 */
export function getDepartmentManhourAndCost(data, config) {
  return post('/workhour/statisticalDashboard/deptCount', data, config)
}

/**
 * 获取人员工时和成本统计
 */
export function getPersonManhourAndCost(data, config) {
  return post('/workhour/statisticalDashboard/userCount', data, config)
}
