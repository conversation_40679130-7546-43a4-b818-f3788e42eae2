<template>
  <ElDialog
    v-model="visible"
    title="月度薪酬导入"
    width="30%"
    @close="handleClose">
    <ElForm
      ref="importRef"
      :model="importForm"
      :rules="rules"
      label-position="top">
      <ElFormItem
        label="数据名称:"
        prop="dataName">
        <ElInput
          v-model="importForm.dataName"
          placeholder="请输入" />
      </ElFormItem>
      <ElFormItem
        label="导入薪酬月份:"
        prop="importMonth">
        <ElDatePicker
          v-model="importForm.importMonth"
          style="width: 100%;"
          type="month"
          value-format="YYYY-MM-DD"
          placeholder="请选择月份" />
      </ElFormItem>
      <ElFormItem
        label="员工月薪数据导入:"
        prop="monthlySalaryFile">
        <FileUpload
          ref="salaryFileRef"
          type="monthlySalaryFile"
          @handle-change="handleChange"
          @handle-file-remove="handleFileRemove"
          @upload-files="uploadFiles" />
      </ElFormItem>
      <ElFormItem
        label="员工月社保数据导入:"
        prop="monthlySocialSecurityFile">
        <FileUpload
          ref="socialSecurityFileRef"
          type="monthlySocialSecurityFile"
          @handle-change="handleChange"
          @handle-file-remove="handleFileRemove"
          @upload-files="uploadFiles" />
      </ElFormItem>
      <ElFormItem
        label="员工公积金数据导入:"
        prop="monthlyAccumulationFundFile">
        <FileUpload
          ref="accumulationFundFileRef"
          type="monthlyAccumulationFundFile"
          @handle-change="handleChange"
          @handle-file-remove="handleFileRemove"
          @upload-files="uploadFiles" />
      </ElFormItem>
      <ElFormItem
        label="员工月年金数据导入:"
        prop="monthlyAnnuityFile">
        <FileUpload
          ref="annuityFileRef"
          type="monthlyAnnuityFile"
          @handle-change="handleChange"
          @handle-file-remove="handleFileRemove"
          @upload-files="uploadFiles" />
      </ElFormItem>
    </ElForm>
    <div class="action">
      <ElButton
        type="primary"
        @click="handleSubmit">
        确认导入
      </ElButton>
    </div>
  </ElDialog>
</template>

<script setup>
import * as salaryApi from '@/api/man-hour/salary.js'
import { ElMessage } from 'element-plus'
import FileUpload from './file-upload.vue'

const emits = defineEmits(['reload'])
const importForm = ref({
  dataName: '',
  importMonth: '',
  monthlySalaryFile: null,
  monthlySocialSecurityFile: null,
  monthlyAccumulationFundFile: null,
  monthlyAnnuityFile: null,
})
// 解析的文件数据
const fileData = ref({
  monthlySalaryList: [],
  monthlySocialSecurityList: [],
  monthlyAccumulationFundList: [],
  monthlyAnnuityList: [],
})
const importRef = ref(null)
const salaryFileRef = ref(null)
const socialSecurityFileRef = ref(null)
const accumulationFundFileRef = ref(null)
const annuityFileRef = ref(null)
const visible = defineModel('visible')

// 文件移除处理
function handleFileRemove(key) {
  importForm.value[key] = null
}
// 文件变化处理
function handleChange(key, value) {
  importForm.value[key] = value
}
// 关闭对话框处理
function handleClose() {
  importRef.value.resetFields()
  salaryFileRef.value.handleReset()
  socialSecurityFileRef.value.handleReset()
  accumulationFundFileRef.value.handleReset()
  annuityFileRef.value.handleReset()
  visible.value = false
}
// 导入文件配置
const fileConfigMap = [
  { key: 'monthlySalaryFile', file: 'monthlySalaryFile', fileData: 'monthlySalaryList', uploadApi: 'uploadSalaryFile' },
  { key: 'monthlySocialSecurityFile', file: 'monthlySocialSecurityFile', fileData: 'monthlySocialSecurityList', uploadApi: 'uploadSocialSecurityFile' },
  { key: 'monthlyAccumulationFundFile', file: 'monthlyAccumulationFundFile', fileData: 'monthlyAccumulationFundList', uploadApi: 'uploadAccumulationFundFile' },
  { key: 'monthlyAnnuityFile', file: 'monthlyAnnuityFile', fileData: 'monthlyAnnuityList', uploadApi: 'uploadAnnuityFile' },
]
// 解析导入文件
async function uploadFiles(key) {
  if (!key)
    return
  try {
    const config = fileConfigMap.find(item => item.key === key)
    const formData = new FormData()
    formData.set('file', importForm.value[key])
    const res = await salaryApi[config.uploadApi](formData)
    if (res.code === 200) {
      fileData.value[config.fileData] = res.data
    } else {
      ElMessage.error('文件解析失败,请重新上传')
    }
  } catch (error) {
    console.log(error)
  }
}
// 导入全部薪酬数据
async function importSalaryFile() {
  try {
    const res = await salaryApi.importSalaryFile({
      dataName: importForm.value.dataName,
      importMonth: importForm.value.importMonth,
      annuities: fileData.value.monthlyAnnuityList,
      housingFunds: fileData.value.monthlyAccumulationFundList,
      salaryDetails: fileData.value.monthlySalaryList,
      socialSecurities: fileData.value.monthlySocialSecurityList,
    })
    if (res.code === 200) {
      ElMessage.success('数据导入成功')
      visible.value = false
      emits('reload')
    } else {
      ElMessage.error('操作失败，请重试')
    }
  } catch (error) {
    console.log(error)
  }
}

// 确认导入
async function handleSubmit() {
  importSalaryFile()
}

const rules = reactive({
  dataName: [
    { required: true, message: '请输入数据名称', trigger: 'change' },
  ],
  importMonth: [
    { required: true, message: '请选择薪酬月份', trigger: 'change' },
  ],
  monthlySalaryFile: [
    { required: true, message: '请上传月薪文件', trigger: 'change' },
  ],
  monthlySocialSecurityFile: [
    { required: true, message: '请上传社保文件', trigger: 'change' },
  ],
  monthlyAccumulationFundFile: [
    { required: true, message: '请上传公积金文件', trigger: 'change' },
  ],
  monthlyAnnuityFile: [
    { required: true, message: '请上传年金文件', trigger: 'change' },
  ],
})
</script>

<style scoped lang="scss">
.action {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
