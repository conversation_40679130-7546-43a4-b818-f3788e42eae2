import request from '@/utils/request'

// 新增、编辑楚天云服务支付流水
export function createOrUpdateCtCloudServicePaymentLedger(data) {
  return request({
    url: '/cloudorder/ct-cloud-service-payment-ledger/createOrUpdate',
    method: 'post',
    data,
  })
}

// 删除楚天云服务支付流水
export function deleteCtCloudServicePaymentLedger(params) {
  return request({
    url: '/cloudorder/ct-cloud-service-payment-ledger/del',
    method: 'get',
    params,
  })
}

// 楚天云服务支付流水详情
export function getCtCloudServicePaymentLedgerDetail(params) {
  return request({
    url: '/cloudorder/ct-cloud-service-payment-ledger/detail',
    method: 'get',
    params,
  })
}

// 下载服务付款台账
export function downloadCtCloudServicePaymentLedger(params) {
  return request({
    url: '/cloudorder/ct-cloud-service-payment-ledger/download',
    method: 'get',
    params,
  })
}

// 楚天云服务支付流水分页列表
export function getCtCloudServicePaymentLedgerPageList(data) {
  return request({
    url: '/cloudorder/ct-cloud-service-payment-ledger/pageList',
    method: 'post',
    data,
  })
}

// 根据结算单主键id查 服务付款台账详情
export function getCtCloudServicePaymentLedgerDetailByStatementId(statementId) {
  return request({
    url: '/cloudorder/ct-cloud-service-payment-ledger/detail-by-statementId',
    method: 'get',
    params: { statementId },
  })
}
