import request from '@/utils/request'
// 分页接口
export function getCustomerMerchantPage(data) {
  return request({
    url: '/system/checkList/page',
    method: 'post',
    data,
  })
}

//   删除检查表模板
export function delchecklist(bid) {
  return request({
    url: `/system/checkList/deleteTemplate/${bid}`,
    method: 'post',
  })
}

//   检查表模板详情
export function checklistsort(bid) {
  return request({
    url: `/system/checkList/detail/${bid}`,
    method: 'post',
  })
}

// 保存检查表模板
export function checklisttempsave(data) {
  return request({
    url: '/system/checkList/saveTemplate',
    method: 'post',
    data,
  })
}

// 更新检查表模板
export function checklisttempupdate(data) {
  return request({
    url: '/system/checkList/updateTemplate',
    method: 'post',
    data,
  })
}
