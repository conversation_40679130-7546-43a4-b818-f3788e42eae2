<template>
  <DefaultContainer v-loading="loading">
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          楚天云服务付款台账
        </div>
      </div>
      <div
        v-if="!props.id"
        class="right">
        <ElButton @click="router.back()">
          取消
        </ElButton>
        <ElButton
          :loading="saveLoading"
          type="primary"
          @click="onSubmit()">
          提交
        </ElButton>
      </div>
    </div>
    <div class="content">
      <ElForm
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="200px"
        :disabled="props.id">
        <ElDivider />
        <ElFormItem
          class="form-item"
          style="width: 95%"
          label="关联结算单编号"
          prop="statementId">
          <ElSelect
            v-model="form.statementName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.settlementDocSelectModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="结算年度"
          prop="year">
          <ElInput
            v-model="form.year"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单部门"
          prop="deptName">
          <ElInput
            v-model="form.deptName"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="应用系统名称"
          prop="ctAppName">
          <ElInput
            v-model="form.ctAppName"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="客户名称"
          prop="customerName">
          <ElInput
            v-model="form.customerName"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="项目名称"
          prop="projectName">
          <ElInput
            v-model="form.projectName"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="项目编号"
          prop="projectCode">
          <ElInput
            v-model="form.projectCode"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="数产项目经理"
          prop="projectManagerName">
          <ElInput
            v-model="form.projectManagerName"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="市场经理"
          prop="marketManagerName">
          <ElInput
            v-model="form.marketManagerName"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="预算有效周期(天)"
          prop="budgetEffectivePeriod">
          <ElInput
            v-model="form.budgetEffectivePeriod"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="项目云预算金额(元)"
          prop="budgetAmount">
          <ElInput
            v-model="form.budgetAmount"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="销售合同名称"
          prop="saleContractName">
          <ElInput
            v-model="form.saleContractName"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="销售合同编号"
          prop="saleContractNumber">
          <ElInput
            v-model="form.saleContractNumber"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同金额(元)"
          prop="contractAmount">
          <ElInputNumber
            v-model="form.contractAmount"
            controls-position="right"
            :precision="2"
            style="width: 100%"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同中的云服务金额(元)"
          prop="contractServiceAmount">
          <ElInput
            v-model="form.contractServiceAmount"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同中的云服务开始日期"
          prop="contractServiceStartTime">
          <ElInput
            v-model="form.contractServiceStartTime"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同中的云服务结束日期"
          prop="contractServiceEndTime">
          <ElInput
            v-model="form.contractServiceEndTime"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同中的云服务履约周期(天)"
          prop="contractServicePerformanceCycle">
          <ElInput
            v-model="form.contractServicePerformanceCycle"
            disabled
            placeholder="选择结账单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同价格类型"
          prop="priceType">
          <ElSelect
            v-model="form.priceType"
            placeholder="请选择">
            <ElOption
              label="新签"
              value="新签" />
            <ElOption
              label="调配"
              value="调配" />
            <ElOption
              label="资源回收"
              value="资源回收" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同合计拟付款金额(元)"
          prop="totalPaymentAmount">
          <ElInputNumber
            v-model="form.totalPaymentAmount"
            controls-position="right"
            :disabled="true"
            :precision="2"
            style="width: 100%"
            placeholder="自动计算" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同合计拟回款金额(元)"
          prop="totalCollectionAmount">
          <ElInputNumber
            v-model="form.totalCollectionAmount"
            controls-position="right"
            :disabled="true"
            :precision="2"
            style="width: 100%"
            placeholder="选择结算单后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="剩余现金流(元)"
          prop="cashFlowAmount">
          <ElInputNumber
            v-model="form.cashFlowAmount"
            controls-position="right"
            style="width: 100%"
            disabled
            placeholder="自动计算" />
        </ElFormItem>
        <div class="sub-title">
          <span style="color: red;">*</span>付款明细
        </div>
        <ElButton
          style="margin-bottom: 10px"
          type="primary"
          @click="addPaymentDetails">
          <ElIcon>
            <Plus />
          </ElIcon>
          新增
        </ElButton>
        <ElTable
          :data="form.ctCloudExpensesDetailList"
          border
          style="width: 95%; margin-bottom: 10px">
          <ElTableColumn
            prop="paymentDate"
            label="付款日期"
            min-width="120px">
            <template #header>
              <span style="color: red;">*</span>付款日期
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="amount"
            label="付款金额(元)"
            min-width="120px">
            <template #header>
              <span style="color: red;">*</span>付款金额(元)
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="operatorName"
            label="付款人"
            min-width="120px">
            <template #header>
              <span style="color: red;">*</span>付款人
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="status"
            label="付款状态"
            min-width="120px">
            <template #header>
              <span style="color: red;">*</span>付款状态
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="remark"
            label="备注"
            min-width="120px" />
          <ElTableColumn
            label="操作"
            width="120px">
            <template #default="scope">
              <ElButton
                link
                type="primary"
                @click="editPaymentDetails(scope.row, scope.$index)">
                编辑
              </ElButton>
              <ElPopconfirm
                title="确定删除吗？"
                @confirm="deletePaymentDetails(scope.row, scope.$index)">
                <template #reference>
                  <ElButton
                    link
                    type="danger">
                    删除
                  </ElButton>
                </template>
              </ElPopconfirm>
            </template>
          </ElTableColumn>
        </ElTable>
        <ElFormItem
          class="form-item"
          label="备注"
          style="width: 95%"
          prop="remark">
          <ElInput
            v-model="form.remark"
            :disabled="id !== ''"
            type="textarea"
            :rows="4"
            placeholder="请输入" />
        </ElFormItem>
      </ElForm>
      <SettlementDocSelectModal
        v-model="modelIsShow.settlementDocSelectModal"
        @select-item="onSelectSettlementDoc" />
      <PaymentModal
        ref="paymentModalRef"
        @add="onAddPaymentDetails"
        @edit="onEditPaymentDetails" />
    </div>
  </DefaultContainer>
</template>

<script setup>
import {
  createOrUpdateCtCloudServicePaymentLedger,
  getCtCloudServicePaymentLedgerDetail,
  getCtCloudServicePaymentLedgerDetailByStatementId,
} from '@/api/ctyOrderManagement/paymentLedger.js'

import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { deepClone } from '@/utils'
import SettlementDocSelectModal from '@/views/ctyOrderManagement/modules/SettlementDocSelectModal.vue'
import { Back } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import PaymentModal from './PaymentModal.vue'

const props = defineProps({
  id: {
    type: String,
  },
  instanceId: {
    type: String,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  forms: {
    type: Object,
    default: () => ({}),
  },
})

const router = useRouter()
const formRef = ref(null)
const form = ref({
  ctCloudExpensesDetailList: [],
})

const rules = ref({
  statementId: [
    { required: true, message: '请选择结算单', trigger: 'blur' },
  ],
  ctCloudExpensesDetailList: [
    { required: true, message: '请输入付款明细', trigger: 'blur' },
  ],
  year: [
    { required: true, message: '请输入结算年度', trigger: 'blur' },
  ],
  deptName: [
    { required: true, message: '请输入订单部门', trigger: 'blur' },
  ],
  ctAppName: [
    { required: true, message: '请输入应用系统名称', trigger: 'blur' },
  ],
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
  ],
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
  ],
  projectCode: [
    { required: true, message: '请输入项目编号', trigger: 'blur' },
  ],
  projectManagerName: [
    { required: true, message: '请输入数产项目经理', trigger: 'blur' },
  ],
  marketManagerName: [
    { required: true, message: '请输入市场经理', trigger: 'blur' },
  ],
  saleContractName: [
    { required: true, message: '请输入销售合同名称', trigger: 'blur' },
  ],
  saleContractNumber: [
    { required: true, message: '请输入销售合同编号', trigger: 'blur' },
  ],
  contractAmount: [
    { required: true, message: '请输入合同金额', trigger: 'blur' },
  ],
  contractServiceAmount: [
    { required: true, message: '请输入合同中的云服务金额', trigger: 'blur' },
  ],
  contractServiceStartTime: [
    { required: true, message: '请输入合同中的云服务开始日期', trigger: 'blur' },
  ],
  contractServiceEndTime: [
    { required: true, message: '请输入合同中的云服务结束日期', trigger: 'blur' },
  ],
  contractServicePerformanceCycle: [
    { required: true, message: '请输入合同中的云服务履约周期', trigger: 'blur' },
  ],
})

const id = ref('')
const loading = ref(false)
const saveLoading = ref(false)
const modelIsShow = ref({
  settlementDocSelectModal: false,
})

onMounted(() => {
  if (props.id || router.currentRoute.value.query.id) {
    id.value = props.id || router.currentRoute.value.query.id
    getFormData()
  }
})
watch(() => [form.value.totalPaymentAmount, form.value.totalCollectionAmount], () => {
  form.value.cashFlowAmount = form.value.totalCollectionAmount - form.value.totalPaymentAmount
})

watch(() => form.value.ctCloudExpensesDetailList, () => {
  form.value.totalPaymentAmount = form.value.ctCloudExpensesDetailList.reduce((acc, item) => {
    if (item.status === '已付款') {
      return acc + item.amount
    }
    return acc
  }, 0)
}, { deep: true })

async function getFormData() {
  loading.value = true
  try {
    const res = await getCtCloudServicePaymentLedgerDetail({
      id: id.value,
    })
    console.log(res)
    form.value = deepClone(res.data)
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

const paymentModalRef = ref(null)
function addPaymentDetails() {
  console.log('addPaymentDetails')
  paymentModalRef.value.add()
}
function editPaymentDetails(row, index) {
  row.index = index
  paymentModalRef.value.edit(row)
}

function onAddPaymentDetails(row) {
  console.log(row, '--- onAddPaymentDetails')
  form.value.ctCloudExpensesDetailList.push(row)
}

function onEditPaymentDetails(row) {
  console.log(row, '--- onEditPaymentDetails')
  if (row.index !== undefined) {
    form.value.ctCloudExpensesDetailList[row.index] = row
  }
}

function deletePaymentDetails(row, index) {
  console.log(row, '--- deletePaymentDetails')
  form.value.ctCloudExpensesDetailList.splice(index, 1)
}

async function onSelectSettlementDoc(item) {
  console.log(item, '--- onSelectSettlementDoc')
  // getCtCloudServicePaymentLedgerDetailByStatementId
  const res = await getCtCloudServicePaymentLedgerDetailByStatementId(item.id)
  console.log(res, '--- res')
  if (res.code === 200 && res.data) {
    id.value = res.data.id
    form.value = deepClone(res.data)
    if (!res.data.statementName) {
      form.value.statementName = item.statementName
    }
  } else {
    form.value.statementId = item.id
    form.value.statementName = item.statementName
    form.value.year = item.year
    form.value.deptName = item.deptName
    form.value.ctAppName = item.ctAppName
    form.value.customerName = item.customerName
    form.value.projectName = item.projectName
    form.value.projectCode = item.projectCode
    form.value.projectManagerName = item.projectManagerName
    form.value.marketManagerName = item.marketManagerName
    form.value.budgetEffectivePeriod = item.budgetEffectivePeriod
    form.value.budgetAmount = item.budgetAmount
    form.value.saleContractName = item.saleContractName
    form.value.saleContractNumber = item.saleContractNumber
    form.value.contractServiceAmount = item.contractServiceAmount
    form.value.contractServiceStartTime = item.contractServiceStartTime
    form.value.contractServiceEndTime = item.contractServiceEndTime
    form.value.contractServicePerformanceCycle = item.contractServicePerformanceCycle
    form.value.totalCollectionAmount = item.collectionAmount
  }
}

function onSubmit() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const data = deepClone(form.value)

      console.log(data, '--- data')
      saveLoading.value = true
      try {
        const res = await createOrUpdateCtCloudServicePaymentLedger(data)
        if (res.code === 200) {
          ElMessage.success('提交成功')
          router.back()
        }
      } catch (error) {
        console.log(error)
      } finally {
        saveLoading.value = false
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important; /* 设置最小高度 */

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .plan-item {
    width: 100%;

    .form-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 10px;
    margin-bottom: 40px;
    border: 1px dashed #1677ff;
    border-radius: 6px;
    color: #1677ff;
    font-size: 14px;
    cursor: pointer;
  }

  .form-item {
    width: 360px;
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 12px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}
</style>

<style lang="scss">
.custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
