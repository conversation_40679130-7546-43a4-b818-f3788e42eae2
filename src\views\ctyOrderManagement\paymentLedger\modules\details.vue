<template>
  <DefaultContainer>
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          楚天云服务付款台账详情
        </div>
      </div>
    </div>
    <div class="content">
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="200">
        <ElDescriptionsItem
          v-for="item in descriptionList"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
      </ElDescriptions>
      <div class="sub-title">
        付款明细
      </div>
      <ElTable
        :data="detail.ctCloudExpensesDetailList"
        border
        style="width: 100%">
        <ElTableColumn
          prop="paymentDate"
          label="付款日期"
          min-width="120px" />
        <ElTableColumn
          prop="amount"
          label="付款金额(元)"
          min-width="120px" />
        <ElTableColumn
          prop="payerName"
          label="付款人"
          min-width="120px" />
        <ElTableColumn
          prop="status"
          label="付款状态"
          min-width="120px" />
        <ElTableColumn
          prop="remark"
          label="备注"
          min-width="120px" />
      </ElTable>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="200">
        <ElDescriptionsItem
          :span="3"
          width="398">
          <template #label>
            <div>备注</div>
          </template>
          {{ detail.remark }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </DefaultContainer>
</template>

<script setup>
import {
  getCtCloudServicePaymentLedgerDetail,
} from '@/api/ctyOrderManagement/paymentLedger.js'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { Back } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
})

const router = useRouter()
const descriptionList = [
  { label: '关联结算单编号', value: 'statementName', span: 1 },
  { label: '结算年度', value: 'year', span: 1 },
  { label: '订单部门', value: 'deptName', span: 1 },
  { label: '应用系统名称', value: 'ctAppName', span: 3 },
  { label: '客户名称', value: 'customerName', span: 1 },
  { label: '项目名称', value: 'projectName', span: 1 },
  { label: '项目编号', value: 'projectCode', span: 1 },
  { label: '数产项目经理', value: 'projectManagerName', span: 1 },
  { label: '市场经理', value: 'marketManagerName', span: 1 },
  { label: '预算有效周期(天)', value: 'budgetEffectivePeriod', span: 1 },
  { label: '项目云预算金额(元)', value: 'budgetAmount', span: 1 },
  { label: '销售合同名称', value: 'saleContractName', span: 1 },
  { label: '销售合同编号', value: 'saleContractNumber', span: 1 },
  { label: '合同金额(元)', value: 'contractAmount', span: 1 },
  { label: '合同中的云服务金额(元)', value: 'contractServiceAmount', span: 1 },
  { label: '合同中的云服务开始日期', value: 'contractServiceStartTime', span: 1 },
  { label: '合同中的云服务结束日期', value: 'contractServiceEndTime', span: 1 },
  { label: '合同中的云服务履约周期(天)', value: 'contractServicePerformanceCycle', span: 1 },
  { label: '合同价格类型', value: 'priceType', span: 1 },
  { label: '合同合计拟付款金额(元)', value: 'totalPaymentAmount', span: 1 },
  { label: '合同合计拟回款金额(元)', value: 'totalCollectionAmount', span: 1 },
  { label: '剩余现金流(元)', value: 'cashFlowAmount', span: 1 },
]

const detail = ref({})
const id = ref()

onMounted(() => {
  if (props.id) {
    id.value = props.id
  } else {
    id.value = router.currentRoute.value.query.id
  }
  getDetail()
})

async function getDetail() {
  try {
    const res = await getCtCloudServicePaymentLedgerDetail({
      id: id.value,
    })
    if (res.code === 200) {
      detail.value = res.data
    }
  } catch (error) {
    console.log(error)
  }
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(detail))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 8px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  position: relative;
  margin-top: 20px;
}
</style>
