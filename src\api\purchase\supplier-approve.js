import { get, post, remove } from '@/utils/alova'
import { download } from '@/utils/request'
/**
 * 供应商审批单分页列表
 * @param {*} data
 * @returns
 */
export const getSupplierApprovalList = data => post('/purchaseManage/supplier/approval/pageList', data)

/**
 * 获取详情
 * @param {*} data
 * @returns
 */
export const getSupplierApprovalDetail = data => get('/purchaseManage/supplier/approval/get', data)
/**
 * 删除定标审批
 * @param {*} data
 * @returns
 */
export const rmSupplierApproval = data => remove (`/purchaseManage/supplier/approval/del/${data}`)
/**
 * 新增定标审批（提交审批）
 * @param {*} data
 * @returns
 */
export const addSupplierApproval = data => post('/purchaseManage/supplier/approval/submit/add', data)
/**
 * 新增定标审批（暂存）
 * @param {*} data
 * @returns
 */
export const saveSupplierApproval = data => post('/purchaseManage/supplier/approval/tempStore/add', data)
/**
 * 获取采购预算
 * @param {*} data
 * @returns
 */
export const getPurchaseBudget = data => get('/purchaseManage/supplier/approval/get/purchaseBudget', data)

export function exportSupplierApprovalList(data, filename) {
  return download('/purchaseManage/supplier/approval/export', data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}
