<template>
  <Container>
    <TableContainer title="变更申请">
      <template #search="{ searchBoxWidth }">
        <FilterCriteria
          :popover-width="searchBoxWidth"
          @search="refresh"
          @reset="onReset">
          <ElForm
            ref="formRef"
            :model="formData"
            label-width="130px"
            label-position="left">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem
                  prop="approveStatus"
                  label="审批状态">
                  <ElSelect
                    v-model="formData.approveStatus"
                    :teleported="false">
                    <ElOption
                      v-for="item in config.listQueryStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem
                  prop="prdProjectName"
                  label="变更项目名称">
                  <ElInput v-model="formData.prdProjectName" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </FilterCriteria>
      </template>
      <template #toolbar>
        <div>
          <ElButton
            type="primary"
            @click="router.push('/project-development/project-application-change/form')">
            变更申请
          </ElButton>
          <ElButton
            @click="exportHandle">
            导出
          </ElButton>
        </div>
      </template>

      <template #default="{ contentHeight }">
        <ElTable
          v-loading="loading"
          border
          :data="data"
          :max-height="contentHeight"
          row-key="bid"
          @selection-change="handleSelect"
          @row-dblclick="onRowDbClick">
          <ElTableColumn
            type="selection"
            reserve-selection
            width="55" />
          <ElTableColumn
            prop="approveStatus"
            label="审批状态">
            <template #default="{ row }">
              <div class="status-row">
                <div :class="`status-icon status-icon-${row.approveStatus}`" />
                {{ FLOW_STATUS_LABEL[row.approveStatus] }}
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="prdProjectName"
            label="变更项目名称" />
          <ElTableColumn
            prop="prdProjectCode"
            label="变更项目编号" />
          <ElTableColumn
            prop="principal"
            label="变更项目研发负责人" />
          <ElTableColumn
            prop="changeType"
            label="变更类型" />
          <ElTableColumn
            prop="changeReason"
            label="变更原因" />
          <ElTableColumn
            prop="submitter"
            label="申请人" />
          <ElTableColumn
            prop="submitTime"
            label="提交时间" />
          <ElTableColumn
            label="操作"
            width="120">
            <template #default="{ row }">
              <template v-if="row.approveStatus === 0">
                <ElButton
                  type="primary"
                  link
                  @click="$router.push({ path: '/project-development/project-application-change/form', query: { type: 'edit', id: row.bid } })">
                  编辑
                </ElButton>
                <ElButton
                  type="danger"
                  link
                  @click="onDeleteHandle(row.bid)">
                  删除
                </ElButton>
              </template>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <template #footer>
        <ElPagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total,prev,pager,next,sizes,jumper"
          :total="Number(total)" />
      </template>
    </TableContainer>
  </Container>
</template>

<script setup>
import { deleteChange, getProjectApplicationChangePageList } from '@/api/project-development/project-application-change.js'
import Container from '@/components/Container/index.vue'
import TableContainer from '@/components/Container/table-container.vue'
import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'
import { FLOW_STATUS_LABEL } from '@/config/flow-status'
import { usePagination } from 'alova/client'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import * as config from '../project-application/config.js'

const { proxy } = getCurrentInstance()

const router = useRouter()
const formData = ref({})
const formRef = ref()

const { loading, data, pageSize, page, total, refresh } = usePagination((pageNum, pageSize) => getProjectApplicationChangePageList({
  params: formData.value,
  page: {
    pageNum,
    pageSize,
  },
}), {
  total: res => res.total,
  data: res => res.list,
  initialPage: 1, // 初始页码，默认为1
  initialPageSize: 10, // 初始每页数据条数，默认为10
})

function onRowDbClick(row) {
  router.push({ path: '/project-development/project-application-change/detail', query: { id: row.bid } })
}

function onReset() {
  formRef.value.resetFields()
  formData.value = {}
  refresh()
}
async function onDeleteHandle(id) {
  ElMessageBox({
    title: '警告',
    type: 'warning',
    message: '确定要删除该条记录吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        try {
          await deleteChange(id)
          ElMessage.success('删除成功')
          done()
          refresh()
        } catch (error) {
          console.log(error)
        }
        instance.confirmButtonLoading = false
      } else {
        done()
      }
    },
  })
}

const ids = ref([])
function handleSelect(selection) {
  ids.value = selection.map(item => item.bid)
}
function exportHandle() {
  if (ids.value.length <= 0) {
    ElMessage.warning('请至少选择一条数据')
  } else {
    proxy.download('/project/prd/project/change/export', { bids: ids.value, isAll: 0, params: {} }, `研发项目变更申请_${new Date().getTime()}.xlsx`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
</script>

<style lang="scss" scoped>
.status-row {
  display: flex;
  align-items: center;

  .status-icon {
    width: 6px;
    height: 6px;
    margin-right: 6px;
    border-radius: 50%;
  }

  .status-icon-0 {
    background: #c1bfbf;
  }

  .status-icon-1 {
    background: #faad14;
  }

  .status-icon-2 {
    background: #1677ff;
  }

  .status-icon-3 {
    background: #52c41a;
  }

  .status-icon-4 {
    background: #ff7875;
  }

  .status-icon-5 {
    background: #722ed1;
  }

  .status-icon-6 {
    background: rgb(0 0 0 / 25%);
  }

  .status-icon-7 {
    background: #eb2f96;
  }
}
</style>
