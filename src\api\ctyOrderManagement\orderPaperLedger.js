import request from '@/utils/request'

// 暂存、更新订单台账申请信息
export function createOrUpdateOrderPaperLedger(data) {
  return request({
    url: '/cloudorder/ct-cloud-order-ledger/createOrUpdate',
    method: 'post',
    data,
  })
}

// 删除订单台账
export function deleteOrderPaperLedger(data) {
  return request({
    url: '/cloudorder/ct-cloud-order-ledger/del',
    method: 'post',
    data,
  })
}

// 获取详情
export function getOrderPaperLedgerDetail(params) {
  return request({
    url: '/cloudorder/ct-cloud-order-ledger/detail',
    method: 'get',
    params,
  })
}

// 下载订单台账
export function downloadOrderPaperLedger(params) {
  return request({
    url: '/cloudorder/ct-cloud-order-ledger/download',
    method: 'get',
    params,
  })
}

// 获取订单台账分页列表
export function getOrderPaperLedgerPageList(data) {
  return request({
    url: '/cloudorder/ct-cloud-order-ledger/pageList',
    method: 'post',
    data,
  })
}

// 提交订单台账
export function submitOrderPaperLedger(data) {
  return request({
    url: '/cloudorder/ct-cloud-order-ledger/submit',
    method: 'post',
    data,
  })
}
