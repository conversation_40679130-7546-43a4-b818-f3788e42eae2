<template>
  <ElTable
    :max-height="contentHeight"
    :data="dataSource"
    border
    style="width: 100%">
    <ElTableColumn
      type="index"
      :index="indexMethod"
      label="序号"
      align="center"
      width="100" />
    <ElTableColumn
      prop="projectPhase"
      label="项目阶段"
      width="180" />
    <ElTableColumn
      prop="remark"
      label="备注"
      width="200" />
    <ElTableColumn
      prop="sortField"
      label="排序字段"
      width="80" />
    <ElTableColumn
      prop="cuttingRequirements"
      label="剪裁要求"
      width="150">
      <template #default="scope">
        <ElSelect
          v-model="scope.row.cuttingRequirements"
          :disabled="formType === 'view'"
          placeholder="请选择">
          <ElOption
            label="可裁剪"
            value="可裁剪" />
          <ElOption
            label="须提交"
            value="须提交" />
        </ElSelect>
      </template>
    </ElTableColumn>
    <ElTableColumn
      prop="electronicDocuments"
      label="电子文件"
      width="80" />
    <ElTableColumn
      prop="electronicDocumentsCommit"
      label="电子资料提交情况"
      width="130">
      <template #default="scope">
        <ElSelect
          v-model="scope.row.electronicDocumentsCommit"
          :disabled="formType === 'view'"
          placeholder="请选择">
          <ElOption
            label="已提交"
            value="已提交" />
          <ElOption
            label="未提交"
            value="未提交" />
          <ElOption
            label="待整改"
            value="待整改" />
        </ElSelect>
      </template>
    </ElTableColumn>
    <template v-if="roles.includes(1)">
      <ElTableColumn
        prop="electronicDocumentsCheckDate"
        label="电子版检查日期"
        width="150">
        <template #default="scope">
          <ElDatePicker
            v-model="scope.row.electronicDocumentsCheckDate"
            :disabled="formType === 'view'"
            style="width: 120px;"
            type="date"
            placeholder="选择日期" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="electronicDocumentsExaminer"
        label="电子版检查人"
        width="150">
        <template #default="scope">
          <ElInput
            v-model="scope.row.electronicDocumentsExaminer"
            :disabled="formType === 'view'"
            placeholder="请输入"
            readonly
            @click="pickShow(scope.row, 'examiner')" />
          <ElInput
            v-model="scope.row.electronicDocumentsExaminerId"
            :disabled="formType === 'view'"
            style="display: none;" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="electronicDocumentsRemark"
        label="电子版备注"
        width="200">
        <template #default="scope">
          <ElInput
            v-model="scope.row.electronicDocumentsRemark"
            :disabled="formType === 'view'"
            placeholder="请输入" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="duplicatesRemark"
        label="补档情况说明"
        width="180">
        <template #default="scope">
          <ElInput
            v-model="scope.row.duplicatesRemark"
            :disabled="formType === 'view'"
            placeholder="请输入" />
        </template>
      </ElTableColumn>
    </template>
    <ElTableColumn
      prop="paperOriginal"
      label="纸质原件"
      width="120" />
    <ElTableColumn
      prop="paperOriginalCommit"
      label="纸质资料提交情况"
      width="130">
      <template #default="scope">
        <ElSelect
          v-model="scope.row.paperOriginalCommit"
          :disabled="formType === 'view'"
          placeholder="请选择">
          <ElOption
            label="已提交"
            value="已提交" />
          <ElOption
            label="未提交"
            value="未提交" />
          <ElOption
            label="不必提交"
            value="不必提交" />
        </ElSelect>
      </template>
    </ElTableColumn>
    <ElTableColumn
      prop="submitDept"
      label="资料提交责任部门"
      width="180" />
    <ElTableColumn
      prop="paperOriginalCount"
      label="纸质档份数"
      width="120">
      <template #default="scope">
        <ElInput
          v-model="scope.row.paperOriginalCount"
          :disabled="formType === 'view'"
          placeholder="请输入" />
      </template>
    </ElTableColumn>
    <ElTableColumn
      prop="storageLocation"
      label="存放地点"
      width="180">
      <template #header>
        <div class="header-tip">
          存放地点
          <ElTooltip
            content="
              数产集团/数据集团填写规范：若区域柜子有编号
              - 填写格式：区域编号+柜子号 - 示例：A区-03柜. 无编号柜子   - 填写具体位置描述"
            placement="top">
            <ElIcon><Warning /></ElIcon>
          </ElTooltip>
        </div>
      </template>
      <template #default="scope">
        <ElInput
          v-model="scope.row.storageLocation"
          :disabled="formType === 'view'"
          placeholder="请输入" />
      </template>
    </ElTableColumn>
    <template v-if="roles.includes(2)">
      <ElTableColumn
        prop="paperOriginalEntryDate"
        label="纸质版入档日期"
        width="150">
        <template #default="scope">
          <ElDatePicker
            v-model="scope.row.paperOriginalEntryDate"
            :disabled="formType === 'view'"
            style="width: 120px;"
            type="date"
            placeholder="选择日期" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="paperOriginalRegistrant"
        label="纸质版登记人"
        width="120">
        <template #default="scope">
          <ElInput
            v-model="scope.row.paperOriginalRegistrant"
            :disabled="formType === 'view'"
            placeholder="请输入"
            readonly
            @click="pickShow(scope.row, 'registrant')" />
          <ElInput
            v-model="scope.row.paperOriginalRegistrantId"
            :disabled="formType === 'view'"
            style="display: none;" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="paperOriginalRecipient"
        label="纸质版接收人"
        width="120">
        <template #default="scope">
          <ElInput
            v-model="scope.row.paperOriginalRecipient"
            :disabled="formType === 'view'"
            placeholder="请输入"
            readonly
            @click="pickShow(scope.row, 'recipient')" />
          <ElInput
            v-model="scope.row.paperOriginalRecipientId"
            :disabled="formType === 'view'"
            style="display: none;" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="paperOriginalRemark"
        label="纸质版备注"
        width="200">
        <template #default="scope">
          <ElInput
            v-model="scope.row.paperOriginalRemark"
            :disabled="formType === 'view'"
            placeholder="请输入" />
        </template>
      </ElTableColumn>
    </template>
    <OrgPicker
      ref="picker"
      type="user"
      :title="`请选择新增关联${title}`"
      :selected="userSelected"
      @ok="orgPickSelected" />
  </ElTable>
</template>

<script setup>
import { usePagination } from '@/utils/hooks.js'
import { Warning } from '@element-plus/icons-vue'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { ref } from 'vue'

const { dataSource, formType, roles } = defineProps({ // role
  dataSource: {
    type: Array,
    default: () => [],
  },
  formType: {
    type: String,
    default: '',
  },
  roles: {
    type: Array,
    default: () => [],
  },
})
const selectedCell = ref(null) // 选中的单元格
const selectedPickType = ref('')
const userSelected = [] // 选中的用户
const title = ref('')
const picker = ref({})
function pickShow(row, type) {
  if (formType !== 'view') {
    picker.value.show()
    selectedCell.value = row
    selectedPickType.value = type
  }
}

function orgPickSelected(selected) {
  if (selectedPickType.value === 'examiner') {
    selectedCell.value.electronicDocumentsExaminer = selected[0].name
    selectedCell.value.electronicDocumentsExaminerId = selected[0].id
  } else if (selectedPickType.value === 'registrant') {
    selectedCell.value.paperOriginalRegistrant = selected[0].name
    selectedCell.value.paperOriginalRegistrantId = selected[0].id
  } else if (selectedPickType.value === 'recipient') {
    selectedCell.value.paperOriginalRecipient = selected[0].name
    selectedCell.value.paperOriginalRecipientId = selected[0].id
  }
}

const { indexMethod } = usePagination()

function getTableData() {
  return unref(dataSource)
}

defineExpose({
  getTableData,
})
</script>
