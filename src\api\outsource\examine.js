import request from '@/utils/request.js'

/**
 * 供应商分页列表
 */
export function getSupplierList(params) {
  return request({
    url: '/outsource/supplier/list',
    method: 'get',
    params,
  })
}

/**
 * 根据名称获取模板
 */
export function getTemplateList(params) {
  return request({
    url: '/outsource/checkTemplate',
    method: 'get',
    params,
  })
}

/**
 * 创建或修改模板
 */
export function saveTemplate(data, method) {
  return request({
    url: '/outsource/checkTemplate',
    method,
    data,
  })
}

/**
 * 考核分页列表
 */
export function getExamineList(params) {
  return request({
    url: '/outsource/check/list',
    method: 'get',
    params,
  })
}

/**
 * 导出供应商核算列表
 */
export function exportSupplierList(data) {
  return request({
    url: '/outsource/check/supplier/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

/**
 * 导出外包员工核算列表
 */
export function exportStaffList(data) {
  return request({
    url: '/outsource/check/employee/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

/**
 * 发起考核
 */
export function startCheck(data) {
  return request({
    url: '/outsource/check/startCheck',
    method: 'post',
    data,
  })
}

/**
 * 考核详情
 */
export function getCheckDetail(id) {
  return request({
    url: `/outsource/check/${id}`,
    method: 'get',
  })
}

/**
 * 获取外包人员考核列表
 */
export function getStaffList(params) {
  return request({
    url: `/outsource/staff/checkList`,
    method: 'get',
    params,
  })
}

/**
 * 通过bid查询外包人员详情
 */
export function getStaffDetail(params) {
  return request({
    url: `/outsource/staff/detail`,
    method: 'get',
    params,
  })
}

/**
 * 打分详情页面
 */
export function getCheckScoreDetail(id) {
  return request({
    url: `/outsource/check/score/detail/${id}`,
    method: 'post',
  })
}

/**
 * 打分
 */
export function checkScore(data) {
  return request({
    url: `/outsource/check/score`,
    method: 'post',
    data,
  })
}
