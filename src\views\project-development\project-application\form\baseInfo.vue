<template>
  <Title>项目基本信息</Title>
  <ElRow :gutter="30">
    <ElCol :span="12">
      <ElFormItem
        label="项目名称"
        prop="prdProjectName">
        <ElInput
          v-model="formData.baseInfo.prdProjectName"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目编号">
        <ElInput
          v-model="formData.baseInfo.prdProjectCode"
          disabled
          placeholder="审批通过后自动生成" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="研发负责人"
        prop="principal">
        <ElSelect
          v-model="formData.baseInfo.principal"
          placeholder="请选择"
          @visible-change="change => change && principalPickerRef.show()" />
      </ElFormItem>
      <OrgPicker
        ref="principalPickerRef"
        type="user"
        :selected="formData.baseInfo.principalId ? [{ name: formData.baseInfo.principal, id: formData.baseInfo.principalId, type: 'user' }] : []"
        @ok="data => { formData.baseInfo.principal = data[0].name; formData.baseInfo.principalId = data[0].id }" />
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="申请日期">
        <ElDatePicker
          v-model="formData.baseInfo.applyTime"
          disabled
          placeholder="提交时自动获取"
          style="width: 100%"
          value-format="YYYY-MM-DD" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="业务主体"
        prop="prdEntityDept">
        <ElSelect
          v-model="formData.baseInfo.prdEntityDept"
          placeholder="请选择">
          <ElOption
            v-for="(item, index) in yfxm_business_entity"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="项目分类"
        prop="prdProjectCategory">
        <ElCascader
          v-model="formData.baseInfo.prdProjectCategory"
          style="width: 100%"
          placeholder="选择项目名称后自动填入"
          :props="cascaderProps"
          clearable
          :options="xsba_classify" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="研发方式"
        prop="rdMethod">
        <ElSelect
          v-model="formData.baseInfo.rdMethod"
          placeholder="请选择">
          <ElOption
            v-for="(item, index) in yfxm_development_method"
            :key="index"
            :value="item.value"
            :label="item.label" />
        </ElSelect>
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目分级">
        <ElInput
          v-model="formData.baseInfo.projectClassification"
          disabled
          placeholder="自动判断" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="立项日期"
        prop="projectInitiationDate">
        <ElDatePicker
          v-model="formData.baseInfo.projectInitiationDate"
          placeholder="请选择日期"
          style="width: 100%"
          value-format="YYYY-MM-DD" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="项目分管领导"
        prop="projectLeader">
        <ElSelect
          v-model="formData.baseInfo.projectLeader"
          placeholder="请选择"
          @visible-change="change => change && projectLeaderPickerRef.show()" />
      </ElFormItem>
      <OrgPicker
        ref="projectLeaderPickerRef"
        type="user"
        :selected="formData.baseInfo.projectLeaderId ? [{ name: formData.baseInfo.projectLeader, id: formData.baseInfo.projectLeaderId, type: 'user' }] : []"
        @ok="data => { formData.baseInfo.projectLeader = data[0].name; formData.baseInfo.projectLeaderId = data[0].id }" />
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        label="研发项目基本情况"
        prop="projectBasicInformation">
        <ElInput
          v-model="formData.baseInfo.projectBasicInformation"
          :rows="2"
          type="textarea"
          placeholder="请简要介绍研发项目的背景，目标，必要性，主要功能，成本预算等" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        ref="uploadRef"
        prop="fileUrlList">
        <template #label>
          <span>附件</span>
          <ElButton
            style="margin-left: 40px;"
            link
            type="primary">
            <ElIcon><Download /></ElIcon>
            模板下载
          </ElButton>
        </template>
        <ElUpload
          v-model:file-list="formData.baseInfo.fileUrlList"
          style="width: 100%; padding-top: 20px;"
          :action="uploadUrl"
          :on-preview="on_preview_or_downFile"
          auto-upload
          :on-success="onFileUploadSuccess"
          :on-error="onFileUploadFail"
          drag
          multiple>
          <i
            style="color: #1677FF; font-size: 28px;"
            class="iconfont icon-InboxOutlined" />
          <h3>拖拽或点击文件上传</h3>
          <div class="el-upload__tip">
            支持PPT、PPTX、PDF、DOC、DOCX、TXT、ZIP、JPG等通用格式
          </div>
        </ElUpload>
      </ElFormItem>
    </ElCol>
  </ElRow>
</template>

<script setup>
import * as apis from '@/api/project-development/project-application.js'
import Title from '@/components/Title/index.vue'
import { on_preview_or_downFile } from '@/utils/hooks.js'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const { yfxm_business_entity, yfxm_development_method, xsba_classify } = proxy.useDict('yfxm_business_entity', 'yfxm_project_classification', 'yfxm_development_method', 'xsba_classify')

const principalPickerRef = useTemplateRef('principalPickerRef')

const projectLeaderPickerRef = useTemplateRef('projectLeaderPickerRef')

const uploadRef = useTemplateRef('uploadRef')

const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址

const formData = defineModel()

const cascaderProps = {
  expandTrigger: 'hover',
}

function onFileUploadSuccess() {
  uploadRef.value.clearValidate()
  ElMessage.success('上传附件成功')
}

function onFileUploadFail() {
  ElMessage.error('上传附件失败')
}

watchEffect(() => {
  if (formData.value.baseInfo.budgetAmount >= 500) {
    formData.value.baseInfo.projectClassification = '战略级'
  } else if (formData.value.baseInfo.budgetAmount >= 200 && formData.value.baseInfo.budgetAmount < 500) {
    formData.value.baseInfo.projectClassification = '重点级'
  } else {
    formData.value.baseInfo.projectClassification = '常规级'
  }
})

onMounted(async () => {
  // 获取数产集团本部产品研发部信息
  try {
    const res = await apis.getDeptInfo({ sysCode: '010103000J' })
    if (res.fenGuanLeaderName && res.fenGuanLeader) {
      formData.value.baseInfo.projectLeader = res.fenGuanLeaderName
      formData.value.baseInfo.projectLeaderId = res.fenGuanLeader
    }
  } catch (error) {
    console.log(error)
  }
})
</script>

<style lang="scss" scoped>

</style>
