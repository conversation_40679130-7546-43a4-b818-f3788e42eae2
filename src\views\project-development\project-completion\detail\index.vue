<template>
  <Container show-back>
    <div class="wrapper">
      <ElForm label-position="top">
        <BaseInfo v-model="formData" />
        <FinancialSettlementInfo
          v-if="isFromApprovePage"
          v-model="formData" />
        <ProjectClosingStatement v-model="formData" />
      </ElForm>
    </div>
  </Container>
</template>

<script setup>
import { getProjectCompletionDetailByBid } from '@/api/project-development/project-completion.js'
import Container from '@/components/Container/index.vue'
import { useRoute } from 'vue-router'
import BaseInfo from './baseInfo.vue'
import FinancialSettlementInfo from './financialSettlementInfo.vue'
import ProjectClosingStatement from './projectClosingStatement.vue'

const props = defineProps({
  id: {
    default: '',
  },
})

const route = useRoute()

const formData = ref({})

onMounted(async () => {
  if (route.query.id) {
    const res = await getProjectCompletionDetailByBid(route.query.id)
    formData.value = res
  } else if (props.id) {
    const res = await getProjectCompletionDetailByBid(props.id)
    formData.value = res
  }
})

const isFromApprovePage = computed(() => {
  const { instanceId, nodeId } = route.query
  return instanceId && nodeId
})

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(formData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve(readonly(unref(formData)))
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.wrapper {
  flex: auto;
  overflow-y: auto;
  padding: 10px 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
