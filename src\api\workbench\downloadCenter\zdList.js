import request from '@/utils/request'

// 分页查询
export function getList(data) {
  return request ({
    url: '/system/institution/document',
    method: 'get',
    params: data,
  })
}

// 新增制度文件
export function addZdList(data) {
  return request({
    url: '/system/institution/document',
    method: 'put',
    data,
  })
}

// 下载
export function downloadList(data) {
  return request({
    url: '/system/institution/document/downloadDocument',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

// 导出
export function exportList() {
  return request({
    url: '/system/institution/document/exportDocument',
    method: 'post',
  })
}

// 制度标签列表
export function zdselectList() {
  return request({
    url: '/system/institution/document/label',
    method: 'get',
  })
}

// 制度文件已读未读
export function zdTypeRead() {
  return request({
    url: '/system/institution/document/readCondition',
    method: 'get',
  })
}

// 标记已读未读
export function bjRead(data) {
  return request({
    url: '/system/institution/document/readCondition',
    method: 'post',
    data,
  })
}

// 制度文件详情
export function typeDetails(bid) {
  return request({
    url: `/system/institution/document/${bid}`,
    method: 'get',
  })
}

// 置顶
export function typeTop(data) {
  return request({
    url: '/system/institution/document/top',
    method: 'post',
    data,
  })
}

// 删除制度文件
export function zdDelete(bid) {
  return request({
    url: `/system/institution/document/${bid}`,
    method: 'delete',
  })
}
