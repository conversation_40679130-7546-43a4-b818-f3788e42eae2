<template>
  <Title style="margin-top: 20px;">
    项目验收说明
  </Title>
  <ElDescriptions
    border
    :column="4"
    label-width="160">
    <ElDescriptionsItem
      label="验收类型">
      {{ formData.checkType }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="是否用印">
      {{ formData.isUseSeal }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      v-if="formData.isUseSeal === '是'"
      label="印章类型">
      {{ formData.sealBids }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="验收采购合同名称"
      :span="2">
      {{ formData.contractName }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="验收采购合同编号"
      :span="1">
      {{ formData.contractCode }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label-width="220"
      label="验收采购合同含税金额（元）"
      :span="1">
      {{ formData.taxAmount }}
    </ElDescriptionsItem>
  </ElDescriptions>
  <ElTable
    border
    :data="formData.contractOtherSubjects || []"
    style="margin-top: 20px;">
    <ElTableColumn
      label="对方主体名称"
      prop="merchantName" />
    <ElTableColumn
      label="对方主体身份"
      prop="identity" />
    <ElTableColumn
      label="银行账户"
      prop="account" />
  </ElTable>
  <ElDescriptions
    border
    :column="1"
    style="margin-top: 20px;"
    label-width="140">
    <ElDescriptionsItem
      label="验收情况说明"
      :span="1">
      {{ formData.checkAcceptFactSheet }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="附件"
      :span="1">
      <template v-if="formData.attachments">
        <div
          v-for="(item, index) in formData.attachments"
          :key="index"
          style="display: flex; align-items: center;">
          <ElIcon>
            <Document />
          </ElIcon>
          <span style="padding: 2px 0 0 2px;">
            {{ item.match(/[^/\\?#]+$/)[0] }}
          </span>
          <ElButton
            link
            type="primary"
            style="margin-left: 8px;"
            @click="handleDownload(item)">
            下载
          </ElButton>
        </div>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const formData = defineModel()

// 自定义附件下载逻辑
function handleDownload(address) {
  const link = document.createElement('a')
  link.href = address
  // 设置下载文件名
  link.download = address.match(/[^/\\?#]+$/)[0]
  document.body.appendChild(link)
  link.click()
  // 清理 DOM
  document.body.removeChild(link)
}
</script>

<style lang="scss" scoped>

</style>
