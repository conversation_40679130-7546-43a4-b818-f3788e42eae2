import { get, post, remove } from '@/utils/alova'

/**
 * 获取项目结项分页列表
 */
export function getProjectCompletionPageList(data, config) {
  return post('/project/prd/project/completion/pageList', data, config)
}

/**
 * 删除项目结项数据
 */
export function deleteProjectCompletion(completionBid, config) {
  return remove(`/project/prd/project/completion/deleted/${completionBid}`, null, config)
}

/**
 * 通过编码获取发起项目结项详情
 */
export function getProjectCompletionDetailByCode(prdProjectCode, config) {
  return get(`/project/prd/project/completion/startDetail/${prdProjectCode}`, null, config)
}

/**
 * 通过bid获取发起项目结项详情
 */
export function getProjectCompletionDetailByBid(completionBid, config) {
  return get(`/project/prd/project/completion/queryDetail/${completionBid}`, null, config)
}

/**
 * 发起项目结项
 */
export function startProjectCompletion(data, config = { transformRes: false }) {
  return post('/project/prd/project/completion/start', data, config)
}
