import request from '@/utils/request'

const prefix = '/sso'

// 获取路由
export function getRouters() {
  return request({
    url: `${prefix}/getRouters`,
    method: 'get',
  })
}

// 获取项目列表
export function getProjectList(data) {
  return request({
    url: `/project/business/library/list`,
    method: 'post',
    data,
  })
}
// 获取预立项项目列表
export function getPreProjectList(data) {
  return request({
    url: `/project/preinit/pre-est-project/tender/pageList`,
    method: 'post',
    data,
  })
}

// 获取项目库列表
export function getProjectLibraryList(data) {
  return request({
    url: `/project/projectLibrary/pageList`,
    method: 'post',
    data,
  })
}

/**
 * 获取检查表列表
 * @param {*} data
 * @returns
 */
export function getCheckList(params) {
  return request({
    url: `/system/checkList/all`,
    method: 'get',
    params,
  })
}

/**
 * 获取投标申请审批通过接口
 * @param {*} data
 * @returns
 */
export function getSuccessBidApplication(params) {
  return request({
    url: `/project/bid/registration/getSuccessBidApplication`,
    method: 'get',
    params,
  })
}
