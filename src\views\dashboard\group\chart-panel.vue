<template>
  <div class="chart_panel">
    <div class="chart_panel_header">
      <slot name="title">
        <div class="flex-1 flex items-center">
          <p class="text-[20px] font-[600] mr-[12px]">
            {{ title }}
          </p>
          <ElTooltip
            placement="top"
            popper-class="chart_panel_tooltip"
            :content="tooltip">
            <i class="iconfont icon-qy_help1 text-[#000]/45" />
          </ElTooltip>
          <template v-if="showMore">
            <ElDivider direction="vertical" />
            <ElTag
              class="cursor-pointer"
              effect="light"
              :round="true"
              @click="handleClick">
              <div class="flex items-center text-[12px]">
                <ElIcon
                  :size="12"
                  style="margin-right: 6px;">
                  <View />
                </ElIcon>
                数据详情
              </div>
            </ElTag>
          </template>
        </div>
      </slot>
      <slot name="headerRight">
        <div class="">
          <ElSelect
            v-model="dataType"
            style="width: 120px;"
            size="small">
            <ElOption
              v-for="item in dataTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </div>
      </slot>
    </div>
    <slot />
  </div>
</template>

<script setup>
const { title, tooltip, showMore } = defineProps({
  title: String,
  tooltip: String,
  showMore: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['click'])
function handleClick() {
  emit('click')
}

const dataType = ref('数产集团')
const dataTypeOptions = ref([
  {
    label: '数产集团',
    value: '数产集团',
  },
])
</script>

<style lang="scss" scoped>
.chart_panel {
  width: 100%;
  min-height: 100%;
  padding: 0 18px;
  border-radius: 10px;
  background: #fff;

  &_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 0 16px;
  }
}
</style>
