<template>
  <ProjectCenter>
    <div style="padding: 20px">
      <div class="title">
        <span class="title-text">国资云</span>
        <div class="desc">
          <div class="desc-item">
            <div class="label">
              项目名称：
            </div>
            <div class="value">
              {{ route.query.name }}
            </div>
          </div>
          <ElDivider direction="vertical" />
          <div class="desc-item">
            <div class="label">
              项目编号：
            </div>
            <div class="value">
              {{ route.query.pcode }}
            </div>
          </div>
        </div>
      </div>
      <div class="sub-title">
        国资云资源申请
      </div>
      <ElTable
        v-loading="resourceApplicationLoading"
        :data="resourceApplicationList"
        border>
        <ElTableColumn
          fixed="left"
          type="index"
          label="序号"
          width="60" />
        <ElTableColumn
          prop="customerName"
          label="客户名称"
          min-width="120px" />
        <ElTableColumn
          prop="gzAppName"
          label="应用系统名称"
          min-width="120px" />
        <ElTableColumn
          prop="servicePlanStartTime"
          label="服务计划起始日期"
          min-width="130px" />
        <ElTableColumn
          prop="servicePlanEndTime"
          label="服务计划终止日期"
          min-width="130px" />
        <ElTableColumn
          prop="budgetAmount"
          label="云预算金额(元)"
          min-width="120px" />
        <ElTableColumn
          prop="availableBudget"
          label="可用预算(元)"
          min-width="120px" />
        <ElTableColumn
          prop="applicationFee"
          label="本次申请费用(元)"
          min-width="130px" />
        <ElTableColumn
          prop="cumulativeExecutionAmount"
          label="累计执行金额(元)"
          min-width="130px" />
      </ElTable>
      <div class="sub-title">
        国资云费用结算
      </div>
      <ElTable
        v-loading="feeSettlementLoading"
        :data="feeSettlementList"
        border>
        <ElTableColumn
          fixed="left"
          type="index"
          label="序号"
          width="60" />
        <ElTableColumn
          prop="billName"
          label="账单名称"
          min-width="120px" />
        <ElTableColumn
          prop="gzAppName"
          label="应用系统名称"
          min-width="120px" />
        <ElTableColumn
          prop="billCycle"
          label="计费周期（天）"
          min-width="130px" />
        <ElTableColumn
          prop="billDiscountedFees"
          label="折后费用（元）"
          min-width="130px" />
        <ElTableColumn
          prop="saleContractNumber"
          label="合同编号"
          min-width="120px" />
        <ElTableColumn
          prop="saleContractName"
          label="合同名称"
          min-width="120px" />
        <ElTableColumn
          prop="servicePlanCycle"
          label="服务计划周期（天）"
          min-width="130px" />
        <ElTableColumn
          prop="budgetAmount"
          label="云预算金额(元)"
          min-width="130px" />
        <ElTableColumn
          prop="thisSettlementAmount"
          label="本次结算金额（元）"
          min-width="130px" />
        <ElTableColumn
          prop="settlementRatio"
          label="结算占比(%)"
          min-width="130px" />
      </ElTable>
    </div>
  </ProjectCenter>
</template>

<script setup>
import { getServiceFeeSettlementPageList } from '@/api/socsManage/newExpenseSettlement.js'
import { getGzCloudResourceRequestPageList } from '@/api/socsManage/resourceUtilizationApplication.js'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import ProjectCenter from '../components/layout/index.vue'

const route = useRoute()

const resourceApplicationList = ref([])
const feeSettlementList = ref([])
const resourceApplicationLoading = ref(false)
const feeSettlementLoading = ref(false)

async function getResourceApplicationList() {
  resourceApplicationLoading.value = true
  try {
    const res = await getGzCloudResourceRequestPageList({
      page: {
        pageNum: 1,
        pageSize: 9999,
      },
      params: {
        projectName: route.query.name,
        statusList: [3],
      },
    })
    resourceApplicationList.value = res.data.list
  } catch (error) {
    console.log(error)
  } finally {
    resourceApplicationLoading.value = false
  }
}

async function getFeeSettlementList() {
  feeSettlementLoading.value = true
  try {
    const res = await getServiceFeeSettlementPageList({
      page: {
        pageNum: 1,
        pageSize: 9999,
      },
      params: {
        projectName: route.query.name,
        statusList: [3],
        cycle: [null, null],
        discountedFees: [null, null],
      },
    })
    feeSettlementList.value = res.data.list
  } catch (error) {
    console.log(error)
  } finally {
    feeSettlementLoading.value = false
  }
}

onMounted(() => {
  getResourceApplicationList()
  getFeeSettlementList()
})
</script>

<style scoped lang="scss">
.title {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid rgb(0 0 0 / 6%);

  .title-text {
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .desc {
    display: flex;
    align-items: center;
    margin-left: 20px;
    padding: 2px 8px;
    border-radius: 26px;
    background: rgb(0 0 0 / 3%);
    font-size: 14px;

    .desc-item {
      display: flex;
      align-items: center;

      .label {
        color: rgb(0 0 0 / 45%);
      }

      .value {
        color: rgb(0 0 0 / 88%);
      }
    }
  }
}

.sub-title {
  width: 100%;
  margin-top: 20px;
  margin-bottom: 20px;
  color: rgb(0 0 0 / 88%);
  font-weight: 600;
  font-size: 20px;
}
</style>
