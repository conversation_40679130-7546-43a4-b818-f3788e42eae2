<template>
  <DefaultContainer>
    <div
      v-if="!props.id"
      class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          流水认领详情
        </div>
      </div>
    </div>
    <div class="content">
      <div class="sub-title">
        基本信息
      </div>
      <ElDescriptions
        border
        :column="2"
        style="margin-bottom: 36px"
        label-width="200">
        <ElDescriptionsItem
          v-for="item in basicInfoList"
          :key="item.value"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detailData[item.value] || '-' }}
        </ElDescriptionsItem>
      </ElDescriptions>

      <div class="sub-title">
        关联信息
      </div>
      <ElDescriptions
        border
        :column="2"
        style="margin-bottom: 36px"
        label-width="200">
        <ElDescriptionsItem
          v-for="item in relatedInfoList"
          :key="item.value"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detailData[item.value] || '-' }}
        </ElDescriptionsItem>
      </ElDescriptions>

      <div class="sub-title">
        审核信息
      </div>
      <ElDescriptions
        border
        :column="2"
        style="margin-bottom: 36px"
        label-width="200">
        <ElDescriptionsItem
          v-for="item in auditInfoList"
          :key="item.value"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          <template v-if="item.value === 'status'">
            <ElTag :type="getStatusType(detailData.status)">
              {{ getStatusText(detailData.status) }}
            </ElTag>
          </template>
          <template v-else-if="item.value === 'shareStatus'">
            <ElTag :type="getSharedStatusType(detailData.shareStatus)">
              {{ getSharedStatusText(detailData.shareStatus) }}
            </ElTag>
          </template>
          <template v-else>
            {{ detailData[item.value] || '-' }}
          </template>
        </ElDescriptionsItem>
      </ElDescriptions>

      <div class="sub-title">
        流水明细
      </div>
      <ElTable
        border
        :data="flowDetails"
        style="width: 100%; margin-bottom: 36px">
        <ElTableColumn
          prop="incomeType"
          label="收入类型"
          width="120">
          <template #default>
            服务费收款
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="availableAmount"
          label="可认领金额"
          width="120" />
        <ElTableColumn
          prop="claimAmount"
          label="认领金额"
          width="120" />
        <ElTableColumn
          prop="bankFlowNumber"
          label="银行流水号"
          width="150" />
        <ElTableColumn
          prop="flowType"
          label="流水类型"
          width="120" />
        <ElTableColumn
          prop="transactionTime"
          label="交易时间"
          width="150" />
        <ElTableColumn
          prop="transactionAmount"
          label="交易金额"
          width="120" />
        <ElTableColumn
          prop="payerName"
          label="付款方户名"
          width="150" />
        <ElTableColumn
          prop="summary"
          label="摘要"
          min-width="150" />
      </ElTable>

      <div class="sub-title">
        事由
      </div>
      <ElDescriptions
        border
        style="margin-bottom: 36px">
        <ElDescriptionsItem :span="2">
          <template #label>
            <div>事由</div>
          </template>
          {{ detailData.remark || '无' }}
        </ElDescriptionsItem>
      </ElDescriptions>

      <div class="sub-title">
        附件
      </div>
      <ElDescriptions
        border
        style="margin-bottom: 36px">
        <ElDescriptionsItem :span="2">
          <template #label>
            <div>附件</div>
          </template>
          <p
            v-for="file in attachments"
            :key="file.url"
            class="file-text"
            @click="downloadFile(file.url, file.name)">
            {{ file.name }}
          </p>
          <span v-if="attachments.length === 0">暂无附件</span>
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </DefaultContainer>
</template>

<script setup>
import { getFlowClaimDetails } from '@/api/financialManagement/collectionManagement/flowClaim.js'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { Back } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
})

const route = useRoute()
const router = useRouter()

const basicInfoList = [
  { label: '申请人', value: 'applicant' },
  { label: 'UI号', value: 'applicantUi' },
  { label: '申请人部门', value: 'applicantDepartment' },
  { label: '部门编码', value: 'applicantDepartmentCode' },
  { label: '收款方式', value: 'paymentMethod' },
  { label: '扫描方式', value: 'scanMethod' },
  { label: '认领金额', value: 'claimAmount' },
  { label: '核算主体', value: 'calculateEntity' },
  { label: '核算主体编码', value: 'calculateEntityCode' },
  { label: '成本中心', value: 'costCenter' },
  { label: '成本中心编码', value: 'costCenterCode' },
]

const relatedInfoList = [
  { label: '项目名称', value: 'projectName' },
  { label: '项目编号', value: 'projectCode' },
  { label: '合同名称', value: 'contractName' },
  { label: '合同编号', value: 'contractNumber' },
  { label: '客户名称', value: 'customerName' },
  { label: '客户编码', value: 'customerCode' },
  { label: '合同开票总金额(不含税:元)', value: 'contractInvoiceTotalAmount' },
  { label: '已认领总金额(元)', value: 'contractClaimTotalAmount' },
]

const auditInfoList = [
  { label: '申请编号', value: 'formNo' },
  { label: '申请时间', value: 'applicationTime' },
  { label: '审核状态', value: 'status' },
  { label: '共享审核状态', value: 'shareStatus' },
  { label: '创建人', value: 'createByName' },
  { label: '修改人', value: 'updateByName' },
]

const detailData = ref({})
const flowDetails = ref([])
const attachments = ref([])
const id = ref()

onMounted(() => {
  if (props.id) {
    id.value = props.id
  } else {
    id.value = route.query.id
  }

  if (id.value) {
    getDetails(id.value)
  }
})

/**
 * 获取详情数据
 */
function getDetails(id) {
  getFlowClaimDetails({ id }).then((res) => {
    if (res.code === 200) {
      detailData.value = res.data
      flowDetails.value = res.data.flowClaimDetailList || []

      if (res.data.remarkAttachment) {
        try {
          let parsedArr = JSON.parse(res.data.remarkAttachment)
          parsedArr = parsedArr.map(item => ({
            name: item.match(/[^/\\?#]+$/)[0],
            url: item,
          }))
          attachments.value = parsedArr
        } catch (error) {
          console.log(error)
        }
      }
    }
  })
}

/**
 * 获取审核状态文本
 */
function getStatusText(status) {
  const statusMap = {
    0: '草稿',
    1: '待审批',
    2: '审批中',
    3: '审批通过',
    4: '审批拒绝',
    10: '已撤销',
    11: '审批结束',
    12: '-',
  }
  return statusMap[status] || '-'
}

/**
 * 获取审核状态标签类型
 */
function getStatusType(status) {
  const typeMap = {
    0: 'info',
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'danger',
    10: 'info',
    11: 'success',
    12: 'info',
  }
  return typeMap[status] || 'info'
}

/**
 * 获取共享审核状态文本
 */
function getSharedStatusText(status) {
  const statusMap = {
    0: '待发起',
    1: '已发起',
    2: '审核通过',
    3: '审核驳回',
    4: '出错',
  }
  return statusMap[status] || '-'
}

/**
 * 获取共享审核状态标签类型
 */
function getSharedStatusType(status) {
  const typeMap = {
    0: 'info',
    1: 'primary',
    2: 'success',
    3: 'danger',
    4: 'warning',
  }
  return typeMap[status] || 'info'
}

/**
 * 下载文件
 */
function downloadFile(url, filename) {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(detailData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 8px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  position: relative;
  margin-top: 20px;

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }
}

.file-text {
  cursor: pointer;

  &:hover {
    color: #409eff;
  }
}
</style>
