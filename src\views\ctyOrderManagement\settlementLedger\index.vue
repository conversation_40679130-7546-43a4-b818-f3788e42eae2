<template>
  <DefaultContainer v-loading="isLoading">
    <!-- 折叠面板组件，用于查询条件 -->
    <Collapse>
      <template #header />
      <!-- 查询表单 -->
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="200px"
        label-position="top">
        <ElFormItem
          prop="ctAppName"
          label="应用系统名称">
          <ElSelect
            v-model="formData.ctAppName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectApplicationModal = true)
            " />
        </ElFormItem>
      </ElForm>
    </Collapse>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
    </div>
    <!-- 数据表格 -->
    <div class="table-box">
      <div class="sub-title">
        结算单信息
        <ElButton
          type="primary"
          @click="handleExport('settlement')">
          导出
        </ElButton>
      </div>
      <ElTable
        :max-height="1000"
        border
        :summary-method="getSummary1"
        show-summary
        :data="tableData1">
        <ElTableColumn
          type="index"
          label="序号"
          width="60"
          fixed="left"
          align="center" />
        <ElTableColumn
          prop="formNo"
          label="结算单编号"
          min-width="120" />
        <ElTableColumn
          prop="statementName"
          label="结算单名称"
          min-width="120" />
        <ElTableColumn
          prop="projectCode"
          label="项目编号"
          min-width="120" />
        <ElTableColumn
          prop="projectName"
          label="项目名称"
          min-width="120" />
        <ElTableColumn
          prop="chargeDateStart"
          label="结算开始计费日期"
          min-width="130" />
        <ElTableColumn
          prop="chargeDateEnd"
          label="结算结束计费日期"
          min-width="130" />
        <ElTableColumn
          prop="statementCycle"
          label="结算周期(天)"
          min-width="120" />
        <ElTableColumn
          prop="totalAmount"
          label="结算总金额(元)"
          min-width="120" />
      </ElTable>
      <ElDescriptions
        border
        :column="1"
        style="margin-top: 10px;"
        label-width="200">
        <ElDescriptionsItem
          width="398">
          <template #label>
            <div>合计结算总额</div>
          </template>
          {{ totalSettlementAmount }}
        </ElDescriptionsItem>
      </ElDescriptions>

      <div class="sub-title">
        对应项目和合同信息
        <ElButton
          type="primary"
          @click="handleExport('project')">
          导出
        </ElButton>
      </div>
      <ElTable
        border
        :max-height="1000"
        :summary-method="getSummary2"
        show-summary
        :data="tableData2">
        <ElTableColumn
          type="index"
          label="序号"
          width="60"
          fixed="left"
          align="center" />
        <ElTableColumn
          prop="statementFormNo"
          label="关联结算单编号"
          min-width="120" />
        <ElTableColumn
          prop="projectCode"
          label="项目编号"
          min-width="120" />
        <ElTableColumn
          prop="projectName"
          label="项目名称"
          min-width="120" />
        <ElTableColumn
          prop="budgetAmount"
          label="项目云预算金额(元)"
          min-width="140" />
        <ElTableColumn
          prop="budgetEffectivePeriod"
          label="预算有效周期(天)"
          min-width="140" />
        <ElTableColumn
          prop="saleContractNumber"
          label="销售合同编号"
          min-width="120" />
        <ElTableColumn
          prop="saleContractName"
          label="销售合同名称"
          min-width="120" />
        <ElTableColumn
          prop="contractServiceAmount"
          label="合同中的云服务金额(元)"
          min-width="180" />
        <ElTableColumn
          prop="contractServiceStartTime"
          label="合同中的云服务开始日期"
          min-width="180" />
        <ElTableColumn
          prop="contractServiceEndTime"
          label="合同中的云服务结束日期"
          min-width="180" />
        <ElTableColumn
          prop="totalCollectionAmount"
          label="合同回款金额(元)"
          min-width="140" />
        <ElTableColumn
          prop="totalPaymentAmount"
          label="合同付款金额(元)"
          min-width="140" />
      </ElTable>
      <ElDescriptions
        border
        :column="2"
        style="margin-top: 10px;"
        label-width="200">
        <ElDescriptionsItem
          width="398">
          <template #label>
            <div>合计云已回款总金额</div>
          </template>
          {{ totalAmountOfPaymentReceived }}
        </ElDescriptionsItem>
        <ElDescriptionsItem
          width="398">
          <template #label>
            <div>合计云已付款总金额</div>
          </template>
          {{ totalPaidAmount }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
    <ApplicationSelectModal
      v-model="modelIsShow.selectApplicationModal"
      @select-item="onChangeApplication" />
  </DefaultContainer>
</template>

<script setup>
import { getCtCloudSettlementLedgerStatement, getCtCloudSettlementLedgerStatementWithPayment } from '@/api/ctyOrderManagement/settlementLedger.js'
import Collapse from '@/components/Collapse/index.vue'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import ApplicationSelectModal from '@/views/ctyOrderManagement/modules/ApplicationSelectModal.vue'
import { ElMessage } from 'element-plus'
import { computed, reactive, ref } from 'vue'

/**
 * 表单数据与引用
 */
const formData = reactive({
  ctAppName: '',
  ctAppId: '',
}) // 查询表单数据
const formRef = ref() // 表单引用，用于重置

/**
 * 表格数据与加载状态
 */
const tableData1 = ref([]) // 表格数据
const tableData2 = ref([]) // 表格数据
const loadingIndex = ref(0) // 加载计数器，用于处理多个并发请求的loading状态

/**
 * 计算属性：是否显示加载状态
 * 当loadingIndex > 0时显示加载状态
 */
const isLoading = computed(() => {
  return loadingIndex.value !== 0
})

const modelIsShow = reactive({
  selectApplicationModal: false,
})

const totalSettlementAmount = computed(() => {
  return formatAmount(tableData1.value.reduce((acc, curr) => acc + curr.totalAmount, 0))
})

const totalAmountOfPaymentReceived = computed(() => {
  return formatAmount(tableData2.value.reduce((acc, curr) => acc + curr.totalCollectionAmount, 0))
})

const totalPaidAmount = computed(() => {
  return formatAmount(tableData2.value.reduce((acc, curr) => acc + curr.totalPaymentAmount, 0))
})

function getParams() {
  return {
    ctAppId: formData.ctAppId,
    ctAppName: formData.ctAppName,
    params: {},
  }
}

/**
 * 获取应用系统信息分页列表
 */
async function getPage() {
  loadingIndex.value++ // 开始加载，计数器加1
  const searchData = getParams()
  const res1 = await getCtCloudSettlementLedgerStatement(searchData)
  const res2 = await getCtCloudSettlementLedgerStatementWithPayment(searchData)
  tableData1.value = res1.data
  tableData2.value = res2.data
  loadingIndex.value-- // 加载完成，计数器减1
}

/**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  if (formData.ctAppId) {
    getPage()
  } else {
    ElMessage.warning('请选择应用系统')
    tableData1.value = []
    tableData2.value = []
  }
}

/**
 * 重置方法
 * 重置表单并查询第一页数据
 */
function onReset() {
  formRef.value.resetFields() // 重置表单字段
  formData.ctAppName = ''
  formData.ctAppId = ''
  tableData1.value = []
  tableData2.value = []
}

function onChangeApplication(row) {
  console.log(row, '--- row')
  formData.ctAppName = row.appName
  formData.ctAppId = row.id
  onSearch()
}

const { proxy } = getCurrentInstance()
function handleExport(type) {
  const params = getParams()
  if (params.ctAppId) {
    if (type === 'settlement') {
      proxy.download(
        '/cloudorder/ct-cloud-settlement-ledger/downloadStatement',
        {
          ...params,
        },
        `结算单信息_${new Date().getTime()}.xlsx`,
      )
    } else if (type === 'project') {
      proxy.download(
        '/cloudorder/ct-cloud-settlement-ledger/downloadStatementWithPayment',
        {
          ...params,
        },
        `对应项目和合同信息_${new Date().getTime()}.xlsx`,
      )
    }
  } else {
    ElMessage.warning('请选择应用系统')
  }
}

function getSummary1(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 定义需要计算合计的列
    const sumCols = [
      'totalAmount',
    ]

    if (!sumCols.includes(column.property)) {
      sums[index] = ''
      return
    }

    // 计算需要合计的列的总和
    const values = data.map(item => Number(item[column.property]))
    if (!values.every(value => Number.isNaN(value))) {
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        }
        return prev
      }, 0)
      sums[index] = formatAmount(sum)
    } else {
      sums[index] = ''
    }
  })
  return sums
}

function getSummary2(param) {
  console.log(param, '--- param')
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 定义需要计算合计的列
    const sumCols = [
      'totalCollectionAmount',
      'totalPaymentAmount',
    ]

    if (!sumCols.includes(column.property)) {
      sums[index] = ''
      return
    }
    // 计算需要合计的列的总和
    const values = data.map(item => Number(item[column.property]))
    if (!values.every(value => Number.isNaN(value))) {
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        }
        return prev
      }, 0)
      sums[index] = formatAmount(sum)
    } else {
      sums[index] = ''
    }
  })
  return sums
}

function formatAmount(amount) {
  if (!amount && amount !== 0)
    return '-'
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}
</script>

<style lang="scss" scoped>
.sub-title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin: 10px 0;
  color: rgb(0 0 0 / 88%);
  font-weight: 600;
  font-size: 20px;
}

.table-box {
  flex: 1;
  overflow: auto;
  width: 100%;
  min-height: 0;
  margin-top: 10px;
  padding-right: 20px;
}
</style>
