import request from '@/utils/request'

// 获取流水认领分页列表
export function getFlowClaimPageList(params) {
  return request({
    url: `/finance/flow-claim/pageList`,
    method: 'post',
    data: params,
  })
}

// 删除流水认领
export function delFlowClaim(params) {
  return request({
    url: `/finance/flow-claim/del`,
    method: 'get',
    params,
  })
}

// 流水认领详情
export function getFlowClaimDetails(params) {
  return request({
    url: `/finance/flow-claim/detail`,
    method: 'get',
    params,
  })
}

// 暂存、更新流水认领信息
export function createOrUpdate(params) {
  return request({
    url: `/finance/flow-claim/createOrUpdate`,
    method: 'post',
    data: params,
  })
}

// 提交流水认领申请信息，需审批
export function submitFlowClaim(params) {
  return request({
    url: `/finance/flow-claim/submit`,
    method: 'post',
    data: params,
  })
}

// 选择的流水明细分页数据
export function queryClaimFlow(params) {
  return request({
    url: `/finance/flow-claim/queryClaimFlow`,
    method: 'get',
    params,
  })
}

// // 获取核算主体列表
// export function getAccountingSubjectList(params) {
//   return request({
//     url: `/finance/flow/claim/getAccountingSubjectList`,
//     method: 'get',
//     params,
//   })
// }

// // 根据核算主体获取项目列表
// export function getProjectListBySubject(params) {
//   return request({
//     url: `/finance/flow/claim/getProjectListBySubject`,
//     method: 'get',
//     params,
//   })
// }

// // 根据项目获取合同列表
// export function getContractListByProject(params) {
//   return request({
//     url: `/finance/flow/claim/getContractListByProject`,
//     method: 'get',
//     params,
//   })
// }

// // 获取合同开票总金额
// export function getContractInvoiceAmount(params) {
//   return request({
//     url: `/finance/flow/claim/getContractInvoiceAmount`,
//     method: 'get',
//     params,
//   })
// }

// // 获取合同已认领总金额
// export function getContractClaimedAmount(params) {
//   return request({
//     url: `/finance/flow/claim/getContractClaimedAmount`,
//     method: 'get',
//     params,
//   })
// }
