<template>
  <Container>
    <TableContainer title="项目终止、挂起">
      <template #search="{ searchBoxWidth }">
        <FilterCriteria
          :popover-width="searchBoxWidth"
          @search="onSearch"
          @reset="onReset">
          <ElForm
            ref="formRef"
            label-position="left"
            label-width="90px"
            :model="filterFormData">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem label="项目编号">
                  <ElInput v-model="filterFormData.projectCode" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="项目名称">
                  <ElInput v-model="filterFormData.projectName" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="申请内容">
                  <ElSelect
                    v-model="filterFormData.applicationContent"
                    :teleported="false"
                    placeholder="请选择">
                    <ElOption
                      label="项目终止"
                      value="项目终止" />
                    <ElOption
                      label="项目临时挂起"
                      value="项目临时挂起" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </FilterCriteria>
      </template>

      <template #toolbar>
        <div>
          <ElButton
            type="primary"
            @click="() => onCreate(null, 'form')">
            新建
          </ElButton>
          <ElButton @click="exportHandler">
            <template #icon>
              <i class="iconfont icon-UploadOutlined" />
            </template>
            下载
          </ElButton>
        </div>
      </template>

      <template #default="{ contentHeight }">
        <ElTable
          :max-height="contentHeight"
          :data="tableData"
          border
          style="width: 100%"
          @row-dblclick="row => onCreate(row, 'view')">
          <ElTableColumn
            prop="reviewStatus"
            label="状态">
            <template #default="{ row }">
              <ElTag :type="getStatusTagType(row.reviewStatus)?.type">
                {{ getStatusTagType(row.reviewStatus)?.text }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="projectCode"
            label="项目编号" />
          <ElTableColumn
            prop="projectName"
            label="项目名称" />
          <ElTableColumn
            prop="applicant"
            label="申请人" />
          <ElTableColumn
            prop="applicationContent"
            label="申请内容" />
          <ElTableColumn
            prop="createTime"
            label="申请日期" />
          <ElTableColumn
            prop="reason"
            label="终止/挂起原因" />
          <ElTableColumn
            fixed="right"
            width="150"
            label="操作类型">
            <template #default="{ row }">
              <ElButton
                v-if="row.reviewStatus === 0"
                type="primary"
                link
                @click="onCreate(row, 'edit')">
                编辑
              </ElButton>
              <ElButton
                v-if="row.reviewStatus === 0"
                type="danger"
                link
                @click="onDelete(row)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <template #footer>
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @current-change="onSearch"
          @size-change="(size) => (onReset({ size, isClear: false }))" />
      </template>
    </TableContainer>
  </Container>
</template>

<script setup>
import { removeProjectEol, selectProjectEolList } from '@/api/project-manage/projectEnd.js'
import Container from '@/components/Container/index.vue'
import TableContainer from '@/components/Container/table-container.vue'
import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'
import { usePagination } from '@/utils/hooks.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const router = useRouter()
const filterFormData = reactive({
  projectCode: '',
  projectName: '',
  applicationContent: '',
})
const tableData = ref([])

const { pagination } = usePagination()

async function onSearch() {
  try {
    const res = await selectProjectEolList({
      page: {
        ...pagination,
      },
      params: {
        ...filterFormData,
      },
    })
    const { total, list } = res.data
    tableData.value = list
    pagination.total = total
  } catch (error) {
    console.log(error)
  }
}
const formRef = useTemplateRef('formRef')
function onReset(data = {}) {
  const config = { isClear: true, size: 10, ...data }
  if (config.isClear) {
    formRef.value.resetFields()
  }
  filterFormData.projectCode = ''
  filterFormData.projectName = ''
  filterFormData.applicationContent = ''
  pagination.pageSize = config.size
  pagination.pageNum = 1
  onSearch()
}

function onCreate(config = {}, pageType = 'form') {
  router.push({
    path: '/project-manage/project-end/apply/form',
    query: {
      ...config,
      pageType,
    },
  })
}

async function onDelete(row) {
  try {
    await ElMessageBox.confirm('确定要删除选中的项目终止吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const ids = [row.id] // 单条删除时取当前行ID
    await removeProjectEol(ids)
    pagination.pageNum = 1
    onSearch() // 刷新列表

    ElMessage.success('删除成功')
  } catch (err) {
    // 用户取消删除时不处理
    console.log(err)
  }
}

// 状态标签样式
function getStatusTagType(status) {
  const statusList = [
    { status: 0, type: 'info', text: '草稿' },
    { status: 1, type: 'warning', text: '待审批' },
    { status: 2, type: 'warning', text: '审批中' },
    { status: 3, type: 'success', text: '审批通过' },
    { status: 4, type: 'danger', text: '审批拒绝' },
    { status: 10, type: 'danger', text: '已撤销' },
    { status: 11, type: '', text: '审批结束' },
    { status: 12, type: '', text: '其他' },
  ]
  return statusList.find(item => item.status === status)
}
// 导出
function exportHandler() {
  proxy.download(
    '/project/eol/export',
    {
      ...filterFormData,
    },
    `项目终止、挂起_${new Date().getTime()}.xlsx`,
  )
}

onMounted(() => {
  onSearch() // 添加首次加载数据
})
</script>

<style lang="scss" scoped>

</style>
