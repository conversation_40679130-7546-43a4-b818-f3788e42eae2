<template>
  <div class="content">
    <ElDescriptions
      border
      title="基本信息"
      label-width="200">
      <ElDescriptionsItem
        v-for="(item, index) in partnerProductInfo"
        :key="index"
        :span="item.span || 1">
        <template #label>
          <div class="cell-item">
            {{ item.label }}
          </div>
        </template>
        <template v-if="item.value === 'partnerTag'">
          <ElTag
            v-for="tag in productInfo[item.value]"
            :key="tag"
            style="margin-right: 10px;">
            {{ tag }}
          </ElTag>
        </template>
        <template v-else>
          {{ productInfo[item.value] }}
        </template>
      </ElDescriptionsItem>
    </ElDescriptions>

    <h1 style="margin-top: 112px;">
      产品及解决方案附件
    </h1>
    <div class="file-list">
      <div class="file-item">
        <ElIcon>
          <Link />
        </ElIcon>
        <span>{{ productInfo.fileName }}</span>
        <div class="action">
          <ElButton
            type="primary"
            link
            @click="handlePreview">
            预览
          </ElButton>
          <ElButton
            type="primary"
            link
            @click="handleDownload">
            下载
          </ElButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getTempProductDetail } from '@/api/ecology/temp-product.js'
import { base64Encode } from '@/utils/base64.js'
import { formatPrice } from '@/views/ecology/product/utils.js'

const { id } = defineProps({
  id: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['init'])

const { proxy } = getCurrentInstance()

const partnerProductInfo = ref([
  {
    label: '产品及解决方案名称',
    value: 'solutionName',
  },
  {
    label: '所属生态伙伴',
    value: 'createBy',
  },
  {
    label: '产品分类',
    value: 'partnerClass',
  },
  {
    label: '是否国产化适配',
    value: 'isDomesticallyAdapted',
  },
  {
    label: '产品是否软著',
    value: 'isProductSoft',
  },
  {
    label: '部署方式',
    value: 'deployment',
  },
  {
    label: '商务模式',
    value: 'businessModel',
    span: 3,
  },
  {
    label: '标准产品包（元）',
    value: 'productPackage',
  },
  {
    label: '产品实施（元）',
    value: 'productImplementation',
  },
  {
    label: '产品定制化（元）',
    value: 'productDevelopment',
  },
  {
    label: '伙伴标签',
    value: 'partnerTag',
    span: 2,
  },
  {
    label: '单位',
    value: 'unit',
  },
])

const productInfo = ref({})
const initLoading = ref(false)
async function getProductDetail() {
  try {
    initLoading.value = true
    const res = await getTempProductDetail({
      productBid: id,
    })
    let partnerClassStr = ''
    if (res.data.partnerClass) {
      const partnerClassList = typeof res.data.partnerClass === 'string' ? JSON.parse(res.data.partnerClass) : res.data.partnerClass
      partnerClassStr = partnerClassList.join('/')
    } else {
      partnerClassStr = `${res.data.firstClassification}/${res.data.secondClassification}/${res.data.threeClassification}`
    }
    productInfo.value = {
      ...res.data,
      isProductSoft: res.data.isProductSoft === 1 ? '是' : '否',
      isDomesticallyAdapted: res.data.isDomesticallyAdapted === 1 ? '是' : '否',
      productDevelopment: formatPrice(res.data.productDevelopment),
      productImplementation: formatPrice(res.data.productImplementation),
      productPackage: formatPrice(res.data.productPackage),
      partnerClass: partnerClassStr,
      partnerTag: typeof res.data.partnerTags === 'string' ? JSON.parse(res.data.partnerTags) : res.data.partnerTags,
    }

    emit('init', productInfo.value)
  } catch (error) {
    console.log(error)
  } finally {
    initLoading.value = false
  }
}

function handlePreview() {
  window.open(`${import.meta.env.VITE_APP_KK_FILE_VIEW_URL}?url=${encodeURIComponent(base64Encode(productInfo.value.fileUrl))}`)
}

function handleDownload() {
  const link = document.createElement('a')
  link.href = productInfo.value.fileUrl
  link.download = productInfo.value.filename // 设置下载的文件名
  fetch(productInfo.value.fileUrl, { method: 'HEAD' })
    .then((response) => {
      if (response.ok) {
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        proxy.$modal.msgError('文件下载失败')
      }
    })
    .catch((error) => {
      console.error('文件下载失败：', error)
      proxy.$modal.msgError('文件下载失败')
    })
}

defineExpose({
  init() {
    getProductDetail()
  },
})
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  overflow: auto;
  padding: 18px 14px;
  border-radius: 10px;
  background: #fff;

  h1 {
    margin-bottom: 10px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 16px;
  }

  .file-list {
    display: flex;
    gap: 16px;

    .file-item {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
      padding: 14px;
      border: 1px solid rgb(0 0 0 / 6%);
      border-radius: 10px;
      line-height: 40px;

      span {
        flex: 1;
        margin: 0 5px;
      }
    }
  }
}
</style>
