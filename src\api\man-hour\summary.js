import { get, post } from '@/utils/alova'
import { download } from '@/utils/request'

const prefix = '/workhour'

/**
 * 项目人力工时汇总
 * @param {*} data
 * @returns
 */
export const getHourSummaryList = data => post(`${prefix}/project/work/summarize/list`, data)

/**
 * 项目人力工时详情
 * @param {*} data
 * @returns
 */
export const getHourSummaryDetail = data => post(`${prefix}/project/work/summarize/detail/${data}`)

/**
 * 项目人力工时详情列表
 * @param {*} data
 * @returns
 */
export const getHourSummaryDetailList = data => post(`${prefix}/project/work/summarize/detail/list`, data)

/**
 * 员工工时详情
 * @param {*} data
 * @returns
 */
export const getStaffHourDetail = data => post(`${prefix}/project/work/summarize/submit/detail`, data)
/**
 * 员工工时列表详情
 * @param {*} data
 * @returns
 */
export const getStaffHourDetailList = data => post(`${prefix}/project/work/summarize/submit/list`, data)

/**
 * 员工工时列表详情
 * @param {*} data
 * @returns
 */
export const getStaffInfoDetail = data => get(`${prefix}/timesheetDetail/${data}`)

/**
 * 项目成本汇总分页
 * @param {*} data
 * @returns
 */
export const getCostSummaryList = data => post(`${prefix}/project/cost/summarize/list`, data)

/**
 * 项目成本汇总详情
 * @param {*} data
 *  @returns
 */
export const getCostSummaryDetail = data => get(`${prefix}/project/cost/summarize/detail/${data}`)

/**
 * 项目成本汇总详情列表
 * @param {*} data
 * @returns
 */
export const getCostSummaryDetailList = data => post(`${prefix}/project/cost/summarize/personnel/list`, data)

export function exportHourSummary(data, filename) {
  return download(`${prefix}/project/work/summarize/list/export`, data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}

export function exportHourMultipleDetail(data, filename) {
  return download(`${prefix}/project/work/summarize/batchDetail/export`, data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}

export function exportHourSummaryDetail(data, filename) {
  return download(`${prefix}/project/work/summarize/detail/export`, data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}
export function exportStaffHourDetail(data, filename) {
  return download(`${prefix}/project/work/summarize/submit/export`, data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}
export function exportCostSummary(data, filename) {
  console.log(data, filename)
  return download(`${prefix}/project/cost/summarize/export`, data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}

export function exportCostSummaryDetail(data, filename) {
  console.log(data, filename)
  return download(`${prefix}//project/cost/summarize/personnel/export`, data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}

export function exportCostMultipleDetail(data, filename) {
  return download(`${prefix}/project/cost/summarize/children/export`, data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}
