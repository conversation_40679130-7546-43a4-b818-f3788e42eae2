<template>
  <div class="content">
    <div
      v-show="route.query.status !== '0' && !route.query.businessId"
      class="code-box">
      业务编号：{{ settlementDetail?.approvalFormNo }}
    </div>
    <div
      :style="
        route.query.status !== '0'
          && !route.query.businessId
          && 'margin-top: 30px'
      ">
      <div class="title">
        结算明细
      </div>
    </div>
    <div
      class="title"
      style="margin-top: 20px">
      结算账单表
    </div>
    <ElDescriptions
      v-loading="descriptionLoading"
      :column="2"
      border
      label-width="150"
      style="margin-bottom: 36px">
      <ElDescriptionsItem
        v-for="(item, index) in descriptionList"
        :key="index"
        label-align="center"
        width="398">
        <template #label>
          <div>{{ item.label }}</div>
        </template>
        {{ settlementDetail?.[item.value] }}
      </ElDescriptionsItem>
    </ElDescriptions>
    <div class="search-box">
      <ElPopover
        :visible="popoverVisible"
        :width="1000"
        placement="bottom-start"
        trigger="click">
        <template #reference>
          <ElButton @click="popoverVisible = !popoverVisible">
            <i
              class="iconfont icon-sift"
              style="margin-right: 6px" />
            <span style="margin-right: 6px">所有筛选</span>
            <ElIcon>
              <ArrowDown />
            </ElIcon>
          </ElButton>
        </template>
        <div style="padding: 15px 8px">
          <ElForm
            ref="searchFormRef"
            :inline="true"
            :model="searchForm">
            <ElFormItem
              label="外包人员名称："
              prop="outsourceName"
              style="width: 360px">
              <ElInput
                v-model="searchForm.outsourceName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
            <ElFormItem
              label="月份："
              prop="month"
              style="width: 360px">
              <ElDatePicker
                v-model="searchForm.month"
                style="width: 100%"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择" />
            </ElFormItem>
          </ElForm>
          <div
            style="
              display: flex;
              justify-content: end;
              width: 100%;
              margin-top: 10px;
            ">
            <ElButton
              type="primary"
              @click="onSearch">
              查询
            </ElButton>
            <ElButton @click="resetSearchForm(searchFormRef)">
              重置
            </ElButton>
          </div>
        </div>
      </ElPopover>
      <ElButton @click="onExportAll">
        <i
          class="iconfont icon-Download"
          style="margin-right: 6px" />
        导出全部
      </ElButton>
    </div>
    <div
      v-show="multipleSelection.length > 0"
      class="check-box">
      <span class="check-count">已选择 {{ multipleSelection.length }} 项</span>
      <div class="check-action">
        <ElButton
          link
          size="small"
          @click="onExportSelected">
          导出所选
        </ElButton>
        <ElButton
          link
          size="small"
          @click="clearSelection">
          取消选择
        </ElButton>
      </div>
    </div>
    <ElTable
      ref="tableRef"
      v-loading="tableLoading"
      :data="settlementDetailList"
      border
      max-height="550"
      style="margin-top: 20px"
      @selection-change="handleSelectionChange">
      <ElTableColumn
        fixed="left"
        type="selection"
        width="40" />
      <ElTableColumn
        label="供应商名称"
        prop="supplierName" />
      <ElTableColumn
        label="月份"
        prop="month" />
      <ElTableColumn
        label="姓名"
        prop="outsourceName" />
      <ElTableColumn
        label="人员编号"
        prop="outsourceNo" />
      <ElTableColumn
        label="联系方式"
        prop="outsourcePhone" />
      <ElTableColumn
        label="部门"
        prop="deptName" />
      <ElTableColumn
        label="岗位"
        prop="position" />
      <ElTableColumn
        label="岗类"
        prop="positionType" />
      <ElTableColumn
        label="岗级"
        prop="positionLevel" />
      <ElTableColumn
        label="项目名称"
        prop="projectName" />
      <ElTableColumn
        label="项目编号"
        prop="projectNumber" />
      <ElTableColumn
        label="入项时间"
        prop="entryTime" />
      <ElTableColumn
        label="下项时间"
        prop="quitTime" />
      <ElTableColumn
        label="当月外包总费用(元)"
        prop="outsourceMonthFee" />
      <ElTableColumn
        label="标准月薪(元)"
        prop="standardSalary" />
      <ElTableColumn
        label="当月应出勤天数(天)"
        prop="requiredAttendance" />
      <ElTableColumn
        label="当月实际出勤天数(天)"
        prop="actualAttendance" />
      <ElTableColumn
        label="当月缺勤天数(天)"
        prop="absenteeism" />
      <ElTableColumn
        label="实际计算月薪(元)"
        prop="actualMonthSalary" />
      <ElTableColumn
        label="社保公积金(公司承担)(元)"
        prop="socialSecurityFund" />
      <ElTableColumn
        label="节假补贴(元)"
        prop="holidayAllowance" />
      <ElTableColumn
        label="绩效/奖励等(元)"
        prop="performance" />
      <ElTableColumn
        label="外包综合费用小计(元)"
        prop="comprehensiveCosts" />
      <ElTableColumn
        label="费率"
        prop="rate" />
      <ElTableColumn
        label="外包服务费用小计(元)"
        prop="serviceCharge" />
      <ElTableColumn
        label="住宿(元)"
        prop="accommodationFee" />
      <ElTableColumn
        label="交通(元)"
        prop="trafficFee" />
      <ElTableColumn
        label="交通补助(元)"
        prop="trafficAllowance" />
      <ElTableColumn
        label="伙食补助(元)"
        prop="foodAllowance" />
      <ElTableColumn
        label="公杂费(元)"
        prop="miscellaneousFee" />
      <ElTableColumn
        label="差旅费用小计(元)"
        prop="travelExpense" />
      <ElTableColumn
        label="备注"
        prop="remark" />
      <ElTableColumn
        v-if="route.query.status === '0'"
        label="操作"
        fixed="right">
        <template #default="{ row }">
          <ElButton
            link
            type="primary"
            @click="onOpenDrawer(row)">
            编辑
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
    <div class="pagination">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
  <ElDrawer
    v-model="drawerVisible"
    destroy-on-close
    :size="408"
    :show-close="false">
    <template #header="{ close }">
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        ">
        <div style="display: flex; align-items: center">
          <ElButton
            link
            size="small"
            @click="close">
            <i
              class="iconfont icon-CloseOutlined"
              style="color: rgb(0 0 0 / 45%); font-size: 14px" />
          </ElButton>
          <div
            style="
              margin-top: -2px;
              margin-left: 16px;
              color: rgb(0 0 0 / 88%);
              font-weight: 600;
            ">
            编辑
          </div>
        </div>
        <ElButton
          type="primary"
          @click="onSubmitEditForm(editFormRef)">
          提交
        </ElButton>
      </div>
    </template>
    <template #default>
      <ElForm
        ref="editFormRef"
        :rules="editFormRules"
        label-position="top"
        :model="editForm">
        <ElFormItem
          label="供应商名称："
          prop="supplierName">
          <ElInput
            v-model="editForm.supplierName"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="月份："
          prop="month">
          <ElDatePicker
            v-model="editForm.month"
            style="width: 100%"
            type="month"
            value-format="YYYY-MM"
            placeholder="请选择" />
        </ElFormItem>
        <ElFormItem
          label="姓名："
          prop="outsourceName">
          <ElInput
            v-model="editForm.outsourceName"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="人员编号："
          prop="outsourceNo">
          <ElInput
            v-model="editForm.outsourceNo"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="联系方式："
          prop="outsourcePhone">
          <ElInput
            v-model="editForm.outsourcePhone"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="部门："
          prop="deptName">
          <ElSelect
            v-model="editForm.deptName"
            clearable
            placeholder="请输入"
            @click="onOpenOrgPicker" />
        </ElFormItem>
        <ElFormItem
          label="岗位："
          prop="position">
          <ElInput
            v-model="editForm.position"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="岗类："
          prop="positionType">
          <ElInput
            v-model="editForm.positionType"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="岗级："
          prop="positionLevel">
          <ElInput
            v-model="editForm.positionLevel"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="项目名称："
          prop="projectName">
          <ElInput
            v-model="editForm.projectName"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="项目编号："
          prop="projectNumber">
          <ElInput
            v-model="editForm.projectNumber"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="入项时间："
          prop="entryTime">
          <ElDatePicker
            v-model="editForm.entryTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择"
            style="width: 100%" />
        </ElFormItem>
        <ElFormItem
          label="下项时间："
          prop="quitTime">
          <ElDatePicker
            v-model="editForm.quitTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择"
            style="width: 100%" />
        </ElFormItem>
        <ElFormItem
          label="当月外包总费用(元)："
          prop="outsourceMonthFee">
          <ElInput
            v-model="editForm.outsourceMonthFee"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="标准月薪(元)："
          prop="standardSalary">
          <ElInput
            v-model="editForm.standardSalary"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="当月应出勤天数(天)："
          prop="requiredAttendance">
          <ElInput
            v-model="editForm.requiredAttendance"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="当月实际出勤天数(天)："
          prop="actualAttendance">
          <ElInput
            v-model="editForm.actualAttendance"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="当月缺勤天数(天)："
          prop="absenteeism">
          <ElInput
            v-model="editForm.absenteeism"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="实际计算月薪(元)："
          prop="actualMonthSalary">
          <ElInput
            v-model="editForm.actualMonthSalary"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="社保公积金(公司承担)(元)："
          prop="socialSecurityFund">
          <ElInput
            v-model="editForm.socialSecurityFund"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="节假补贴(元)："
          prop="holidayAllowance">
          <ElInput
            v-model="editForm.holidayAllowance"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="绩效/奖励等(元)："
          prop="performance">
          <ElInput
            v-model="editForm.performance"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="外包综合费用小计(元)："
          prop="comprehensiveCosts">
          <ElInput
            v-model="editForm.comprehensiveCosts"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="费率："
          prop="rate">
          <ElInput
            v-model="editForm.rate"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="外包服务费用小计(元)："
          prop="serviceCharge">
          <ElInput
            v-model="editForm.serviceCharge"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="住宿(元)："
          prop="accommodationFee">
          <ElInput
            v-model="editForm.accommodationFee"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="交通(元)："
          prop="trafficFee">
          <ElInput
            v-model="editForm.trafficFee"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="交通补助(元)："
          prop="trafficAllowance">
          <ElInput
            v-model="editForm.trafficAllowance"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="伙食补助(元)："
          prop="foodAllowance">
          <ElInput
            v-model="editForm.foodAllowance"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="公杂费(元)："
          prop="miscellaneousFee">
          <ElInput
            v-model="editForm.miscellaneousFee"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="差旅费用小计(元)："
          prop="travelExpense">
          <ElInput
            v-model="editForm.travelExpense"
            type="number"
            clearable
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          label="备注："
          prop="remark">
          <ElInput
            v-model="editForm.remark"
            type="textarea"
            :rows="3"
            clearable
            placeholder="请输入" />
        </ElFormItem>
      </ElForm>
    </template>
  </ElDrawer>
  <OrgPicker
    ref="orgPickerRef"
    type="dept"
    @ok="onDeptSelectOk" />
</template>

<script setup>
import * as api from '@/api/outsource/settlement.js'
import { descriptionList } from '@/views/outsource/settlement/config.js'
import { ArrowDown } from '@element-plus/icons-vue'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'

const route = useRoute()

const popoverVisible = ref(false)
const drawerVisible = ref(false)
const searchFormRef = ref()
const tableRef = ref()
const editFormRef = ref()
const orgPickerRef = ref()
const tableLoading = ref(false)
const descriptionLoading = ref(false)
const total = ref(0)
const multipleSelection = ref([])
const settlementDetail = ref({})
const settlementDetailList = ref([])

const searchForm = reactive({
  outsourceName: '',
  month: '',
})
const editForm = reactive({
  id: '',
  bid: '',
  supplierName: '',
  month: '',
  outsourceName: '',
  outsourceNo: '',
  outsourcePhone: '',
  deptName: '',
  position: '',
  positionType: '',
  positionLevel: '',
  projectName: '',
  projectNumber: '',
  entryTime: '',
  quitTime: '',
  outsourceMonthFee: '',
  standardSalary: '',
  requiredAttendance: '',
  actualAttendance: '',
  absenteeism: '',
  actualMonthSalary: '',
  socialSecurityFund: '',
  holidayAllowance: '',
  performance: '',
  comprehensiveCosts: '',
  rate: '',
  serviceCharge: '',
  accommodationFee: '',
  trafficFee: '',
  trafficAllowance: '',
  foodAllowance: '',
  miscellaneousFee: '',
  travelExpense: '',
  remark: '',
})
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
})
const editFormRules = reactive({
  outsourceNo: [{ required: true, message: '请输入人员编号', trigger: 'blur' }],
  deptName: [{ required: true, message: '请选择部门', trigger: 'blur' }],
  projectNumber: [
    { required: true, message: '请输入项目编号', trigger: 'blur' },
  ],
  outsourceMonthFee: [
    { required: true, message: '请输入当月外包总费用(元)', trigger: 'blur' },
  ],
})

function onSearch() {
  popoverVisible.value = false
  getSettlementDetailList()
}

function resetSearchForm(formEl) {
  if (!formEl)
    return
  formEl.resetFields()
  popoverVisible.value = false
  getSettlementDetailList()
}

function handleSizeChange(val) {
  pagination.pageSize = val
}

function handleCurrentChange(val) {
  pagination.pageNum = val
}

function clearSelection() {
  tableRef.value.clearSelection()
}

function handleSelectionChange(val) {
  multipleSelection.value = val
}

function onOpenDrawer(row) {
  drawerVisible.value = true
  for (const key in editForm) {
    editForm[key] = row[key]
  }
}

async function onSubmitEditForm(formEl) {
  if (!formEl)
    return
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        await api.updateSettlementDetail(editForm)
        ElMessage.success('提交成功')
        drawerVisible.value = false
        await getSettlementDetailList()
      } catch (error) {
        console.log(error)
      }
    }
  })
}

async function getSettlementDetail() {
  descriptionLoading.value = true
  try {
    const res = await api.getSettlementDetail(
      route.query.bid || route.query.businessId,
    )
    settlementDetail.value = res.data || {}
  } catch (error) {
    console.log(error)
  } finally {
    descriptionLoading.value = false
  }
}

async function getSettlementDetailList() {
  tableLoading.value = true
  try {
    const res = await api.getSettlementDetailList(
      route.query.bid || route.query.businessId,
      searchForm,
    )
    settlementDetailList.value = res.rows
    total.value = +res.total
  } catch (error) {
    console.log(error)
  } finally {
    tableLoading.value = false
  }
}

function onOpenOrgPicker() {
  orgPickerRef.value.show()
}

function onDeptSelectOk(data) {
  editForm.deptName = data[0].name
  // editForm.deptId = +data[0].id
}

function onExportAll() {
  exportSettlementDetail({
    exportAll: 1,
  })
}

function onExportSelected() {
  exportSettlementDetail({
    bids: multipleSelection.value.map(item => item.bid),
  })
}

async function exportSettlementDetail(data) {
  try {
    const res = await api.exportSettlementDetail(
      route.query.bid || route.query.businessId,
      data,
    )
    downloadAsExcel(res, '结算明细.xlsx')
  } catch (error) {
    console.log(error)
  }
}

function downloadAsExcel(data, name) {
  // data是二进制流，通过new Blob方法转化为Blob,type是下载文件格式，本方法以excel为例
  // name是你想下载的文件名
  const url = window.URL.createObjectURL(
    new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }),
  )
  // 如果后端返回的是Blob,则不需要用上面的方法new Blob直接用data
  // const url = window.URL.createObjectURL(data);
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', `${name}` || 'template.xlsx')
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

onMounted(() => {
  getSettlementDetail()
  getSettlementDetailList()
})

defineExpose({
  async getFormData() {},
  async saveFormData() {},
})
</script>

<style scoped lang="scss">
.content {
  position: relative;
  overflow: auto;
  height: 100%;
  padding: 18px 14px;
  border-radius: 10px;
  background: #fff;

  .code-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 8px 12px;
    background: #e6f4ff;
    color: rgb(0 0 0 / 88%);
    font-size: 14px;
  }

  .title {
    margin-bottom: 12px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .search-box {
    padding: 20px 0;
    border-bottom: 1px solid rgb(0 0 0 / 6%);
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 20px;
  }

  .check-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44px;
    margin-top: 20px;
    padding: 0 24px;
    border-radius: 4px;
    background: rgb(0 0 0 / 4%);

    .check-count {
      color: rgb(0 0 0 / 45%);
      font-size: 14px;
    }
  }
}
</style>
