<template>
  <Container
    show-back
    back-title="新建">
    <template #headerRight>
      <div class="ml-auto">
        <ElButton
          type="info"
          @click="handleDraft">
          暂存
        </ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit">
          提交
        </ElButton>
      </div>
    </template>
    <DocsForm
      :id="route.query.id"
      ref="DocsFormRef"
      :form-type="route.query?.pageType" />
  </Container>
</template>

<script setup>
import Container from '@/components/Container/index.vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import DocsForm from '../components/docs-form.vue'

const route = useRoute()
const router = useRouter()
const DocsFormRef = useTemplateRef('DocsFormRef')

async function handleSubmit() {
  try {
    DocsFormRef.value.onSave().then(() => {
      ElMessage.success('保存成功')
      router.back()
    }).catch((err) => {
      console.log(err)
    })
  } catch (error) {
    console.log(error)
  }
}
function handleDraft() {
  try {
    DocsFormRef.value.onSaveDraft().then(() => {
      ElMessage.success('暂存成功')
      router.back()
    }).catch((err) => {
      console.log(err)
    })
  } catch (error) {
    console.log(error)
  }
}
</script>
