<template>
  <Title style="margin-top: 20px;">
    项目预算信息（变更前）
  </Title>
  <ElDescriptions
    border
    :column="4"
    label-width="150">
    <ElDescriptionsItem
      label="预算金额（万元）">
      {{ form.prdProjectHistory.budgetAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="外采成本（万元）">
      {{ form.prdProjectHistory.budgetOutsourcingCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="人力成本（万元）">
      {{ form.prdProjectHistory.budgetJobLaborCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目管理费（万元）">
      {{ form.prdProjectHistory.budgetProjectManagerCostAmount }}
    </ElDescriptionsItem>
  </ElDescriptions>
  <ElTabs style="margin-top: 20px;">
    <ElTabPane label="概览">
      <ElTable
        :data="historyData"
        border
        :span-method="objectSpanMethodForHistory">
        <ElTableColumn
          label="项目"
          prop="project" />
        <ElTableColumn
          label="分项"
          prop="itemsize" />
        <ElTableColumn
          label="含税金额（元）"
          prop="taxInclusiveAmount" />
        <ElTableColumn
          label="不含税金额（元）"
          prop="excludingTaxAmount" />
      </ElTable>
    </ElTabPane>
    <ElTabPane label="外采成本">
      <ElTable
        border
        style="width: 100%;"
        :data="form.prdProjectHistory.budgetInfo.outsourcingCost || []"
        show-summary
        :summary-method="wccbSummaryMethod">
        <ElTableColumn
          v-for="(item, index) in wccbTableColumnInfo"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          align="center" />
      </ElTable>
    </ElTabPane>
    <ElTabPane label="人力成本">
      <ElTable
        border
        style="width: 100%;"
        :data="form.prdProjectHistory.budgetInfo.jobLaborCost || []"
        show-summary
        :summary-method="rlcbSummaryMethod">
        <ElTableColumn
          v-for="(item, index) in rlcbTableColumnInfo"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          align="center" />
      </ElTable>
    </ElTabPane>
    <ElTabPane label="项目管理费">
      <ElTable
        border
        style="width: 100%;"
        :data="form.prdProjectHistory.budgetInfo.manageCost || []"
        show-summary
        :summary-method="xmglfSummaryMethod">
        <ElTableColumn
          label="科目"
          prop="subject"
          align="center" />
        <ElTableColumn
          label="金额（元）"
          prop="amount"
          align="center">
          <template #default="{ row }">
            <ElInput
              v-if="row.isEdit"
              v-model.number="row.amount"
              type="number" />
            <span v-else>
              {{ row.amount }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="备注"
          prop="remark"
          align="center">
          <template #default="{ row }">
            <ElInput
              v-if="row.isEdit"
              v-model="row.remark" />
            <span v-else>
              {{ row.remark }}
            </span>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElTabPane>
  </ElTabs>
  <Title style="margin-top: 20px;">
    项目预算信息（变更后）
  </Title>
  <ElDescriptions
    border
    :column="4"
    label-width="150">
    <ElDescriptionsItem
      label="预算金额（万元）">
      {{ form.projectChangeDraft.budgetAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="外采成本（万元）">
      {{ form.projectChangeDraft.budgetOutsourcingCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="人力成本（万元）">
      {{ form.projectChangeDraft.budgetJobLaborCostAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目管理费（万元）">
      {{ form.projectChangeDraft.budgetProjectManagerCostAmount }}
    </ElDescriptionsItem>
  </ElDescriptions>
  <ElTabs style="margin-top: 20px;">
    <ElTabPane label="概览">
      <ElTable
        :data="changeData"
        border
        :span-method="objectSpanMethodForChange">
        <ElTableColumn
          label="项目"
          prop="project" />
        <ElTableColumn
          label="分项"
          prop="itemsize" />
        <ElTableColumn
          label="含税金额（元）"
          prop="taxInclusiveAmount" />
        <ElTableColumn
          label="不含税金额（元）"
          prop="excludingTaxAmount" />
      </ElTable>
    </ElTabPane>
    <ElTabPane label="外采成本">
      <ElTable
        border
        style="width: 100%;"
        :data="form.projectChangeDraft.budgetInfo.outsourcingCost || []"
        show-summary
        :summary-method="wccbSummaryMethod">
        <ElTableColumn
          v-for="(item, index) in wccbTableColumnInfo"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          align="center" />
      </ElTable>
    </ElTabPane>
    <ElTabPane label="人力成本">
      <ElTable
        border
        style="width: 100%;"
        :data="form.projectChangeDraft.budgetInfo.jobLaborCost || []"
        show-summary
        :summary-method="rlcbSummaryMethod">
        <ElTableColumn
          v-for="(item, index) in rlcbTableColumnInfo"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          align="center" />
      </ElTable>
    </ElTabPane>
    <ElTabPane label="项目管理费">
      <ElTable
        border
        style="width: 100%;"
        :data="form.projectChangeDraft.budgetInfo.manageCost || []"
        show-summary
        :summary-method="xmglfSummaryMethod">
        <ElTableColumn
          label="科目"
          prop="subject"
          align="center" />
        <ElTableColumn
          label="金额（元）"
          prop="amount"
          align="center">
          <template #default="{ row }">
            <ElInput
              v-if="row.isEdit"
              v-model.number="row.amount"
              type="number" />
            <span v-else>
              {{ row.amount }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="备注"
          prop="remark"
          align="center">
          <template #default="{ row }">
            <ElInput
              v-if="row.isEdit"
              v-model="row.remark" />
            <span v-else>
              {{ row.remark }}
            </span>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElTabPane>
  </ElTabs>
</template>

<script setup lang="jsx">
import Title from '@/components/Title/index.vue'

const form = defineModel()

const historyData = computed(() => {
  let data = [
    { project: '外采成本', itemsize: '外采成本', taxInclusiveAmount: form.value.prdProjectHistory.budgetInfo.budgetOverview.outsourcingCostSumTaxAmount || 0, excludingTaxAmount: form.value.prdProjectHistory.budgetInfo.budgetOverview.outsourcingCostSumNotTaxAmount || 0 },
    { project: '人力成本', itemsize: '自有人力成本', taxInclusiveAmount: form.value.prdProjectHistory.budgetInfo.budgetOverview.ownPersonnelCostTaxAmount || 0, excludingTaxAmount: '/' },
    { project: '人力成本', itemsize: '外包外采成本', taxInclusiveAmount: form.value.prdProjectHistory.budgetInfo.budgetOverview.outsourcedStaffCostTaxAmount || 0, excludingTaxAmount: '/' },
  ]
  const project_subject_data = form.value.prdProjectHistory.budgetInfo.budgetOverview.manageCost.map(item => ({ project: '项目管理费', itemsize: item.costName, taxInclusiveAmount: item.taxAmount, excludingTaxAmount: '/' }))
  data = [...data, ...project_subject_data]
  return data
})

const changeData = computed(() => {
  let data = [
    { project: '外采成本', itemsize: '外采成本', taxInclusiveAmount: form.value.projectChangeDraft.budgetInfo.budgetOverview.outsourcingCostSumTaxAmount || 0, excludingTaxAmount: form.value.projectChangeDraft.budgetInfo.budgetOverview.outsourcingCostSumNotTaxAmount || 0 },
    { project: '人力成本', itemsize: '自有人力成本', taxInclusiveAmount: form.value.projectChangeDraft.budgetInfo.budgetOverview.ownPersonnelCostTaxAmount || 0, excludingTaxAmount: '/' },
    { project: '人力成本', itemsize: '外包外采成本', taxInclusiveAmount: form.value.projectChangeDraft.budgetInfo.budgetOverview.outsourcedStaffCostTaxAmount || 0, excludingTaxAmount: '/' },
  ]
  const project_subject_data = form.value.projectChangeDraft.budgetInfo.budgetOverview.manageCost.map(item => ({ project: '项目管理费', itemsize: item.costName, taxInclusiveAmount: item.taxAmount, excludingTaxAmount: '/' }))
  data = [...data, ...project_subject_data]
  return data
})

const wccbTableColumnInfo = [
  {
    label: '标包',
    prop: 'standardPackage',
  },
  {
    label: '子项科目',
    prop: 'subitemSubject',
  },
  {
    label: '采购名称',
    prop: 'procurementName',
  },
  {
    label: '技术规格、型号或采购内容说明',
    prop: 'specificationsModels',
  },
  {
    label: '单位',
    prop: 'unit',
  },
  {
    label: '数量',
    prop: 'quantity',
  },
  {
    label: '销售单价（元）',
    prop: 'unitSellingPrice',
  },
  // {
  //   label: '折扣',
  //   prop: 'discountRate',
  // },
  // {
  //   label: '折扣后销售单价（元）',
  //   prop: 'discountedPrice',
  // },
  {
    label: '预计采购单价（含税：元）',
    prop: 'taxPredictPurchasePrice',
  },
  {
    label: '预计采购总价（含税：元）',
    prop: 'taxPredictPurchaseTotalPrice',
  },
  {
    label: '税率%',
    prop: 'taxRate',
  },
  {
    label: '预计采购单价（不含税：元）',
    prop: 'notTaxPredictPurchasePrice',
  },
  {
    label: '预计采购总价（不含税：元）',
    prop: 'notTaxPredictPurchaseTotalPrice',
  },
  {
    label: '预计采购日期',
    prop: 'predictDate',
  },
]

const rlcbTableColumnInfo = [
  {
    label: '团队角色',
    prop: 'teamRole',
  },
  {
    label: '人员类型',
    prop: 'personType',
  },
  {
    label: '人数',
    prop: 'personNumber',
  },
  {
    label: '人员投入估算（人天）',
    prop: 'estimateHumanDays',
  },
  {
    label: '单价（元/人天）',
    prop: 'unitPrice',
  },
  {
    label: '总价（元）',
    prop: 'totalPrice',
  },
  {
    label: '备注',
    prop: 'remark',
  },
]

function objectSpanMethodForHistory({ rowIndex, columnIndex }) {
  if (columnIndex === 0) {
    switch (rowIndex) {
      case 0: return [1, 1] // 第1行不向下也不向右合并
      case 1: return [2, 1] // 第2行向下合并1行，不向右合并
      case 3: return [historyData.value.length - 1, 1] // 第4行向下合并项目管理费所配置的字典length-1行，不向右合并
      default: return [0, 0] // 第3行被第2行合并，第4行以后的数据被第4行向下所合并，所以需要隐藏
    }
  }
}

function objectSpanMethodForChange({ rowIndex, columnIndex }) {
  if (columnIndex === 0) {
    switch (rowIndex) {
      case 0: return [1, 1] // 第1行不向下也不向右合并
      case 1: return [2, 1] // 第2行向下合并1行，不向右合并
      case 3: return [changeData.value.length - 1, 1] // 第4行向下合并项目管理费所配置的字典length-1行，不向右合并
      default: return [0, 0] // 第3行被第2行合并，第4行以后的数据被第4行向下所合并，所以需要隐藏
    }
  }
}

function wccbSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map(item => Number(item[column.property]))
    if (column.property === 'taxPredictPurchaseTotalPrice' || column.property === 'notTaxPredictPurchaseTotalPrice') {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
    }
  })
  return sums
}

function rlcbSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      // 通过<br/>实现换行，达到2行的效果,
      // 注意需要安装jsx依赖，并在script标签声明lang="jsx"
      sums[index] = (
        <p>
          汇总
          <br />
          <br />
          <br />
          <br />
          合计
        </p>
      )
    }
    if (column.property === 'personType') {
      sums[index] = (
        <p>
          自有
          <br />
          <br />
          外包
          <br />
          <br />
          <br />
        </p>
      )
    }
    if (column.property === 'personNumber') {
      // 自有人数
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.personNumber)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.personNumber)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
    }

    if (column.property === 'estimateHumanDays') {
      // 自有人数
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.estimateHumanDays)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.estimateHumanDays)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
    }

    if (column.property === 'unitPrice') {
      // 自有人数
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.unitPrice)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.unitPrice)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
    }

    if (column.property === 'totalPrice') {
      // 自有人数总价
      const self = data.reduce((prev, curr) => {
        const value = Number(curr.totalPrice)
        if (!Number.isNaN(value) && curr.personType === '自有') {
          return prev + value
        } else {
          return prev
        }
      }, 0)

      // 外包人数总价
      const out = data.reduce((prev, curr) => {
        const value = Number(curr.totalPrice)
        if (!Number.isNaN(value) && curr.personType === '外包') {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = (
        <p>
          {self}
          <br />
          <br />
          {out}
          <br />
          <br />
          {self + out}
        </p>
      )
    }
  })
  return sums
}

function xmglfSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map(item => Number(item[column.property]))
    if (column.property === 'amount') {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
    }
  })
  return sums
}
</script>

<style lang="scss" scoped>

</style>
