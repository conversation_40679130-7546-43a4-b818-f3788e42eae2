<template>
  <Container>
    <TableContainer title="项目结项">
      <template #search="{ searchBoxWidth }">
        <FilterCriteria
          :popover-width="searchBoxWidth"
          @search="onSearch"
          @reset="onReset">
          <ElForm
            ref="formRef"
            label-position="left"
            label-width="90px"
            :model="filterFormData">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem label="项目名称">
                  <ElInput v-model="filterFormData.projectName" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="项目编号">
                  <ElInput v-model="filterFormData.projectCode" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="结项类型">
                  <ElSelect
                    v-model="filterFormData.knotType"
                    :teleported="false"
                    placeholder="请选择">
                    <ElOption
                      v-for="item in project_finish_type"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </FilterCriteria>
      </template>

      <template #toolbar>
        <div>
          <ElButton
            type="primary"
            @click="() => onCreate(null, 'form')">
            新建
          </ElButton>
          <ElButton @click="exportHandler">
            <template #icon>
              <i class="iconfont icon-UploadOutlined" />
            </template>
            下载
          </ElButton>
        </div>
      </template>

      <template #default="{ contentHeight }">
        <ElTable
          :max-height="contentHeight"
          :data="tableData"
          border
          style="width: 100%"
          @row-dblclick="row => onCreate(row, 'view')">
          <ElTableColumn
            align="center"
            prop="reviewStatus"
            label="状态">
            <template #default="{ row }">
              <ElTag :type="getStatusTagType(row.reviewStatus)?.type">
                {{ getStatusTagType(row.reviewStatus)?.text }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="projectCode"
            label="项目编号" />
          <ElTableColumn
            prop="projectName"
            label="项目名称" />
          <ElTableColumn
            prop="applicant"
            label="申请人" />
          <ElTableColumn
            prop="projectManager"
            label="项目经理" />
          <ElTableColumn
            prop="applicantDept"
            label="解决方案部门" />
          <ElTableColumn
            prop="knotDate"
            label="结项日期" />
          <!-- <ElTableColumn
            prop="knotType"
            label="结项类型" /> -->
          <ElTableColumn
            prop="knotType"
            label="结项类型">
            <template #default="{ row }">
              {{
                project_finish_type.find(item =>
                  item.value === row.knotType)?.label || row.knotType
              }}
            </template>
          </ElTableColumn>
          <ElTableColumn
            fixed="right"
            width="150"
            label="操作类型">
            <template #default="{ row }">
              <ElButton
                v-if="row.reviewStatus === 0"
                type="primary"
                link
                @click="onCreate(row, 'edit')">
                编辑
              </ElButton>
              <ElButton
                v-if="row.reviewStatus === 0"
                type="danger"
                link
                @click="onDelete(row)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <template #footer>
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @current-change="onSearch"
          @size-change="(size) => (onReset({ size, isClear: false }))" />
      </template>
    </TableContainer>
  </Container>
</template>

<script setup>
import { removeProjectKnot, selectProjectKnotList } from '@/api/project-manage/projectFinish.js'
import Container from '@/components/Container/index.vue'
import TableContainer from '@/components/Container/table-container.vue'
import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'
import { usePagination } from '@/utils/hooks.js'
import { ElMessageBox, ElMessage} from 'element-plus'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
// const { project_finish_type } = proxy.useDict('project_finish_type')
const project_finish_type = [
  {
    value: 1,
    label: '正常结项',
  },
  {
    value: 2,
    label: '异常结项',
  },
]
const router = useRouter()
const filterFormData = reactive({
  projectCode: '',
  projectName: '',
  knotType: '',
})
const tableData = ref([])

const { pagination } = usePagination()

async function onSearch() {
  try {
    const res = await selectProjectKnotList({
      page: {
        ...pagination,
      },
      params: {
        ...filterFormData,
      },
    })
    const { total, list } = res.data
    tableData.value = list
    pagination.total = total
  } catch (error) {
    console.log(error)
  }
}
const formRef = useTemplateRef('formRef')
function onReset(data = {}) {
  const config = { isClear: true, size: 10, ...data }
  if (config.isClear) {
    console.log(formRef)
    formRef.value.resetFields()
  }
  filterFormData.projectName = ''
  filterFormData.projectCode = ''
  filterFormData.knotType = null
  pagination.pageSize = config.size
  pagination.pageNum = 1
  onSearch()
}

function onCreate(config = {}, pageType = 'form') {
  router.push({
    path: '/project-manage/project-finish/apply/form',
    query: {
      ...config,
      pageType,
    },
  })
}

async function onDelete(row) {
  try {
    await ElMessageBox.confirm('确定要删除选中的项目结项吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const ids = [row.id] // 单条删除时取当前行ID
    await removeProjectKnot(ids)
    pagination.pageNum = 1
    onSearch() // 刷新列表

    ElMessage.success('删除成功')
  } catch (err) {
    // 用户取消删除时不处理
    console.log(err)
  }
}

// 状态标签样式
function getStatusTagType(status) {
  const statusList = [
    { status: 0, type: 'info', text: '草稿' },
    { status: 1, type: 'warning', text: '待审批' },
    { status: 2, type: 'warning', text: '审批中' },
    { status: 3, type: 'success', text: '审批通过' },
    { status: 4, type: 'danger', text: '审批拒绝' },
    { status: 10, type: 'danger', text: '已撤销' },
    { status: 11, type: '', text: '审批结束' },
    { status: 12, type: '', text: '其他' },
  ]
  return statusList.find(item => item.status === status)
}
// 导出
function exportHandler() {
  proxy.download(
    '/project/knot/export',
    {
      ...filterFormData,
    },
    `项目结项_${new Date().getTime()}.xlsx`,
  )
}

onMounted(() => {
  onSearch() // 添加首次加载数据
})
</script>

<style lang="scss" scoped>

</style>
