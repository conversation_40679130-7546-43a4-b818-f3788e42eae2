<template>
  <ElDialog
    v-model="model"
    @open="onOpen">
    <template #header>
      <Title is-hidden>
        外采成本
      </Title>
    </template>
    <ElForm
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      label-position="left">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem
            prop="standardPackage"
            label="标包">
            <ElInput
              v-model="form.standardPackage"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="subitemSubject"
            label="子项科目">
            <ElSelect v-model="form.subitemSubject">
              <ElOption
                v-for="item in expense_account"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="采购名称"
            prop="procurementName">
            <ElInput
              v-model="form.procurementName"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="技术规格、型号"
            prop="specificationsModels">
            <ElInput
              v-model="form.specificationsModels"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="单位"
            prop="unit">
            <ElSelect v-model="form.unit">
              <ElOption
                v-for="item in wccb_unit"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="数量"
            prop="quantity">
            <ElInput
              v-model.number="form.quantity"
              type="number"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="销售单价(元)"
            prop="unitSellingPrice">
            <ElInput
              v-model.number="form.unitSellingPrice"
              type="number"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <!-- <ElCol :span="12">
          <ElFormItem
            label="折扣"
            prop="discountRate">
            <ElInputNumber
              v-model="form.discountRate"
              placeholder="请输入折扣"
              style="width: 100%;"
              class="custom_input_number"
              :step="0.01"
              :precision="2"
              controls-position="right"
              type="number"
              :min="0"
              :max="1">
              <template #decrease-icon>
                <ElIcon>
                  <Minus />
                </ElIcon>
              </template>
              <template #increase-icon>
                <ElIcon>
                  <Plus />
                </ElIcon>
              </template>
            </ElInputNumber>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="折扣后销售单价"
            prop="discountedPrice">
            <ElInput
              v-model="form.discountedPrice"
              disabled />
          </ElFormItem>
        </ElCol> -->
        <ElCol :span="12">
          <ElFormItem
            label="税率"
            prop="taxRate">
            <ElInput
              v-model.number="form.taxRate"
              type="number">
              <template #append>
                %
              </template>
            </ElInput>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="预计采购日期"
            prop="predictDate">
            <ElDatePicker
              v-model="form.predictDate"
              value-format="YYYY-MM-DD"
              placeholder="请选择预计采购日期"
              style="width: 100%"
              type="date" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label-width="180"
            label="预计采购单价(含税:元)"
            prop="taxPredictPurchasePrice">
            <ElInput
              v-model="form.taxPredictPurchasePrice"
              type="number"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label-width="180"
            label="预计采购总价(不含税:元)"
            prop="notTaxPredictPurchaseTotalPrice">
            <ElInput
              v-model="form.notTaxPredictPurchaseTotalPrice"
              disabled />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label-width="180"
            label="预计采购单价(不含税:元)"
            prop="notTaxPredictPurchasePrice">
            <ElInput
              v-model="form.notTaxPredictPurchasePrice"
              disabled />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label-width="180"
            label="预计采购总价(含税:元)"
            prop="taxPredictPurchaseTotalPrice">
            <ElInput
              v-model="form.taxPredictPurchaseTotalPrice"
              disabled />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton @click="onCancel">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="onConfirm">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import Title from '@/components/Title'
import { isEmpty } from '@/utils/hooks'
import { isNumber, multiply, toFixedAccurate, toNumber } from '@/utils/math'
import regexp from '@/utils/regexp'
import { ElButton, ElCol, ElDialog, ElForm, ElFormItem, ElInput, ElRow } from 'element-plus'

const { info } = defineProps({
  info: {
    type: [Object, null],
    default: null,
  },
})
const emit = defineEmits(['confirm', 'cancel'])
const { proxy } = getCurrentInstance()
const { expense_account, wccb_unit } = proxy.useDict('expense_account', 'wccb_unit')
/**
 * 外采成本:预计采购总价(含税:元)求和、预计采购总价(不含税:元)求和
 * 数量*预计采购单价(含税:元)求和、数量*预计采购单价(不含税:元)求和
 * 预计采购单价(不含岁) = 预计采购单价(含税:元) / (1 + 税率)
 * 预计采购总价(不含税:元) = 预计采购总价(含税:元) / (1 + 税率)
 */
const rules = reactive({
  standardPackage: [{ required: true, message: '请输入标包', trigger: ['blur', 'change'] }],
  subitemSubject: [{ required: true, message: '请选择子项科目', trigger: 'change' }],
  procurementName: [{ required: true, message: '请输入采购名称', trigger: ['blur', 'change'] }],
  specificationsModels: [{ required: true, message: '请输入技术规格、型号', trigger: ['blur', 'change'] }],
  predictDate: [{ required: true, message: '请选择预计采购日期', trigger: 'change' }],
  unit: [{ required: true, message: '请选择单位', trigger: ['blur', 'change'] }],
  quantity: [
    { required: true, message: '请输入数量', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  unitSellingPrice: [
    { required: true, message: '请输入销售单价', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  taxPredictPurchasePrice: [
    { required: true, message: '请输入预计采购单价(含税:元)', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  taxPredictPurchaseTotalPrice: [
    { required: true, message: '请输入预计采购总价(含税:元)', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  notTaxPredictPurchasePrice: [
    { required: true, message: '请输入预计采购单价(不含税:元)', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  notTaxPredictPurchaseTotalPrice: [
    { required: true, message: '请输入预计采购总价(不含税:元)', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  taxRate: [
    { required: true, message: '请输入税率', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的税率', trigger: ['blur', 'change'] },
  ],
  // discountRate: [
  //   { required: true, message: '请输入折扣', trigger: ['blur', 'change'] },
  //   { pattern: regexp.naturalNumber, message: '请输入正确的折扣', trigger: ['blur', 'change'] },
  // ],
  // discountedPrice: [
  //   { required: true, message: '请输入折扣后销售单价', trigger: ['blur', 'change'] },
  //   { pattern: regexp.naturalNumber, message: '请输入正确的折扣后销售单价', trigger: ['blur', 'change'] },
  // ],
})
const formRef = useTemplateRef('formRef')
const form = ref({
  standardPackage: '',
  subitemSubject: '',
  procurementName: '',
  specificationsModels: '',
  unit: '',
  quantity: '',
  unitSellingPrice: '',
  predictDate: '',
  taxPredictPurchasePrice: '',
  taxPredictPurchaseTotalPrice: '',
  // discountRate: 1,
  // discountedPrice: '',
  taxRate: 0,
  costType: '',
  notTaxPredictPurchasePrice: '',
  notTaxPredictPurchaseTotalPrice: '',
})
const model = defineModel()
function onCancel() {
  formRef.value.resetFields()
  emit('cancel')
  model.value = false
}
function onConfirm() {
  formRef.value.validate().then(() => {
    emit('confirm', {
      formData: { ...form.value },
      dialogType: 'wccb',
    })
    onCancel()
  })
}
function onOpen() {
  if (!isEmpty(info)) {
    form.value = { ...form.value, ...info }
  } else {
    delete form.value.uuid
    delete form.value.id
  }
}

watch(() => [
  form.value.taxRate,
  form.value.quantity,
  form.value.taxPredictPurchasePrice,
], () => {
  if (!isNumber(form.value.taxRate)) {
    form.value.taxPredictPurchasePrice = '请填写正确的"数量"和"税率"以及"预计采购单价"'
    form.value.taxPredictPurchaseTotalPrice = '请填写正确的"数量"和"税率"以及"预计采购单价"'
  }
  if (isNumber(form.value.quantity) && isNumber(form.value.taxPredictPurchasePrice)) {
    // 先计算需要税率的预计采购总价(不含税:元)
    form.value.taxPredictPurchaseTotalPrice = toFixedAccurate(multiply(toNumber(form.value.quantity), toNumber(form.value.taxPredictPurchasePrice)))
    // 如果税率、预计采购单价(不含税)、数量都有值
    if (isNumber(form.value.taxRate) && isNumber(form.value.quantity) && isNumber(form.value.taxPredictPurchasePrice)) {
      const taxPredictPurchasePrice = toNumber(form.value.taxPredictPurchasePrice)
      const taxRate = toNumber(form.value.taxRate)
      const quantity = toNumber(form.value.quantity)
      // 预计采购单价(不含税:元) = 预计采购单价(含税:元) * (1 - 税率)
      form.value.notTaxPredictPurchasePrice = toFixedAccurate(taxPredictPurchasePrice * (1 - taxRate / 100))
      // 预计采购总价(不含税:元) = 预计采购总价(含税:元) * (1 - 税率)
      form.value.notTaxPredictPurchaseTotalPrice = toFixedAccurate(multiply(taxPredictPurchasePrice, quantity) * (1 - taxRate / 100))
    }
  } else {
    form.value.notTaxPredictPurchaseTotalPrice = '请填写正确的"数量"和"预计采购单价"'
    form.value.taxPredictPurchaseTotalPrice = '请填写正确的"数量"和"税率"以及"预计采购单价"'
  }
})

// watchEffect(() => {
//   if (form.value.unitSellingPrice || form.value.discountRate) {
//     form.value.discountedPrice = toFixedAccurate(form.value.unitSellingPrice * form.value.discountRate, 2)
//   }
// })
</script>

<style lang="scss" scoped>
:deep(.custom_input_number) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
