<template>
  <ProjectCenter>
    <div style="padding: 20px">
      <div class="title">
        <span class="title-text">工时统计信息</span>
        <div class="desc">
          <div class="desc-item">
            <div class="label">
              项目名称：
            </div>
            <div class="value">
              {{ route.query.name }}
            </div>
          </div>
          <ElDivider direction="vertical" />
          <div class="desc-item">
            <div class="label">
              项目编号：
            </div>
            <div class="value">
              {{ route.query.pcode }}
            </div>
          </div>
        </div>
      </div>
      <div class="search-box">
        <ElPopover
          :visible="popoverVisible"
          :width="1636"
          placement="bottom-start"
          trigger="click">
          <template #reference>
            <ElButton @click="popoverVisible = !popoverVisible">
              <i
                class="iconfont icon-sift"
                style="margin-right: 6px" />
              <span style="margin-right: 6px">所有筛选</span>
              <ElIcon>
                <ArrowDown />
              </ElIcon>
            </ElButton>
          </template>
          <div style="padding: 15px 8px">
            <ElForm
              :inline="true"
              :model="searchForm">
              <ElFormItem
                label="填报人："
                prop="submitter"
                style="width: 360px">
                <ElInput
                  v-model="searchForm.submitter"
                  clearable
                  placeholder="请输入" />
              </ElFormItem>
              <ElFormItem
                label="工时日期："
                prop="workDate"
                style="width: 360px">
                <ElDatePicker
                  v-model="searchForm.workDate"
                  type="daterange"
                  clearable
                  range-separator="-"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期" />
              </ElFormItem>
              <ElFormItem
                label="工时类型："
                prop="workHourType"
                style="width: 360px">
                <ElSelect
                  v-model="searchForm.workHourType"
                  clearable
                  placeholder="请选择">
                  <ElOption
                    v-for="item in workTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="所属部门："
                prop="deptName"
                style="width: 360px">
                <ElInput
                  v-model="searchForm.deptName"
                  clearable
                  placeholder="请输入" />
              </ElFormItem>
              <ElFormItem
                label="工时属性："
                prop="workAttribute"
                style="width: 360px">
                <ElSelect
                  v-model="searchForm.workAttribute"
                  clearable
                  placeholder="请选择">
                  <ElOption
                    v-for="item in workPropertyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
              </ElFormItem>
              <ElFormItem
                label="提交日期："
                prop="submitTime"
                style="width: 360px">
                <ElDatePicker
                  v-model="searchForm.submitTime"
                  type="daterange"
                  clearable
                  range-separator="-"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期" />
              </ElFormItem>
              <ElFormItem
                label="工时明细单号："
                prop="timesheetDetailCode"
                style="width: 360px">
                <ElInput
                  v-model="searchForm.timesheetDetailCode"
                  clearable
                  placeholder="请输入" />
              </ElFormItem>
            </ElForm>
            <div
              style="
                display: flex;
                justify-content: end;
                width: 100%;
                margin-top: 10px;
              ">
              <ElButton
                type="primary"
                @click="onSearch">
                查询
              </ElButton>
              <ElButton @click="resetForm">
                重置
              </ElButton>
            </div>
          </div>
        </ElPopover>
      </div>
      <div class="menu-box">
        <ElButton @click="onExportAll">
          <i
            class="iconfont icon-Download"
            style="margin-right: 6px" />
          导出全部
        </ElButton>
      </div>
      <div
        v-show="multipleSelection.length > 0"
        class="check-box">
        <span class="check-count">已选择 {{ multipleSelection.length }} 项</span>
        <div class="check-action">
          <ElButton
            link
            size="small"
            @click="onExportSelected">
            导出所选
          </ElButton>
          <ElButton
            link
            size="small"
            @click="clearSelection">
            取消选择
          </ElButton>
        </div>
      </div>
      <ElTable
        ref="tableRef"
        v-loading="tableLoading"
        :data="approveDetailList"
        border
        max-height="530"
        style="margin-top: 20px"
        @row-dblclick="handleDetail"
        @selection-change="handleSelectionChange">
        <ElTableColumn
          fixed="left"
          type="selection"
          width="40" />
        <ElTableColumn
          fixed="left"
          type="index"
          label="序号"
          width="60" />
        <ElTableColumn
          label="填报人"
          prop="submitter"
          width="120" />
        <ElTableColumn
          label="工时日期"
          prop="workDate"
          width="120" />
        <ElTableColumn
          label="工作时长（天）"
          width="130"
          prop="submitDays" />
        <ElTableColumn
          label="工时类型"
          width="100"
          prop="workHourType">
          <template #default="{ row }">
            <ElTag :type="getWorkTypeTagType(row.workHourType)">
              {{ row.workHourType }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="所属部门"
          prop="deptName"
          width="130" />
        <ElTableColumn
          label="工作内容"
          prop="workContent"
          width="200" />
        <ElTableColumn
          label="备注"
          prop="remark"
          width="200" />
        <ElTableColumn
          label="工时属性"
          width="80"
          prop="workAttribute">
          <template #default="{ row }">
            <ElTag :type="getWorkPropertyTagType(row.workAttribute)">
              {{
                workPropertyOptions.find(
                  (item) => item.value === row.workAttribute,
                )?.label
              }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          label="部门负责人"
          prop="deptLeader"
          width="120" />
        <ElTableColumn
          label="提交日期"
          prop="submitTime"
          width="180" />
        <ElTableColumn
          label="工时明细单号"
          prop="timesheetDetailCode"
          width="220" />
      </ElTable>
      <div class="pagination">
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>
  </ProjectCenter>
</template>

<script setup>
import * as api from '@/api/man-hour/approval.js'
import {
  workPropertyOptions,
  workTypeOptions,
} from '@/views/man-hour/approval/config.js'
import { ArrowDown } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import ProjectCenter from '../components/layout/index.vue'

const route = useRoute()
const router = useRouter()

const popoverVisible = ref(false)
const tableRef = ref()
const tableLoading = ref(false)
const multipleSelection = ref([])
const total = ref(0)
const approveDetailList = ref([])

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
})
const searchForm = reactive({
  submitter: '',
  workDate: [],
  workHourType: '',
  deptName: '',
  workAttribute: '',
  submitTime: [],
  timesheetDetailCode: '',
})

function getWorkTypeTagType(type) {
  return workTypeOptions.find(item => item.value === type)?.type
}

function getWorkPropertyTagType(type) {
  return workPropertyOptions.find(item => item.value === type)?.type
}

function onSearch() {
  popoverVisible.value = false
  getApproveDetailList()
}

function resetForm() {
  for (const key in searchForm) {
    searchForm[key] = ''
  }
  searchForm.workDate = []
  searchForm.submitTime = []
  popoverVisible.value = false
  getApproveDetailList()
}

function clearSelection() {
  tableRef.value.clearSelection()
}

function handleSelectionChange(val) {
  multipleSelection.value = val
}

function handleSizeChange(val) {
  pagination.pageSize = val
  getApproveDetailList()
}

function handleCurrentChange(val) {
  pagination.pageNum = val
  getApproveDetailList()
}

function onExportSelected() {
  exportSupplierList({
    exportAll: 0,
    bids: multipleSelection.value.map(item => item.bid),
  })
}

function onExportAll() {
  exportSupplierList({
    exportAll: 1,
  })
}

async function exportSupplierList(data) {
  try {
    const res = await api.exportPageList(data)
    downloadAsExcel(res, '工时明细表.xlsx')
  } catch (error) {
    console.log(error)
  }
}

function downloadAsExcel(data, name) {
  // data是二进制流，通过new Blob方法转化为Blob,type是下载文件格式，本方法以excel为例
  // name是你想下载的文件名
  const url = window.URL.createObjectURL(
    new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }),
  )
  // 如果后端返回的是Blob,则不需要用上面的方法new Blob直接用data
  // const url = window.URL.createObjectURL(data);
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', `${name}` || 'template.xlsx')
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

function handleDetail(row) {
  router.push(`/man-hour/particulars/detail?bid=${row.bid}`)
}

async function getApproveDetailList() {
  tableLoading.value = true
  try {
    const res = await api.getApproveDetailList({
      page: pagination,
      params: {
        ...searchForm,
        projectCode: route.query.pcode,
        approveStatus: 2,
        startWorkDate: searchForm.workDate.length
          ? searchForm.workDate[0]
          : undefined,
        endWorkDate: searchForm.workDate.length
          ? searchForm.workDate[1]
          : undefined,
        submitStartTime: searchForm.submitTime.length
          ? searchForm.submitTime[0]
          : undefined,
        submitEndTime: searchForm.submitTime.length
          ? searchForm.submitTime[1]
          : undefined,
      },
    })
    approveDetailList.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    console.log(error)
  } finally {
    tableLoading.value = false
  }
}

onMounted(() => {
  getApproveDetailList()
})
</script>

<style scoped lang="scss">
.title {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid rgb(0 0 0 / 6%);

  .title-text {
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .desc {
    display: flex;
    align-items: center;
    margin-left: 20px;
    padding: 2px 8px;
    border-radius: 26px;
    background: rgb(0 0 0 / 3%);
    font-size: 14px;

    .desc-item {
      display: flex;
      align-items: center;

      .label {
        color: rgb(0 0 0 / 45%);
      }

      .value {
        color: rgb(0 0 0 / 88%);
      }
    }
  }
}

.search-box {
  padding: 20px 0;
  border-bottom: 1px solid rgb(0 0 0 / 6%);
}

.menu-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
}

.check-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  margin-top: 20px;
  padding: 0 24px;
  border-radius: 4px;
  background: rgb(0 0 0 / 4%);

  .check-count {
    color: rgb(0 0 0 / 45%);
    font-size: 14px;
  }
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 20px;
}

.status-row {
  display: flex;
  align-items: center;

  .status-icon {
    width: 6px;
    height: 6px;
    margin-right: 6px;
    border-radius: 50%;
  }

  .status-icon-0 {
    background: rgb(0 0 0 / 25%);
  }

  .status-icon-1 {
    background: #1677ff;
  }

  .status-icon-2 {
    background: #52c41a;
  }

  .status-icon-3 {
    background: #ff7875;
  }

  .status-icon--1 {
    background: rgb(251 189 67);
  }
}
</style>
