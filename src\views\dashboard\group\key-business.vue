<template>
  <div class="key_business">
    <ElTable
      height="331px"
      style="width: 100%;"
      :data="tableData">
      <ElTableColumn
        label="序号"
        type="index"
        width="55"
        fixed="left" />
      <ElTableColumn
        label="项目名称"
        prop="name"
        width="130" />
      <ElTableColumn
        label="规模（万）"
        prop="scale"
        width="100" />
      <ElTableColumn
        label="经营主体"
        prop="businessSubject"
        width="100" />
      <ElTableColumn
        label="商务"
        prop="businessManager"
        width="100" />
      <ElTableColumn
        label="预率"
        prop="winRate"
        width="100" />
      <ElTableColumn
        label="落单率"
        prop="orderRate"
        width="100" />
      <ElTableColumn
        label="预计落单时间"
        prop="orderTime"
        width="110" />
      <ElTableColumn
        label="概算利润率"
        prop="profitRate"
        width="100" />
    </ElTable>
  </div>
</template>

<script setup>
const tableData = ref([{
  name: '项目1',
  scale: 100,
  businessSubject: '主体1',
  businessManager: '张三',
  winRate: '80%',
  orderRate: '70%',
  orderTime: '2025-01-01',
  profitRate: '20%',
}, {
  name: '项目2',
  scale: 200,
  businessSubject: '主体2',
  businessManager: '李四',
  winRate: '70%',
  orderRate: '60%',
  orderTime: '2025-02-01',
  profitRate: '15%',
}, {
  name: '项目3',
  scale: 300,
  businessSubject: '主体3',
  businessManager: '王五',
  winRate: '60%',
  orderRate: '50%',
  orderTime: '2025-03-01',
  profitRate: '10%',
}, {
  name: '项目4',
  scale: 400,
  businessSubject: '主体4',
  businessManager: '赵六',
  winRate: '50%',
  orderRate: '40%',
  orderTime: '2025-04-01',
  profitRate: '5%',
}, {
  name: '项目5',
  scale: 500,
  businessSubject: '主体5',
  businessManager: '钱七',
  winRate: '40%',
  orderRate: '30%',
  orderTime: '2025-05-01',
  profitRate: '0%',
}, {
  name: '项目6',
  scale: 600,
  businessSubject: '主体6',
  businessManager: '孙八',
  winRate: '30%',
  orderRate: '20%',
  orderTime: '2025-06-01',
  profitRate: '-5%',
}, {
  name: '项目7',
  scale: 700,
  businessSubject: '主体7',
  businessManager: '周九',
  winRate: '20%',
  orderRate: '10%',
  orderTime: '2025-07-01',
  profitRate: '-10%',
}, {
  name: '项目8',
  scale: 800,
  businessSubject: '主体8',
  businessManager: '吴十',
  winRate: '10%',
  orderRate: '5%',
  orderTime: '2025-08-01',
  profitRate: '-15%',
}, {
  name: '项目9',
  scale: 900,
  businessSubject: '主体9',
  businessManager: '郑十一',
  winRate: '5%',
  orderRate: '2.5%',
  orderTime: '2025-09-01',
  profitRate: '-20%',
}, {
  name: '项目10',
  scale: 1000,
  businessSubject: '主体10',
  businessManager: '冯十二',
  winRate: '2.5%',
  orderRate: '1.25%',
  orderTime: '2025-10-01',
  profitRate: '-25%',
}])
</script>

<style lang="scss" scoped>
.key_business {
  height: 100%;
  padding-bottom: 22px;
}
</style>
