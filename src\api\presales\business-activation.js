import request from '@/utils/request'

/**
 * 商机启动列表
 */
export function getBusinessActivationList(data) {
  return request({
    url: '/project/business/init/list',
    method: 'post',
    data,
  })
}

/**
 * 获取商机统计数据
 */
export function getBusinessStatistics() {
  return request({
    url: '/project/business/init/statistics',
    method: 'get',
  })
}

/**
 * 商机备案
 */
export function businessRecord(data) {
  return request({
    url: '/project/business/init/record',
    method: 'post',
    data,
  })
}

/**
 * 商机撤回
 */
export function businessWithdrawn(id) {
  return request({
    url: `/project/business/init/withdrawn/${id}`,
    method: 'get',
  })
}

/**
 * 商机重启
 */
export function businessRestart(id) {
  return request({
    url: `/project/business/init/restart/${id}`,
    method: 'get',
  })
}

/**
 * 查询商机规则
 */
export function getBusinessRules() {
  return request({
    url: '/project/business/init/rule',
    method: 'get',
  })
}

/**
 * 修改商机规则金额
 */
export function updateBusinessRules(data) {
  return request({
    url: '/project/business/init/rule/edit',
    method: 'post',
    data,
  })
}

/**
 * 获取商机详情
 */
export function getBusinessDetail(id) {
  return request({
    url: `/project/business/init/${id}`,
    method: 'get',
  })
}

/**
 * 获取售前支持部门
 */
export function getBusinessDept(params) {
  return request({
    url: `/project/business/init/get/dept`,
    method: 'get',
    params,
  })
}

// 查询部门下拉树结构
export function getDeptTreeList() {
  return request({
    url: '/system/user/deptTree',
    method: 'get',
  })
}

// 查询市场经理列表
export function getManagerList(id) {
  return request({
    url: `/system/user/listByDeptId?deptId=${id}`,
    method: 'get',
  })
}

/**
 * 商机修改
 */
export function updateBusiness(data) {
  return request({
    url: '/project/business/init/approval/update',
    method: 'post',
    data,
  })
}

/**
 * 商机新增
 */
export function addBusiness(data) {
  return request({
    url: '/project/business/init/add',
    method: 'post',
    data,
  })
}

/**
 * 商机删除
 */
export function removeBusiness(id) {
  return request({
    url: `/project/business/init/del/${id}`,
    method: 'post',
  })
}
