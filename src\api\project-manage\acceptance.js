import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 获取阶段性验收列表
 * @param {*} data
 * @returns
 */
export function getAcceptanceList(data) {
  return request({
    url: `${prefix}/phasedAcceptance/pageList`,
    method: 'post',
    data,
  })
}

/**
 * 暂存或更新阶段性验收
 * @param {*} data
 * @returns
 */
export function createOrUpdateAcceptance(data) {
  return request({
    url: `${prefix}/phasedAcceptance/createOrUpdate`,
    method: 'post',
    data,
  })
}

/**
 * 提交阶段性验收，走审批
 * @param {*} data
 * @returns
 */
export function submitAcceptance(data) {
  return request({
    url: `${prefix}/phasedAcceptance/submit`,
    method: 'post',
    data,
  })
}

/**
 * 获取可以选择的合同列表，用于创建阶段性验收时选择合同
 * @param {*} params
 * @returns
 */
export function getContractByProjectForAcceptance(params) {
  return request({
    url: `/purchase/psmContract/list`,
    method: 'get',
    params,
  })
}

/**
 * 获取可以选择的里程碑列表，用于创建阶段性验收时选择里程碑
 * @param {*} data
 * @returns
 */
export function getMilestoneByContractForAcceptance(data) {
  return request({
    url: `${prefix}/milestone/plan/contract/list`,
    method: 'post',
    data,
  })
}

/**
 * 获取历史结算总金额
 * @param {*} params
 * @returns
 */
export function getTotalAmountByContractForAcceptance(params) {
  return request({
    url: `${prefix}/phasedAcceptance/history/settleAmount`,
    method: 'get',
    params,
  })
}

/**
 * 获取阶段性验收详情
 * @param {*} id
 * @returns
 */
export function getAcceptanceDetail(id) {
  return request({
    url: `${prefix}/phasedAcceptance/details/${id}`,
    method: 'get',
  })
}

/**
 * 删除阶段性验收
 * @param {*} id
 * @returns
 */
export function deleteAcceptance(id, data) {
  return request({
    url: `${prefix}/phasedAcceptance/del/${id}`,
    method: 'post',
    data,
  })
}

/**
 * 冲正阶段性验收
 * @param {*} data
 * @returns
 */
export function reversalAcceptance(data) {
  return request({
    url: `${prefix}/phasedAcceptance/correction`,
    method: 'post',
    data,
  })
}
