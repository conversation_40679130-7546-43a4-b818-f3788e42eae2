import request from '@/utils/request.js'

const prefix = '/ecology/partner'

/**
 * 新增临时产品
 * @param {*} data
 * @returns
 */
export function createTempProduct(data) {
  return request({
    method: 'post',
    url: `${prefix}/addProduct`,
    data,
  })
}

/**
 * 获取临时产品列表
 * @param {*} params
 * @returns
 */
export function getTempProductList(data) {
  return request({
    method: 'post',
    url: `${prefix}/product/all/list`,
    data,
  })
}

/**
 * 获取临时产品详情
 * @param {*} params
 * @returns
 */
export function getTempProductDetail(params) {
  return request({
    method: 'get',
    url: `${prefix}/product/details`,
    params,
  })
}

/**
 * 转为生态产品
 * @param {*} data
 * @returns
 */
export function transformToEcologyProduct(data) {
  return request({
    method: 'post',
    url: `${prefix}/tempToEcology`,
    data,
  })
}

/**
 * 删除临时产品
 * @param {*} params
 * @returns
 */
export function deleteTempProduct(params) {
  return request({
    method: 'get',
    url: `${prefix}/product/del`,
    params,
  })
}

/**
 * 编辑临时产品
 * @param {*} data
 * @returns
 */
export function editTempProduct(data) {
  return request({
    method: 'post',
    url: `${prefix}/updateProduct`,
    data,
  })
}
