<template>
  <TableContainer title="外包人员列表">
    <template #toolbar>
      <div>
        <ElButton
          @click="exportHandle">
          导出
        </ElButton>
      </div>
    </template>
    <template #default="{ contentHeight }">
      <ElTable
        v-loading="loading"
        border
        :data="data"
        :max-height="contentHeight"
        row-key="bid"
        @selection-change="handleSelect">
        <ElTableColumn
          type="selection"
          reserve-selection
          width="55" />
        <ElTableColumn
          prop="staffName"
          label="外包人员名称" />
        <ElTableColumn
          prop="personnelStatus"
          label="人员状态" />
        <ElTableColumn
          prop="staffCode"
          label="外包人员编号" />
        <ElTableColumn
          prop="positionName"
          label="岗位" />
        <ElTableColumn
          prop="departmentName"
          label="所属部门" />
        <ElTableColumn
          prop="plannedEntryDate"
          label="计划进场时间" />
        <ElTableColumn
          prop="plannedExitDate"
          label="计划离场时间" />
        <ElTableColumn
          prop="actualEntryDate"
          label="实际进场时间" />
        <ElTableColumn
          prop="actualExitDate"
          label="实际离场时间" />
      </ElTable>
    </template>
    <template #footer>
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="Number(total)" />
    </template>
  </TableContainer>
</template>

<script setup>
import { getProjectLibraryOutsourcedPersonList } from '@/api/project-development/project-library.js'
import TableContainer from '@/components/Container/table-container.vue'
import { usePagination } from 'alova/client'
import { ElMessage } from 'element-plus'

const props = defineProps({
  projectCode: {
    default: '',
  },
})

const { proxy } = getCurrentInstance()

const { loading, data, pageSize, page, total } = usePagination((pageNum, pageSize) => getProjectLibraryOutsourcedPersonList({
  page: {
    pageNum,
    pageSize,
  },
  params: {
    projectCode: props.projectCode,
  },
}), {
  total: res => res.total,
  data: res => res.list,
  initialPage: 1, // 初始页码，默认为1
  initialPageSize: 10, // 初始每页数据条数，默认为10
})

const ids = ref([])
function handleSelect(selection) {
  ids.value = selection.map(item => item.bid)
}

async function exportHandle() {
  if (ids.value.length <= 0) {
    ElMessage.warning('请至少选择一条数据')
  } else {
    proxy.download('/project/prd/project/library/detail/outsource/export', { bids: ids.value, isAll: 0, params: {} }, `研发项目项目库外包人员_${new Date().getTime()}.xlsx`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
