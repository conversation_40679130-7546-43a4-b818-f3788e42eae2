<template>
  <div class="chart_panel">
    <div class="chart_panel_header">
      <slot name="title">
        <div class="flex-none flex items-center">
          <p class="text-[20px] font-[600] mr-[12px] truncate">
            {{ title }}
          </p>
          <ElTooltip
            placement="top"
            popper-class="chart_panel_tooltip"
            :content="tooltip">
            <i class="iconfont icon-qy_help1 text-[#000]/45" />
          </ElTooltip>
          <ElDivider direction="vertical" />
          <ElTag
            class="cursor-pointer"
            effect="light"
            :round="true"
            @click="handleClick">
            <div class="flex items-center text-[12px]">
              <ElIcon
                :size="12"
                style="margin-right: 6px;">
                <View />
              </ElIcon>
              数据详情
            </div>
          </ElTag>
        </div>
      </slot>
      <slot name="headerRight">
        <div class="flex-1 flex justify-end">
          <ElDatePicker
            v-model="formYear"
            style="width: 120px;"
            type="year"
            value-format="YYYY"
            size="small"
            placeholder="请选择年度"
            :clearable="false" />
        </div>
      </slot>
    </div>
    <slot :year="formYear" />
  </div>
</template>

<script setup>
const { title, tooltip, year } = defineProps({
  title: String,
  tooltip: String,
  year: Number,
})

const emit = defineEmits(['click'])
const formYear = ref(year)
watch(() => year, () => {
  formYear.value = year
})

function handleClick() {
  emit('click')
}
</script>

<style lang="scss" scoped>
.chart_panel {
  width: 100%;
  min-height: 100%;
  padding: 0 18px 18px;
  border-radius: 10px;
  background: #fff;

  &_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 0 16px;
  }
}
</style>

<style lang="scss">
.chart_panel_tooltip {
  max-width: 240px;
}
</style>
