import request from '@/utils/request'

// 国资云应用系统分页列表
export function getGzCloudAppSystemPageList(params) {
  return request({
    url: `/cloudorder/gz-cloud-app-system/pageList`,
    method: 'post',
    data: params,
  })
}

// 获取国资云应用系统详情
export function getGzCloudAppSystemDetail(params) {
  return request({
    url: `/cloudorder/gz-cloud-app-system/detail`,
    method: 'get',
    params,
  })
}

// 删除国资云应用系统
export function deleteGzCloudAppSystem(params) {
  return request({
    url: `/cloudorder/gz-cloud-app-system/del`,
    method: 'get',
    params,
  })
}

// 新增、编辑国资云应用系统
export function createOrUpdateGzCloudAppSystem(data) {
  return request({
    url: `/cloudorder/gz-cloud-app-system/createOrUpdate`,
    method: 'post',
    data,
  })
}

// 校验是否关联其他数据
export function checkAssociationOthert(params) {
  return request({
    url: `/cloudorder/gz-cloud-app-system/check-association-other`,
    method: 'get',
    params,
  })
}

// 校验是否国资云负责人
export function checkGzCloudLeader() {
  return request({
    url: `/cloudorder/gz-cloud-app-system/check-gzcloud-leader`,
    method: 'get',
  })
}
