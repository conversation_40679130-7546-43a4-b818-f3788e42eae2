import request from '@/utils/request'

// 详情展示
export function templateDetails(bid) {
  return request({
    url: `/system/template/${bid}`,
    method: 'get',
  })
}

// 模板新增
export function templateAdd(data) {
  return request ({
    url: '/system/template',
    method: 'put',
    data,
  })
}

// 模板修改
export function templateEdit(data) {
  return request({
    url: '/system/template',
    method: 'post',
    data,
  })
}
