<template>
  <ElDialog
    v-model="dialogVisible"
    title="选择发票"
    width="1200px"
    :before-close="handleClose">
    <ElTable
      ref="tableRef"
      border
      :data="tableData"
      height="500px"
      style="margin-top: 20px"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick">
      <!-- 复选框列 -->
      <ElTableColumn
        v-if="multiple"
        type="selection"
        width="55"
        fixed="left"
        align="center" />
      <ElTableColumn
        v-if="!multiple"
        label="操作"
        fixed="left"
        width="80">
        <template #default="{ row }">
          <ElButton
            link
            type="primary"
            @click="selectItem(row)">
            选择
          </ElButton>
        </template>
      </ElTableColumn>
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        width="60"
        fixed="left"
        align="center"
        row-key="id" />
      <ElTableColumn
        prop="invoiceNumber"
        label="发票号码"
        min-width="120px" />
      <ElTableColumn
        prop="invoiceDate"
        label="发票日期"
        width="120px" />
      <ElTableColumn
        prop="invoiceAmountIncludeTax"
        label="开票金额(含税:元)"
        width="140px" />
      <ElTableColumn
        prop="invoiceAmount"
        label="开票金额(不含税:元)"
        width="140px" />
      <ElTableColumn
        prop="taxRate"
        label="税率(%)"
        width="80px" />
      <ElTableColumn
        prop="taxAmount"
        label="税额(元)"
        width="80px" />
      <ElTableColumn
        prop="purchaser"
        label="销售方信息"
        width="120px" />
      <ElTableColumn
        prop="seller"
        label="购买方信息"
        width="120px" />
      <ElTableColumn
        prop="remark"
        label="发票备注"
        width="120px" />
      <ElTableColumn
        prop="confirmIncomeNo"
        label="关联确收申请单号"
        width="120px" />
      <ElTableColumn
        prop="totalConfirmIncomeAmount"
        label="累计确认收入(不含税：元)"
        width="180px" />
      <ElTableColumn
        prop="remainUnconfirmIncomeAmount"
        label="剩余未确认收入(不含税：元)"
        width="180px" />
    </ElTable>
    <div class="pagination-container">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="getList" />
    </div>
    <template #footer>
      <span
        v-if="multiple"
        class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleConfirm"> 确定 </ElButton>
      </span>
      <span
        v-else
        class="dialog-footer">
        <ElButton @click="handleClose">关闭</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup>
import {
  // getInvoiceCorrectList
  getInvoiceCorrectPageList,
} from '@/api/financialManagement/managementOfInvoices/invoiceLedger.js'
import { usePagination } from '@/utils/hooks.js'
import { nextTick, ref } from 'vue'

// 定义组件属性和事件
const props = defineProps({
  currentId: {
    type: Array,
    default: () => [],
  },
  projectCode: {
    type: String,
    default: '',
  },
  contractNumber: {
    type: String,
    default: '',
  },
  multiple: {
    type: Boolean,
    default: true,
  },

})

const emit = defineEmits(['change'])

// 表格和分页相关
const { pagination, indexMethod } = usePagination()
const dialogVisible = ref(false)
const tableData = ref([])
const tableRef = ref(null)
const selectedRows = ref([])
const selectedIds = ref([])

function handleRowClick(row) {
  if (props.multiple) {
    tableRef.value.toggleRowSelection(row)
  }
}

/**
 * 处理表格选择变化
 * @param {Array} rows - 当前选中的行
 */
function handleSelectionChange(rows) {
  // 更新当前页面选中的行
  const currentPageSelectedRows = rows
  // 保留其他页面已选中但当前页面不存在的行
  const otherPagesSelectedRows = selectedRows.value.filter((row) => {
    const rowId = row.id
    return !tableData.value.some((currentRow) => {
      const currentRowId = currentRow.id
      return currentRowId === rowId
    })
  })
  // 合并当前页面选中的行和其他页面已选中的行
  selectedRows.value = [...currentPageSelectedRows, ...otherPagesSelectedRows]
  // 更新选中的ID列表
  selectedIds.value = selectedRows.value.map(row => row.id)
  console.log('选择变化后的ID列表:', selectedIds.value)
}

/**
 * 打开对话框
 */
function showDialog() {
  dialogVisible.value = true
  // 获取发票列表
  getList()
}

/**
 * 确认选择
 */
function handleConfirm() {
  // 触发change事件，传递选中的ID和完整数据
  console.log(selectedIds.value, selectedRows.value, '--- keys rows')
  emit('change', selectedIds.value, selectedRows.value)
  handleClose()
}

function selectItem(row) {
  emit('selectItem', row)
  handleClose()
}
/**
 * 关闭对话框
 */
function handleClose() {
  tableData.value = []
  selectedIds.value = []
  selectedRows.value = []
  pagination.pageNum = 1
  dialogVisible.value = false
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getList()
}

/**
 * 获取发票列表
 */
function getList() {
  // 构建查询参数
  const searchData = {
    page: {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    },
    params: {
      projectCode: props.projectCode,
      contractNumber: props.contractNumber,
      idFilterList: props.currentId,
    },
  }

  console.log('查询前选中ID:', selectedIds.value)

  // 调用API获取数据
  getInvoiceCorrectPageList(searchData)
    .then((res) => {
      console.log('API返回数据:', res)
      if (res.code === 200) {
        const list = res.data?.list || []
        console.log('获取到的发票列表:', list)
        tableData.value = list
        pagination.total = res.data?.total || 0
        nextTick(() => {
          if (tableRef.value) {
            tableRef.value.clearSelection()
          }
        })
      } else {
        console.error('API返回错误:', res.message || '未知错误')
      }
    })
    .catch((err) => {
      console.error('获取发票列表失败:', err)
    })
}

// 暴露方法给父组件
defineExpose({
  showDialog,
})
</script>

  <style lang="scss" scoped>
  .dialog-footer button:first-child {
  margin-right: 10px;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
