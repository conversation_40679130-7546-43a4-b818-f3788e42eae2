<template>
  <DefaultContainer v-loading="isLoading">
    <!-- 折叠面板组件，用于查询条件 -->
    <Collapse>
      <template #header />
      <!-- 查询表单 -->
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="100px"
        label-position="left">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElFormItem
              prop="calculateEntity"
              label="核算主体">
              <ElInput
                v-model="formData.calculateEntity"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="calculateEntityCode"
              label="核算主体编码">
              <ElInput
                v-model="formData.calculateEntityCode"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="projectName"
              label="项目名称">
              <ElInput
                v-model="formData.projectName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="projectCode"
              label="项目编号">
              <ElInput
                v-model="formData.projectCode"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="contractName"
              label="合同名称">
              <ElInput
                v-model="formData.contractName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="contractNumber"
              label="合同编号">
              <ElInput
                v-model="formData.contractNumber"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="customerName"
              label="客户名称">
              <ElInput
                v-model="formData.customerName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="customerCode"
              label="客户编码">
              <ElInput
                v-model="formData.customerCode"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="formNo"
              label="申请编号">
              <ElInput
                v-model="formData.formNo"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="status"
              label="审核状态">
              <ElSelect
                v-model="formData.status"
                placeholder="请选择"
                clearable
                style="width: 100%">
                <ElOption
                  label="全部"
                  value="" />
                <ElOption
                  label="草稿"
                  value="0" />
                <ElOption
                  label="待审批"
                  value="1" />
                <ElOption
                  label="审批中"
                  value="2" />
                <ElOption
                  label="审批通过"
                  value="3" />
                <ElOption
                  label="审批拒绝"
                  value="4" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="shareStatus"
              label="共享审核状态">
              <ElSelect
                v-model="formData.shareStatus"
                placeholder="请选择"
                clearable
                style="width: 100%">
                <ElOption
                  label="全部"
                  value="" />
                <ElOption
                  label="待发起"
                  value="0" />
                <ElOption
                  label="已发起"
                  value="1" />
                <ElOption
                  label="审核通过"
                  value="2" />
                <ElOption
                  label="审核驳回"
                  value="3" />
                <ElOption
                  label="出错"
                  value="4" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="applicant"
              label="申请人">
              <ElInput
                v-model="formData.applicant"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="applicationTime"
              label="申请时间">
              <ElDatePicker
                v-model="formData.applicationTime"
                clearable
                type="daterange"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </Collapse>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
      <ElButton
        type="primary"
        @click="handle('add')">
        新增
      </ElButton>
      <!-- 表头设置抽屉组件 -->
      <SelectHeadersDrawer
        ref="selectHeadersDrawerRef"
        v-model="allHeaders"
        :module-key="moduleKey" />
    </div>

    <!-- 数据表格 -->
    <ElTable
      border
      :data="tableData"
      style="margin-top: 20px"
      @row-dblclick="onRowDbClick">
      <!-- 序号列 -->
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        width="60"
        fixed="left"
        align="center" />
      <!-- 动态表头列 -->
      <ElTableColumn
        v-for="item in headers"
        :key="item.headerKey"
        :prop="item.headerKey"
        :label="item.headerName"
        :min-width="120" />
      <ElTableColumn
        label="操作"
        width="120"
        fixed="right">
        <template #default="scope">
          <ElButton
            type="primary"
            link
            @click="handle('edit', scope.row)">
            编辑
          </ElButton>
          <ElPopconfirm
            v-hasPermi="['flowClaim:delete']"
            title="确定删除吗？"
            @confirm="handle('delete', scope.row)">
            <template #reference>
              <ElButton
                type="primary"
                link>
                删除
              </ElButton>
            </template>
          </ElPopconfirm>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onSearch" />
    </div>
  </DefaultContainer>
</template>

<script setup>
import {
  delFlowClaim,
  getFlowClaimPageList,
} from '@/api/financialManagement/collectionManagement/flowClaim.js'
import { getDynamicHeader } from '@/api/financialManagement/index.js'
import Collapse from '@/components/Collapse/index.vue'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { usePagination } from '@/utils/hooks.js'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import SelectHeadersDrawer from '../../modules/SelectHeadersDrawer.vue'

/**
 * 分页相关
 * pagination: 分页参数对象
 * indexMethod: 表格序号计算方法
 */
const { pagination, indexMethod } = usePagination()

const router = useRouter()

/**
 * 表单数据与引用
 */
const formData = reactive({}) // 查询表单数据
const formRef = ref() // 表单引用，用于重置

/**
 * 表格数据与加载状态
 */
const tableData = ref([]) // 表格数据
const loadingIndex = ref(0) // 加载计数器，用于处理多个并发请求的loading状态
const moduleKey = 'flow_claim' // 模块标识，用于获取动态表头

/**
 * 计算属性：是否显示加载状态
 * 当loadingIndex > 0时显示加载状态
 */
const isLoading = computed(() => {
  return loadingIndex.value !== 0
})

/**
 * 表头相关数据
 */
const allHeaders = ref([]) // 所有表头数据
const selectHeadersDrawerRef = ref(null) // 表头设置抽屉组件引用

/**
 * 计算属性：当前显示的表头
 * 过滤出isShow为1的表头并按headerSort排序
 */
const headers = computed(() => {
  return allHeaders.value
    .filter(item => item.isShow === 1)
    .sort((a, b) => a.headerSort - b.headerSort)
})

/**
 * 生命周期钩子：组件挂载完成后初始化
 */
onMounted(() => {
  init()
})

/**
 * 初始化方法
 */
function init() {
  getDynamicHeaders()
  getFlowClaimPageFun()
}

/**
 * 获取动态表头数据
 */
function getDynamicHeaders() {
  loadingIndex.value++ // 开始加载，计数器加1
  getDynamicHeader(moduleKey)
    .then((res) => {
      console.log(res, '---- getDynamicHeader')
      const data = res.data
      // 设置表头数据
      allHeaders.value = data
      loadingIndex.value-- // 加载完成，计数器减1
    })
    .catch((err) => {
      console.log(err, '---- getDynamicHeader')
      loadingIndex.value-- // 加载出错，计数器减1
    })
}

/**
 * 获取流水认领分页列表
 */
function getFlowClaimPageFun() {
  loadingIndex.value++ // 开始加载，计数器加1
  const searchData = {
    page: {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    },
    params: {},
  }
  searchData.params = {
    ...formData,
  }

  // 处理日期范围查询
  if (formData.applicationTime) {
    searchData.params.applicationTimeStart = formData.applicationTime[0]
    searchData.params.applicationTimeEnd = formData.applicationTime[1]
    delete searchData.params.applicationTime
  }

  if (formData.shareStatus) {
    searchData.params.shareStatus = Number(formData.shareStatus)
  }
  if (formData.status) {
    searchData.params.status = Number(formData.status)
  }

  getFlowClaimPageList(searchData)
    .then((res) => {
      console.log(res, '---- getFlowClaimPageList')
      tableData.value = res.data.list
      pagination.total = res.data.total
      loadingIndex.value-- // 加载完成，计数器减1
    })
    .catch((err) => {
      console.log(err, '---- getFlowClaimPageList')
      loadingIndex.value-- // 加载出错，计数器减1
    })
}

/**
 * 表格行双击事件处理
 * @param {object} row - 行数据
 */
function onRowDbClick(row) {
  router.push({
    path: '/financialManagement/collectionManagement/flowClaim/details',
    query: {
      id: row.id,
    },
  })
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getFlowClaimPageFun()
}

/**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  getFlowClaimPageFun()
}

/**
 * 重置方法
 * 重置表单并查询第一页数据
 */
function onReset() {
  formRef.value.resetFields() // 重置表单字段
  pagination.pageNum = 1 // 重置到第一页
  onSearch() // 执行查询
}

/**
 * 操作方法
 * @param {string} type - 操作类型
 * @param {object} row - 行数据
 */
function handle(type, row) {
  console.log(type, row, '---- handle')
  switch (type) {
    case 'add':
      router.push({
        path: '/financialManagement/collectionManagement/flowClaim/form',
        query: {
          type: 'add',
        },
      })
      break
    case 'edit':
      router.push({
        path: '/financialManagement/collectionManagement/flowClaim/form',
        query: {
          id: row.id,
          type: 'edit',
        },
      })
      break
    case 'detail':
      router.push({
        path: '/financialManagement/collectionManagement/flowClaim/details',
        query: {
          id: row.id,
        },
      })
      break
    case 'delete':
      delFlowClaim({
        id: row.id,
      }).then((res) => {
        console.log(res, '---- delFlowClaim')
        if (res.code === 200) {
          ElMessage.success('删除成功')
          getFlowClaimPageFun()
        } else {
          ElMessage.warning(res.msg)
        }
      })
      break
    default:
      console.log('其他操作')
      break
  }
}
</script>

<style lang="scss" scoped>
/* 操作区域样式 */
.operation-area {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
