<template>
  <ElDialog
    v-model="model"
    destroy-on-close
    @open="onOpen">
    <ElPopover
      placement="bottom-start"
      trigger="click"
      width="calc(50% - 30px)"
      :hide-after="0"
      :show-arrow="false">
      <template #reference>
        <ElButton>
          <i
            class="iconfont icon-sift"
            :style="{
              marginRight: '8px',
            }" />
          所有筛选
          <ElIcon
            :style="{
              marginLeft: '10px',
            }">
            <ArrowDown />
          </ElIcon>
        </ElButton>
      </template>
      <div style="padding: 20px 4px;">
        <ElForm label-width="auto">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="产品名称">
                <ElInput
                  v-model="queryParams.productName"
                  placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="产品分类">
                <ElCascader
                  ref="elCascader"
                  v-model="queryParams.types"
                  :options="productTypeOptions"
                  collapse-tags
                  :props="{ multiple: true, value: 'value', label: 'value' }"
                  clearable
                  :teleported="false" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="产品类别">
                <ElSelect
                  v-model="queryParams.productType"
                  :teleported="false">
                  <ElOption
                    v-for="item in productType"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="所属生态伙伴">
                <ElInput
                  v-model="queryParams.partnerName"
                  placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="6">
              <div style="display: flex; justify-content: flex-end; align-items: center;">
                <ElButton
                  type="primary"
                  @click="onSearch">
                  搜索
                </ElButton>
                <ElButton @click="onReset">
                  重置
                </ElButton>
              </div>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>
    </ElPopover>
    <ElTable
      v-loading="loading"
      style="margin-top: 20px;"
      row-key="bid"
      max-height="calc(70vh - 100px)"
      :data="data"
      @selection-change="handleSelectionChange"
      @row-click="openDetailDialog">
      <ElTableColumn
        type="selection"
        width="60"
        reserve-selection />
      <ElTableColumn
        label="产品名称"
        prop="solutionName" />
      <ElTableColumn
        label="产品类别"
        prop="productType" />
      <ElTableColumn
        label="产品分类"
        prop="partnerClass"
        :formatter="row => formatterProductClass(row)" />
      <ElTableColumn
        label="
        标准产品包"
        prop="productPackage" />
      <ElTableColumn
        label="产品实施"
        prop="productImplementation" />
      <ElTableColumn
        label="产品定制化开发"
        prop="productDevelopment" />
      <ElTableColumn
        label="所属生态伙伴"
        prop="companyName" />
    </ElTable>
    <div style="display: flex; justify-content: flex-end; align-items: center; margin-top: 10px;">
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="Number(total)"
        @size-change="onSizeChange"
        @current-change="onCurrentChange" />
    </div>
    <div style="display: flex; margin-top: 20px;">
      <ElButton
        type="primary"
        style="margin-left: auto;"
        @click="onConfirm">
        确定
      </ElButton>
      <ElButton @click="model = false">
        取消
      </ElButton>
    </div>
  </ElDialog>
  <ElDialog
    v-model="showDetail">
    <Title>基本信息</Title>
    <ElDescriptions
      label-width="160"
      border
      :column="2">
      <ElDescriptionsItem
        label="产品及解决方案名称"
        :span="1">
        {{ productDetail.solutionName }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="所属生态伙伴"
        :span="1">
        {{ productDetail.companyName }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="产品分类"
        :span="1">
        {{ productDetail.productType }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="是否国产化适配"
        :span="1">
        {{ productDetail.isDomesticallyAdapted === 1 ? '是' : '否' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="产品是否软著"
        :span="1">
        {{ productDetail.isProductSoft === 1 ? '是' : '否' }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="部署方式"
        :span="1">
        {{ productDetail.deployment }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="商务模式"
        :span="1">
        {{ productDetail.businessModel }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="标准产品包（元）"
        :span="1">
        {{ productDetail.productPackage }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="实施产品（元）"
        :span="1">
        {{ productDetail.productImplementation }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="产品定制化开发（元）"
        :span="1">
        {{ productDetail.productDevelopment }}
      </ElDescriptionsItem>
      <ElDescriptionsItem
        label="伙伴标签"
        :span="2">
        {{ productDetail.partnerClassList ? productDetail.partnerClassList.join('、') : '' }}
      </ElDescriptionsItem>
    </ElDescriptions>
    <Title style="margin-top: 20px;">
      产品及解决方案附件
    </Title>
    <div>
      <span>{{ productDetail.fileName }}</span>
      <ElButton
        style="margin-left: 10px;"
        link
        type="primary"
        @click="previewFile">
        预览
      </ElButton>
      <ElButton
        link
        type="primary"
        @click="downloadFile">
        下载
      </ElButton>
    </div>
    <div style="display: flex; margin-top: 20px;">
      <ElButton
        type="primary"
        style="margin-left: auto;"
        @click="showDetail = false">
        关闭
      </ElButton>
    </div>
  </ElDialog>
</template>

<script setup>
import { getPartnerClass } from '@/api/ecology/audit.js'
import { getProduct, getProductDetail } from '@/api/presales/pre-project.js'
import Title from '@/components/Title'
import { on_preview_or_downFile } from '@/utils/hooks.js'
import { usePagination } from 'alova/client'
import { ElDialog } from 'element-plus'

const emit = defineEmits(['confirm'])

const elCascaderRef = useTemplateRef('elCascader')

const model = defineModel()

const showDetail = ref(false)

const queryParams = ref({
  productName: '',
  productType: '',
  types: '',
  partnerName: '',
})

const productTypeOptions = ref([])

const productDetail = ref({})

const productType = [
  {
    label: '生态产品',
    value: '生态产品',
  },
  {
    label: '临时产品',
    value: '临时产品',
  },
]

async function getPartnerClassReq() {
  const res = await getPartnerClass()
  productTypeOptions.value = res.data
}

onMounted(() => {
  getPartnerClassReq()
})

const { data, total, page, pageSize, loading, refresh } = usePagination((page, pageSize) => {
  // elCascader全选时只传递父级，未全选时传递子级
  let checkedNodes = elCascaderRef.value.getCheckedNodes()
  checkedNodes = checkedNodes.filter(option => !(option.parent && option.parent.checked))
  const types = checkedNodes.map(item => item.label)
  const { types: originTypes, ...rest } = queryParams.value
  return getProduct({ pageNum: page, pageSize, types, ...rest })
}, {
  append: false, // 数据不追加
  data: res => res.records, // 定义如何取data数据
  total: res => res.total, // 定义如何取total数据
  initialPage: 1, // 设置默认pageNum
  initialPageSize: 10, // 设置默认pageSize
  immediate: false, // 初始化时不立即执行一次
})

function onCurrentChange(pageNum) {
  page.value = pageNum
}

function onSizeChange(newPageSize) {
  page.value = 1
  pageSize.value = newPageSize
}

let selectValue = []
function handleSelectionChange(val) {
  selectValue = val
}

function onConfirm() {
  model.value = false
  emit('confirm', selectValue)
}

function onSearch() {
  if (page.value === 1) {
    refresh()
  } else {
    page.value = 1
  }
}

function onReset() {
  queryParams.value = {
    productName: '',
    productType: '',
    types: '',
    partnerName: '',
  }
  page.value = 1
  pageSize.value = 10
  // 由于这里手动修改了types的值将其设为''，getCheckedNodes方法获取checkedNodes需要在dom更新之后，否则重置时获取到的的还是上一次选中的产品分类，所以这里需要放在nextTick中执行
  nextTick(() => {
    refresh()
  })
}

function onOpen() {
  page.value = 1
  pageSize.value = 10
  onReset()
}

async function openDetailDialog(data) {
  const res = await getProductDetail({ productBid: data.bid })
  productDetail.value = res
  showDetail.value = true
}

function downloadFile() {
  // 创建一个隐藏的 <a> 标签
  const a = document.createElement('a')
  a.href = productDetail.value.fileUrl
  a.download = productDetail.value.fileName // 默认文件名
  a.style.display = 'none'
  // 添加到文档中
  document.body.appendChild(a)
  // 触发点击事件
  a.click()
  // 移除 <a> 标签
  document.body.removeChild(a)
}

function previewFile() {
  on_preview_or_downFile({ url: productDetail.value.fileUrl }, { preview: true })
}

function formatterProductClass(row) {
  try {
    return JSON.parse(row.partnerClass).join('/')
  } catch (error) {
    console.log(error)
    return row.partnerClass
  }
}
</script>

<style lang="scss" scoped></style>
