<template>
  <Title style="margin-top: 20px;">
    项目结项说明
  </Title>
  <ElDescriptions
    :column="1"
    label-width="140"
    border>
    <ElDescriptionsItem label="结项情况说明">
      {{ formData.completionExplain }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="附件">
      <template v-if="formData.completionExplainAttachment">
        <div
          v-for="(item, index) in formData.completionExplainAttachment"
          :key="index"
          style="display: flex; align-items: center;">
          <ElIcon>
            <Document />
          </ElIcon>
          <span style="padding: 2px 0 0 2px;">
            {{ item.match(/[^/\\?#]+$/)[0] }}
          </span>
          <ElButton
            link
            type="primary"
            style="margin-left: 8px;"
            @click="handleDownload(item)">
            下载
          </ElButton>
        </div>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const formData = defineModel()

// 自定义附件下载逻辑
function handleDownload(address) {
  const link = document.createElement('a')
  link.href = address
  // 设置下载文件名
  link.download = address.match(/[^/\\?#]+$/)[0]
  document.body.appendChild(link)
  link.click()
  // 清理 DOM
  document.body.removeChild(link)
}
</script>

<style lang="scss" scoped>

</style>
