<template>
  <DefaultContainer v-loading="loading">
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          流水认领
        </div>
      </div>
      <div
        v-if="!props.id"
        class="right">
        <ElButton @click="router.back()">
          取消
        </ElButton>
        <ElButton
          :loading="saveLoading"
          plain
          type="primary"
          @click="onSubmit(false)">
          暂存
        </ElButton>
        <ElButton
          :loading="saveLoading"
          type="primary"
          @click="onSubmit(true)">
          提交
        </ElButton>
      </div>
    </div>
    <div class="content">
      <ElForm
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="180px"
        :disabled="props.id">
        <ElDivider />
        <div class="sub-title">
          基本信息
        </div>
        <ElFormItem
          class="form-item"
          label="申请人"
          prop="applicant">
          <ElInput
            v-model="form.applicant"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="UI号"
          prop="applicantUi">
          <ElInput
            v-model="form.applicantUi"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="申请人部门"
          prop="applicantDepartment">
          <ElInput
            v-model="form.applicantDepartment"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="部门编码"
          prop="applicantDepartmentCode">
          <ElInput
            v-model="form.applicantDepartmentCode"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="收款方式"
          prop="paymentMethod">
          <ElInput
            v-model="form.paymentMethod"
            disabled
            value="有合同收款" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="扫描方式"
          prop="scanMethod">
          <ElInput
            v-model="form.scanMethod"
            disabled
            value="自助拍照上传" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="认领金额"
          prop="claimAmount">
          <ElInputNumber
            v-model="form.claimAmount"
            style="width: 100%"
            disabled
            placeholder="自动计算" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="业务类型"
          prop="businessType">
          <ElInput
            v-model="form.businessType"
            disabled
            value="服务费收款" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="核算主体"
          prop="calculateEntity">
          <ElSelect
            v-model="form.calculateEntity"
            placeholder="请选择"
            @change="onCalculateEntityChange">
            <ElOption
              v-for="item in accountingSubjectOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="核算主体编码"
          prop="calculateEntityCode">
          <ElInput
            v-model="form.calculateEntityCode"
            disabled
            placeholder="选择核算主体后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="成本中心"
          prop="costCenter">
          <ElSelect
            v-model="form.costCenter"
            placeholder="请选择"
            @change="onCostCenterChange">
            <ElOption
              v-for="item in costCenterOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="成本中心编码"
          prop="costCenterCode">
          <ElInput
            v-model="form.costCenterCode"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <div class="sub-title">
          关联信息
        </div>
        <ElFormItem
          class="form-item"
          label="项目名称"
          prop="projectName">
          <ElSelect
            v-model="form.projectName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectProjectModal = true)
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="项目编号"
          prop="projectCode">
          <ElInput
            v-model="form.projectCode"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同名称"
          prop="contractName">
          <ElSelect
            v-model="form.contractName"
            placeholder="请选择"
            :disabled="!form.projectCode"
            @visible-change="
              (change) => change && (modelIsShow.selectContractModal = true)
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同编号"
          prop="contractNumber">
          <ElInput
            v-model="form.contractNumber"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="客户名称"
          prop="customerName">
          <ElSelect
            v-model="form.customerName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectMerchantModal = true)
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="客户编码"
          prop="customerCode">
          <ElInput
            v-model="form.customerCode"
            disabled
            placeholder="自动输入，不可修改" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="合同开票总金额(不含税:元)">
          <ElInput
            v-model="form.contractInvoiceTotalAmount"
            disabled
            placeholder="选择合同后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="已认领总金额(元)">
          <ElInput
            v-model="form.contractClaimTotalAmount"
            disabled
            placeholder="选择合同后带入" />
        </ElFormItem>

        <div class="sub-title">
          流水明细
          <ElButton
            type="primary"
            style="margin-left: 20px"
            @click="openFlowDetailsDialog">
            选择明细
          </ElButton>
        </div>

        <ElTable
          border
          :data="flowClaimDetailList"
          style="width: 95%; margin-bottom: 20px">
          <ElTableColumn
            prop="incomeType"
            label="收入类型"
            width="120">
            <template #default>
              服务费收款
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="availableAmount"
            label="可认领金额"
            width="120" />
          <ElTableColumn
            prop="claimAmount"
            label="认领金额"
            width="160">
            <template #default="scope">
              <ElInputNumber
                v-model="scope.row.claimAmount"
                style="width: 100%"
                placeholder="请输入"
                controls-position="right"
                :min="0"
                :step="0.01"
                :precision="2"
                :max="Number(scope.row.availableAmount)"
                @input="onClaimAmountChange" />
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="bankFlowNumber"
            label="银行流水号"
            width="150" />
          <ElTableColumn
            prop="flowType"
            label="流水类型"
            width="120" />
          <ElTableColumn
            prop="transactionTime"
            label="交易时间"
            width="150" />
          <ElTableColumn
            prop="transactionAmount"
            label="交易金额"
            width="120" />
          <ElTableColumn
            prop="payerName"
            label="付款方户名"
            width="150" />
          <ElTableColumn
            prop="summary"
            label="摘要"
            min-width="150" />
          <ElTableColumn
            label="操作"
            fixed="right"
            width="80">
            <template #default="scope">
              <ElButton
                type="primary"
                link
                @click="removeFlowDetail(scope.$index)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>

        <br>
        <ElFormItem
          style="width: 95%"
          label="事由"
          prop="remark">
          <ElInput
            v-model="form.remark"
            style="width: 100%"
            type="textarea"
            placeholder="请输入事由"
            :rows="3" />
        </ElFormItem>

        <div class="sub-title">
          附件
        </div>
        <div class="attachment_wrapper">
          <ElUpload
            ref="uploadRef"
            v-model:file-list="fileList"
            class="custom_upload"
            :action="uploadUrl"
            auto-upload
            :on-success="onFileUploadSuccess"
            :on-error="onFileUploadFail"
            multiple
            :on-preview="handlePreview"
            style="padding: 0 20px">
            <template #trigger>
              <ElButton
                link
                icon="link">
                附件
              </ElButton>
            </template>
            <ElButton
              type="primary"
              style="float: right"
              @click="selectFile">
              上传
            </ElButton>
          </ElUpload>
        </div>
      </ElForm>
    </div>

    <!-- 流水明细选择对话框 -->
    <ElDialog
      v-model="flowDetailsDialogVisible"
      title="选择流水明细"
      width="80%"
      :close-on-click-modal="false">
      <ElTable
        ref="flowDetailsTableRef"
        border
        :data="availableFlowDetails"
        @selection-change="handleFlowDetailsSelection">
        <ElTableColumn
          type="selection"
          width="55" />
        <ElTableColumn
          prop="hostid"
          label="银行流水号"
          width="150" />
        <ElTableColumn
          prop="waitClaimAmount"
          label="可认领金额"
          width="120" />
        <ElTableColumn
          prop="dirflag"
          label="流水类型"
          width="120" />
        <ElTableColumn
          prop="actDate"
          label="交易时间"
          width="150">
          <template #default="{ row }">
            {{ row.actDate ? new Date(row.actDate).toLocaleString() : '' }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="amount"
          label="交易金额"
          width="120" />
        <ElTableColumn
          prop="payName"
          label="付款方户名"
          width="150" />
        <ElTableColumn
          prop="explainInfo"
          label="摘要"
          min-width="150" />
        <ElTableColumn
          prop="claimStatusName"
          label="认领状态"
          width="120" />
        <ElTableColumn
          prop="leName"
          label="核算主体"
          width="150" />
        <ElTableColumn
          prop="oppositeName"
          label="往来方"
          width="150" />
      </ElTable>

      <div class="pagination-container">
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="flowDetailsDialogVisible = false">
            取消
          </ElButton>
          <ElButton
            type="primary"
            @click="confirmFlowDetailsSelection">
            确定
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <SelectProjectModal
      v-model="modelIsShow.selectProjectModal"
      @select-item="onChangeProject" />
    <SelectContractModal
      v-model="modelIsShow.selectContractModal"
      :project-code="searchProjectCode"
      @select-item="onChangeContract" />
    <SelectMerchantModal
      v-model="modelIsShow.selectMerchantModal"
      :fill-status="3"
      @select-item="onChangeMerchantInvoice" />
  </DefaultContainer>
</template>

<script setup>
import {
  createOrUpdate,
  getFlowClaimDetails,
  queryClaimFlow,
  submitFlowClaim,
} from '@/api/financialManagement/collectionManagement/flowClaim.js'
import {
  sumAmountBycontractNumber,
} from '@/api/financialManagement/collectionManagement/paymentCollectionRecord.js'
import { getSumAmountByContractNumber } from '@/api/financialManagement/managementOfInvoices/invoiceLedger.js'
import { onSearchCustomerInfo } from '@/api/presales/customer.js'
import { getCurrentProcessId } from '@/api/wflow-pro'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import useUserStore from '@/store/modules/user.js'
import { deepClone } from '@/utils'
import SelectContractModal from '@/views/financialManagement/modules/SelectContractModal.vue'
import SelectMerchantModal from '@/views/financialManagement/modules/SelectMerchantModal.vue'
import SelectProjectModal from '@/views/financialManagement/modules/SelectProjectModal.vue'
import { Back } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
  instanceId: {
    type: String,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  forms: {
    type: Object,
    default: () => ({}),
  },
})
const router = useRouter()

// 表单相关
const formRef = ref()
const loading = ref(false)
const saveLoading = ref(false)
const id = ref()

// 表单数据
const form = ref({
  applicant: '',
  applicantUi: '',
  applicantDepartment: '',
  applicantDepartmentCode: '',
  paymentMethod: '有合同收款',
  scanMethod: '自助拍照上传',
  claimAmount: 0,
  businessType: '服务费收款',
  calculateEntity: '',
  calculateEntityCode: '',
  projectName: '',
  projectCode: '',
  projectMainCode: '',
  contractName: '',
  contractNumber: '',
  contractMainNumber: '',
  customerName: '',
  customerCode: '',
  contractInvoiceTotalAmount: '',
  contractClaimTotalAmount: '',
  remark: '',
})

// 表单验证规则
const rules = reactive({
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' },
  ],
  projectCode: [
    { required: true, message: '请选择项目编码', trigger: 'change' },
  ],
  contractName: [
    { required: true, message: '请选择合同名称', trigger: 'change' },
  ],
  contractNumber: [
    { required: true, message: '请选择合同编号', trigger: 'change' },
  ],
  customerName: [
    { required: true, message: '请选择客户名称', trigger: 'change' },
  ],
  customerCode: [
    { required: true, message: '请选择客户编码', trigger: 'change' },
  ],
  contractInvoiceTotalAmount: [
    { required: true, message: '请输入合同开票总金额', trigger: 'change' },
  ],
  contractClaimTotalAmount: [
    { required: true, message: '请输入已认领总金额', trigger: 'change' },
  ],
})

// 选项数据
const accountingSubjectOptions = ref([
  {
    label: '服务费收款',
    value: '1',
    code: 'serviceFee',
  },
  {
    label: '其他',
    value: '2',
    code: 'other',
  },
])

const costCenterOptions = ref([

  {
    label: '服务费收款',
    value: '1',
    code: 'serviceFee',
  },
])

// 流水明细相关
const flowClaimDetailList = ref([])
const flowDetailsDialogVisible = ref(false)
const availableFlowDetails = ref([])
const selectedFlowDetails = ref([])
const flowDetailsTableRef = ref()

// 分页相关
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  orders: {},
})

// 文件上传相关
const uploadRef = ref()
const fileList = ref([])
const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址
const userInfo = useUserStore().userInfo

const modelIsShow = reactive({
  selectProjectModal: false,
  selectContractModal: false,
  selectMerchantModal: false,
})

const searchProjectCode = computed(() => {
  if (form.value.projectCode) {
    return form.value.projectCode.split('-D')[0]
  }
  return ''
})

// 使用计算属性来单独追踪 contractNumber
const contractNumber = computed(() => form.value.contractNumber)

// 只监听 contractNumber 的变化
watch(
  contractNumber,
  (newVal) => {
    console.log('合同编号变更为:', newVal)
    if (newVal) {
      // 这里可以添加当合同编号变化时需要执行的逻辑
      getAmountOfMoneyInfo(newVal)
    }
  },
  {
    immediate: true,
  },
)

/**
 * 生命周期钩子：组件挂载完成后初始化
 */
onMounted(() => {
  init()
})

/**
 * 初始化方法
 */
function init() {
  if (props.id || router.currentRoute.value.query.id) {
    id.value = props.id || router.currentRoute.value.query.id
    getFormData()
  } else {
    // 初始化用户信息
    initUserInfo()
  }
}

/**
 * 初始化用户信息
 */
function initUserInfo() {
  console.log(userInfo, '---- userInfo')
  form.value.applicant = userInfo.nickName
  form.value.applicantUi = userInfo.userName
  form.value.applicantDepartment = userInfo?.dept?.deptName
  form.value.applicantDepartmentCode = userInfo?.dept?.code
}

/**
 * 获取表单数据（编辑模式）
 */
function getFormData() {
  loading.value = true

  getFlowClaimDetails({ id: id.value })
    .then((res) => {
      console.log(res, '---- getFormData')
      if (res.code === 200) {
        form.value = deepClone(res.data)
        flowClaimDetailList.value = res.data.flowClaimDetailList || []
        if (form.value.remarkAttachment) {
          try {
            let parsedArr = JSON.parse(form.value.remarkAttachment)
            parsedArr = parsedArr.map(item => ({
              name: item.match(/[^/\\?#]+$/)[0],
              url: item,
            }))
            fileList.value = parsedArr
          } catch (error) {
            console.log(error)
          }
        }
      }
    })
    .catch((err) => {
      console.log(err, '---- getFlowClaimDetails')
    })
    .finally(() => {
      loading.value = false
    })
}

/**
 * 核算主体变更事件
 */
function onCalculateEntityChange(value) {
  const selected = accountingSubjectOptions.value.find(item => item.value === value)
  if (selected) {
    form.value.calculateEntityCode = selected.code
  }
  // 清空关联信息
  resetRelatedInfo()
}

function onCostCenterChange(value) {
  const selected = costCenterOptions.value.find(item => item.value === value)
  if (selected) {
    form.value.costCenterCode = selected.code
  }
}

/**
 * 项目变更事件
 */
function onChangeProject(project) {
  // 项目
  console.log(project, '---- onChangeProject')
  if (project) {
    form.value.projectCode = project.projectCode
    form.value.projectName = project.projectName || project.bolProjectName
    form.value.projectMainCode = project.masterCode
    onChangeContract()
    getCustomerInfo(project.customerId)
  } else {
    form.value.projectCode = ''
    form.value.projectName = ''
    form.value.projectMainCode = ''
    onChangeContract()
  }
}

/**
 * 合同变更事件
 */
function onChangeContract(contract) {
  console.log(contract, '---- onChangeContract')
  // 合同
  if (contract) {
    form.value.contractName = contract.name
    form.value.contractNumber = contract.code
    form.value.contractMainNumber = contract.contractNumber
  } else {
    form.value.contractName = ''
    form.value.contractNumber = ''
    form.value.contractMainNumber = ''
  }
}

async function getCustomerInfo(customerId) {
  const res = await onSearchCustomerInfo(customerId)
  const { merchantBasicInformation } = res.data
  form.value.customerName = merchantBasicInformation.name
  form.value.customerCode = merchantBasicInformation.code
}

/**
 * 获取合同相关金额
 */
async function getAmountOfMoneyInfo(contractNumber) {
  const claimedAmountRes = await sumAmountBycontractNumber({
    contractNumber,
    excludeBid: id.value,
  })
  if (claimedAmountRes.code === 200) {
    form.value.contractInvoiceTotalAmount = claimedAmountRes.data
  }
  const accumulatedInvoicingRes = await getSumAmountByContractNumber({
    contractNumber,
    type: 1,
  })
  if (accumulatedInvoicingRes.code === 200) {
    form.value.contractClaimTotalAmount = accumulatedInvoicingRes.data
  }
}

function onChangeMerchantInvoice(businessEntity) {
  // 单位
  if (businessEntity) {
    form.value.customerName = businessEntity.name
    form.value.customerCode = businessEntity.code
  }
}

/**
 * 重置关联信息
 */
function resetRelatedInfo() {
  form.value.projectName = ''
  form.value.projectCode = ''
  form.value.projectMainCode = ''
  form.value.contractName = ''
  form.value.contractNumber = ''
  form.value.contractMainNumber = ''
  form.value.customerName = ''
  form.value.customerCode = ''
  form.value.contractInvoiceTotalAmount = ''
  form.value.contractClaimTotalAmount = ''
}

/**
 * 打开流水明细选择对话框
 */
async function openFlowDetailsDialog() {
  try {
    loading.value = true
    // 调用API获取可用的流水明细数据
    const res = await queryClaimFlow({
      page: {
        orders: {},
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
      },
      params: {
      },
    })
    if (res.code === 200) {
      availableFlowDetails.value = res.data.list || []
      pagination.total = res.data.total || 0
      // 设置已选中的流水明细
      if (flowClaimDetailList.value.length > 0) {
        const selectedIds = flowClaimDetailList.value.map(item => item.id)
        nextTick(() => {
          availableFlowDetails.value.forEach((row) => {
            if (selectedIds.includes(row.id)) {
              flowDetailsTableRef.value?.toggleRowSelection(row, true)
            }
          })
        })
      }
    }
  } catch (error) {
    console.error('获取流水明细失败:', error)
    ElMessage.error('获取流水明细失败')
  } finally {
    loading.value = false
    flowDetailsDialogVisible.value = true
  }
}

/**
 * 流水明细选择变更
 */
function handleFlowDetailsSelection(selection) {
  selectedFlowDetails.value = selection
}

/**
 * 确认选择流水明细
 */
function confirmFlowDetailsSelection() {
  // 过滤掉已存在的流水明细
  const newDetails = selectedFlowDetails.value.filter(selected =>
    !flowClaimDetailList.value.some(existing => existing.id === selected.id),
  )

  // 添加新的流水明细
  flowClaimDetailList.value.push(...newDetails.map(item => ({
    ...item,
    incomeType: '服务费收款',
    claimAmount: 0,
    availableAmount: item.waitClaimAmount, // 使用 waitClaimAmount 作为可认领金额
    bankFlowNumber: item.hostid, // 使用 hostid 作为银行流水号
    flowType: item.dirflag, // 使用 dirflag 作为流水类型
    transactionTime: item.actDate, // 使用 actDate 作为交易时间
    transactionAmount: item.amount, // 使用 amount 作为交易金额
    payerName: item.payName, // 使用 payName 作为付款方户名
    summary: item.explainInfo, // 使用 explainInfo 作为摘要
  })))

  flowDetailsDialogVisible.value = false
  selectedFlowDetails.value = []
  calculateTotalClaimAmount()
}

/**
 * 删除流水明细
 */
function removeFlowDetail(index) {
  flowClaimDetailList.value.splice(index, 1)
  calculateTotalClaimAmount()
}

/**
 * 认领金额变更事件
 */
function onClaimAmountChange() {
  calculateTotalClaimAmount()
}

/**
 * 计算总认领金额
 */
function calculateTotalClaimAmount() {
  const total = flowClaimDetailList.value.reduce((sum, item) => {
    return sum + (Number.parseFloat(item.claimAmount) || 0)
  }, 0)
  form.value.claimAmount = total
}

function selectFile() {
  uploadRef.value.$el.querySelector('input').click()
}

function onFileUploadSuccess() {
  ElMessage.success('附件上传成功')
}

function onFileUploadFail() {
  ElMessage.error('附件上传失败')
}

function handlePreview(file) {
  console.log(file, '---- handlePreview')
  if (file?.url) {
    window.open(file.url)
  }
  if (file?.response?.data) {
    window.open(file.response.data)
  }
}

/**
 * 处理页码变化
 */
async function handlePageChange(page) {
  pagination.pageNum = page
  await openFlowDetailsDialog()
}

/**
 * 处理每页条数变化
 */
async function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.pageNum = 1
  await openFlowDetailsDialog()
}

/**
 * 提交
 */
async function onSubmit(isSubmit) {
  if (flowClaimDetailList.value.length === 0 && isSubmit) {
    ElMessage.warning('请选择流水明细')
    return
  }
  let valid = true
  if (isSubmit) {
    valid = await formRef.value.validate()
  }
  if (valid) {
    const submitData = {
      ...form.value,
      flowClaimDetailList: flowClaimDetailList.value,
    }
    if (fileList.value.length > 0) {
      const addressArr = fileList.value
        .map(item => item.url || item.response.data)
        .filter(address => address)
      if (addressArr.length > 0) {
        submitData.remarkAttachment = JSON.stringify(addressArr)
      } else {
        submitData.remarkAttachment = ''
      }
    } else {
      submitData.remarkAttachment = ''
    }
    console.log(fileList.value, '--- fileList.value')
    console.log(submitData, '--- submitData')
    const apiCall = isSubmit ? submitFlowClaim : createOrUpdate
    saveLoading.value = true
    if (isSubmit) {
      submitData.processDefId = await getCurrentProcessId('flow-claim')
      console.log(submitData.processDefId, '--- submitData.processDefId')
    }
    apiCall(submitData)
      .then((res) => {
        if (res.code === 200) {
          ElMessage.success(isSubmit ? '提交成功' : '保存成功')
          router.back()
        } else {
          ElMessage.error(res.msg || '操作失败')
        }
      })
      .catch((err) => {
        console.log(err, '---- submit')
        ElMessage.error('操作失败')
      })
      .finally(() => {
        saveLoading.value = false
      })
  }
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(form))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important;

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .form-item {
    width: 360px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}

.dialog-footer {
  text-align: right;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>

<style lang="scss">
.custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
