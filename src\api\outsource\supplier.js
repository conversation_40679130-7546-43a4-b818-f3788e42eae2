import { get, post, put } from '@/utils/alova.js'

const prefix = '/outsource/supplier'
/**
 * 供应商列表
 */
export function getSupplierList(params) {
  return get(`${prefix}/list`, params)
}
/**
 * 供应商下拉列表
 */
export function getSupplierSelectList() {
  return get(`${prefix}/all`)
}
/**
 * 导出列表
 */
export function exportSupplierList(data) {
  return post(`${prefix}/export`, data, {
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
/**
 * 添加供应商
 */
export function addSupplier(data, config = { transformRes: false }) {
  return put(`${prefix}`, data, config)
}
/**
 * 修改供应商
 */
export function editSupplier(data, config = { transformRes: false }) {
  return post(`${prefix}`, data, config)
}
