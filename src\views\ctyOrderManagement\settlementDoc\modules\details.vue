<template>
  <DefaultContainer>
    <div
      v-if="!props.id"
      class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          结算单详情
        </div>
      </div>
    </div>
    <div class="content">
      <div class="sub-title">
        楚天云结算单信息
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="240">
        <ElDescriptionsItem
          v-for="item in descriptionList1"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
        <ElDescriptionsItem
          :span="3"
          width="398">
          <template #label>
            <div>结算单明细附件</div>
          </template>
          <p
            v-for="file in fileList"
            :key="file.url"
            class="file-text"
            @click="downloadFile(file.url, file.name)">
            {{ file.name }}
          </p>
        </ElDescriptionsItem>
      </ElDescriptions>
      <div class="sub-title">
        数产集团人员核对
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="240">
        <ElDescriptionsItem
          v-for="item in descriptionList2"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </DefaultContainer>
</template>

<script setup>
import { getSettlementDocDetail } from '@/api/ctyOrderManagement/settlementDoc.js'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { Back } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
})

const router = useRouter()
const fileList = ref([])
const descriptionList1 = [
  { label: '结算年度', value: 'year', span: 1 },
  { label: '订单部门', value: 'deptName', span: 1 },
  { label: '经办人', value: 'handledByName', span: 1 },
  { label: '结算单编号', value: 'formNo', span: 1 },
  { label: '结算单名称', value: 'statementName', span: 2 },
  { label: '结算发起单位', value: 'initiateUnit', span: 1 },
  { label: '应用系统名称', value: 'ctAppName', span: 2 },
  { label: '客户名称', value: 'customerName', span: 1 },
  { label: '结算依据单价合同名称', value: 'protocolName', span: 2 },
  { label: '结算依据单价合同编号', value: 'protocolCode', span: 1 },
  { label: '项目名称', value: 'projectName', span: 2 },
  { label: '项目编号', value: 'projectCode', span: 1 },
  { label: '数产项目经理', value: 'projectManagerName', span: 1 },
  { label: '市场经理', value: 'marketManagerName', span: 2 },
  { label: '结算开始计费日期', value: 'chargeDateStart', span: 1 },
  { label: '结算结束计费日期', value: 'chargeDateEnd', span: 1 },
  { label: '结算周期(天)', value: 'statementCycle', span: 1 },
  { label: '结算总金额(元)', value: 'totalAmount', span: 1 },
  { label: '申请日期', value: 'applicationTime', span: 2 },
  { label: '备注', value: 'remark', span: 3 },
]

const descriptionList2 = [
  { label: '数产市场经理', value: 'marketManagerName', span: 1 },
  { label: '关联结算单编号', value: 'formNo', span: 2 },
  { label: '项目名称', value: 'projectName', span: 2 },
  { label: '项目编号', value: 'projectCode', span: 1 },
  { label: '销售合同名称', value: 'saleContractName', span: 2 },
  { label: '销售合同编号', value: 'saleContractNumber', span: 1 },
  { label: '项目云预算', value: 'budgetAmount', span: 1 },
  { label: '合同中的云服务开始日期', value: 'contractServiceStartTime', span: 1 },
  { label: '合同中的云服务结束日期', value: 'contractServiceEndTime', span: 1 },
  { label: '合同中的云服务履约周期(天)', value: 'contractServicePerformanceCycle', span: 1 },
  { label: '预算有效周期(天)', value: 'budgetEffectivePeriod', span: 1 },
  { label: '本次拟结算金额(元)', value: 'proposedSettlementAmount', span: 2 },
  { label: '云回款金额(元)', value: 'collectionAmount', span: 1 },
  { label: '合同中的云服务金额(元)', value: 'contractServiceAmount', span: 2 },
  { label: '拟同意付款金额(元)', value: 'proposedPaymentAmount', span: 3 },
]
const detail = ref({})
const id = ref()
onMounted(() => {
  if (props.id) {
    id.value = props.id
  } else {
    id.value = router.currentRoute.value.query.id
  }
  getSettlementDocDetail({ id: id.value }).then((res) => {
    if (res.code === 200) {
      detail.value = res.data
      if (detail.value.attachment) {
        try {
          let parsedArr = JSON.parse(detail.value.attachment)
          parsedArr = parsedArr.map(item => ({
            name: item.match(/[^/\\?#]+$/)[0],
            url: item,
          }))
          fileList.value = parsedArr
        } catch (error) {
          console.log(error)
        }
      }
    }
  })
})

function downloadFile(url, filename) {
  const link = document.createElement('a')
  link.href = url
  link.download = filename // 设置下载的文件名
  document.body.appendChild(link)
  link.click() // 触发点击事件
  document.body.removeChild(link) // 移除元素
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(detail))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

    <style lang="scss" scoped>
  .header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 8px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  position: relative;
  margin-top: 20px;

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .remark-attachment {
    &:hover {
      color: #409eff;
      cursor: pointer;
    }
  }
}

.file-text {
  cursor: pointer;

  &:hover {
    color: #409eff;
    cursor: pointer;
  }
}
</style>
