import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 客户企业性质分析数据
 * @param {*} data
 * @returns
 */
export function getClientNatureData(data) {
  return request({
    url: `${prefix}/customer/analysis/enterprise/nature`,
    method: 'post',
    data,
  })
}

/**
 * 客户地区分类分析数据
 * @param {*} data
 * @returns
 */
export function getClientDistrictData(data) {
  return request({
    url: `${prefix}/customer/analysis/area/class`,
    method: 'post',
    data,
  })
}

/**
 * 客户合同额分析数据
 * @param {*} data
 * @returns
 */
export function getClientAmountData(data) {
  return request({
    url: `${prefix}/customer/analysis/contract/value`,
    method: 'post',
    data,
  })
}

/**
 * 客户历年回款分析数据
 * @param {*} data
 * @returns
 */
export function getClientPaymentData(data) {
  return request({
    url: `${prefix}/customer/analysis/contract/receive`,
    method: 'post',
    data,
  })
}

/**
 * 客户历年回款分析详情数据
 * @param {*} data
 * @returns
 */
export function getCustomerPaymentDetail(params) {
  return request({
    url: `${prefix}/customer/analysis/contract/receive/detail`,
    method: 'get',
    params,
  })
}

/**
 * 市场经理销售目标分析数据
 * @param {*} data
 * @returns
 */
export function getManagerTargetData(data) {
  return request({
    url: `${prefix}/customer/analysis/target/list`,
    method: 'post',
    data,
  })
}

/**
 * 市场经理销售目标详情数据
 * @param {*} data
 * @returns
 */
export function getManagerTargetDetail(params) {
  return request({
    url: `${prefix}/customer/analysis/target/query`,
    method: 'get',
    params,
  })
}

/**
 * 市场经理销售目标保存数据
 * @param {*} data
 * @returns
 */
export function saveManagerTargetData(data) {
  return request({
    url: `${prefix}/customer/analysis/target/write`,
    method: 'post',
    data,
  })
}
