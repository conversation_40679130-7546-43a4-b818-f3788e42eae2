<template>
  <div class="">
    <ElTable
      height="331px"
      style="width: 100%;"
      :data="tableData">
      <ElTableColumn
        label="序号"
        type="index"
        width="55"
        fixed="left" />
      <ElTableColumn
        label="项目名称"
        prop="projectName"
        width="130" />
      <ElTableColumn
        label="合同额（万）"
        prop="contractAmount"
        width="110" />
      <ElTableColumn
        label="交付主体"
        prop="deliverySubject"
        width="100" />
      <ElTableColumn
        label="项目经理"
        prop="projectManager"
        width="100" />
      <ElTableColumn
        label="确收比例"
        prop="confirmedReceiptRate"
        width="100" />
      <ElTableColumn
        label="回款比例"
        prop="backPaymentRate"
        width="100" />
      <ElTableColumn
        label="预算利润率"
        prop="budgetProfitRate"
        width="110" />
      <ElTableColumn
        label="动态利润率"
        prop="dynamicProfitRate"
        width="100" />
    </ElTable>
  </div>
</template>

<script setup>
const tableData = ref([{
  projectName: '项目1',
  contractAmount: 100,
  deliverySubject: '主体1',
  projectManager: '张三',
  confirmedReceiptRate: '80%',
  backPaymentRate: '70%',
  budgetProfitRate: '20%',
  dynamicProfitRate: '15%',
}, {
  projectName: '项目2',
  contractAmount: 200,
  deliverySubject: '主体2',
  projectManager: '李四',
  confirmedReceiptRate: '70%',
  backPaymentRate: '60%',
  budgetProfitRate: '15%',
  dynamicProfitRate: '10%',
}, {
  projectName: '项目3',
  contractAmount: 300,
  deliverySubject: '主体3',
  projectManager: '王五',
  confirmedReceiptRate: '60%',
  backPaymentRate: '50%',
  budgetProfitRate: '10%',
  dynamicProfitRate: '5%',
}, {
  projectName: '项目4',
  contractAmount: 400,
  deliverySubject: '主体4',
  projectManager: '赵六',
  confirmedReceiptRate: '50%',
  backPaymentRate: '40%',
  budgetProfitRate: '5%',
  dynamicProfitRate: '0%',
}, {
  projectName: '项目5',
  contractAmount: 500,
  deliverySubject: '主体5',
  projectManager: '钱七',
  confirmedReceiptRate: '40%',
  backPaymentRate: '30%',
  budgetProfitRate: '0%',
  dynamicProfitRate: '-5%',
}, {
  projectName: '项目6',
  contractAmount: 600,
  deliverySubject: '主体6',
  projectManager: '孙八',
  confirmedReceiptRate: '30%',
  backPaymentRate: '20%',
  budgetProfitRate: '-5%',
  dynamicProfitRate: '-10%',
}, {
  projectName: '项目7',
  contractAmount: 700,
  deliverySubject: '主体7',
  projectManager: '周九',
  confirmedReceiptRate: '20%',
  backPaymentRate: '10%',
  budgetProfitRate: '-10%',
  dynamicProfitRate: '-15%',
}, {
  projectName: '项目8',
  contractAmount: 800,
  deliverySubject: '主体8',
  projectManager: '吴十',
  confirmedReceiptRate: '10%',
  backPaymentRate: '5%',
  budgetProfitRate: '-15%',
  dynamicProfitRate: '-20%',
}, {
  projectName: '项目9',
  contractAmount: 900,
  deliverySubject: '主体9',
  projectManager: '郑十一',
  confirmedReceiptRate: '5%',
  backPaymentRate: '2.5%',
  budgetProfitRate: '-20%',
  dynamicProfitRate: '-25%',
}, {
  projectName: '项目10',
  contractAmount: 1000,
  deliverySubject: '主体10',
  projectManager: '冯十二',
  confirmedReceiptRate: '2.5%',
  backPaymentRate: '1.25%',
  budgetProfitRate: '-25%',
  dynamicProfitRate: '-30%',
}])
</script>

<style lang="scss" scoped>

</style>
