<template>
  <ElPopover
    placement="bottom-start"
    trigger="click"
    width="calc(50% - 30px)"
    :hide-after="0"
    :show-arrow="false">
    <template #reference>
      <ElButton>
        <i
          class="iconfont icon-sift"
          :style="{
            marginRight: '8px',
          }" />
        所有筛选
        <ElIcon
          :style="{
            marginLeft: '10px',
          }">
          <ArrowDown />
        </ElIcon>
      </ElButton>
    </template>
    <div style="padding: 20px 4px;">
      <ElForm label-width="auto">
        <ElRow :gutter="20">
          <ElCol :span="8">
            <ElFormItem label="项目名称">
              <ElInput
                v-model="queryParams.params.prdProjectName"
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <div style="display: flex; justify-content: flex-end; align-items: center;">
              <ElButton @click="onReset">
                重置
              </ElButton>
              <ElButton
                type="primary"
                @click="onSearch">
                搜索
              </ElButton>
            </div>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
  </ElPopover>
  <ElTable
    v-loading="loading"
    :data="data"
    max-height="calc(70vh - 80px)"
    style="margin-top: 10px;">
    <ElTableColumn
      label="序号"
      width="60"
      type="index" />
    <ElTableColumn
      label="项目名称"
      prop="prdProjectName" />
    <ElTableColumn
      label="项目编号"
      prop="prdProjectCode" />
    <ElTableColumn
      label="研发负责人"
      prop="principal" />
    <ElTableColumn
      label="立项日期"
      prop="projectInitiationDate" />
    <ElTableColumn
      label="操作"
      width="80">
      <template #default="{ row }">
        <ElButton
          link
          type="primary"
          @click="onAssociate(row)">
          关联
        </ElButton>
      </template>
    </ElTableColumn>
  </ElTable>
  <div style="display: flex; justify-content: flex-end; align-items: center; height: 40px;">
    <ElPagination
      v-model:current-page="page"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, prev, pager, next, jumper"
      :total="Number(total)"
      @size-change="onSizeChange"
      @current-change="onCurrentChange" />
  </div>
</template>

<script setup>
import { getProjectLibraryPageList } from '@/api/project-development/project-library.js'
import { usePagination } from 'alova/client'

const emit = defineEmits(['associate'])

function onAssociate(row) {
  emit('associate', row, '研发项目')
}

const queryParams = ref({
  page: {
    orders: [
      {
        asc: true,
        field: '',
      },
    ],
    pageNum: 0,
    pageSize: 0,
  },
  params: {
    prdProjectName: '',
  },
})

const { data, loading, total, page, pageSize, refresh } = usePagination((page, pageSize) => {
  queryParams.value.page.pageNum = page
  queryParams.value.page.pageSize = pageSize
  return getProjectLibraryPageList({ ...queryParams.value })
}, {
  append: false, // 数据不追加
  data: res => res.list, // 定义如何取data数据
  total: res => res.total, // 定义如何取total数据
  initialPage: 1, // 设置默认pageNum
  initialPageSize: 10, // 设置默认pageSize
  immediate: false, // 不立即执行一次
})

function onCurrentChange(pageNum) {
  page.value = pageNum
}

function onSizeChange(newPageSize) {
  page.value = 1
  pageSize.value = newPageSize
}

function onSearch() {
  if (page.value === 1) {
    refresh()
  } else {
    page.value = 1
  }
}

function onReset() {
  queryParams.value = {
    page: {
      orders: [
        {
          asc: true,
          field: '',
        },
      ],
      pageNum: 0,
      pageSize: 0,
    },
    params: {
      prdProjectName: '',
    },
  }
  page.value = 1
  pageSize.value = 10
  refresh()
}

defineExpose({
  refresh: onReset,
})
</script>

<style lang="scss" scoped></style>
