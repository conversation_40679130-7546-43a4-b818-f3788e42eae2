<template>
  <ElScrollbar>
    <div class="pane">
      <header>
        <ElButton
          type="primary"
          @click="handleChange">
          <template #icon>
            <i class="iconfont icon-lcbgl_bgsq" />
          </template>
          里程碑变更申请
        </ElButton>
      </header>
      <div class="pane_main pb-[20px]">
        <div
          v-for="(vItem, vIndex) in versionList"
          :key="vIndex"
          class="version">
          <ElDivider>
            <h2
              :class="{
                active: vIndex === 0,
              }">
              版本V{{ versionList.length - vIndex }}
            </h2>
          </ElDivider>
          <ElTable
            v-loading="loading"
            border
            :max-height="500"
            :data="vItem">
            <ElTableColumn
              type="index"
              label="序号"
              align="center" />
            <ElTableColumn
              label="里程碑名称"
              prop="milestoneName"
              align="center" />
            <ElTableColumn
              label="里程碑类型"
              prop="milestoneType"
              align="center" />
            <ElTableColumn
              label="计划开始日期"
              prop="planStartDate"
              align="center" />
            <ElTableColumn
              label="计划结束日期"
              prop="planEndDate"
              align="center" />
            <ElTableColumn
              label="计划回款日期"
              prop="refundDate"
              align="center" />
            <ElTableColumn
              label="计划回款金额（元）"
              prop="refundAmount"
              align="center" />
            <ElTableColumn
              label="本次里程碑累计形象进度(%)"
              prop="progress"
              min-width="80"
              align="center">
              <template #default="{ row }">
                <ElProgress :percentage="Number(row.progress)" />
              </template>
            </ElTableColumn>
          </ElTable>
        </div>
      </div>
    </div>
  </ElScrollbar>
</template>

<script setup>
import { getBaseMilestonesList } from '@/api/project-manage/repository.js'
import { useRequest } from 'alova/client'

const emit = defineEmits(['updateCount'])

const router = useRouter()
const route = useRoute()
// 跳转到变更申请页面
function handleChange() {
  router.push({
    path: '/project-manage/repository/detail/change-apply',
    query: route.query,
  })
}

const { loading, data } = useRequest(getBaseMilestonesList(route.query.pcode), {
  initialData: [],
})

// 版本列表 分组并排序，同时更新版本数量信息到父组件
const versionList = computed(() => {
  if (data.value.length === 0) {
    return []
  }

  const grouped = {}

  // 1. 分组
  for (const obj of data.value) {
    if (typeof obj !== 'object' || obj === null || !('versionNum' in obj) || typeof obj.versionNum !== 'number') {
      console.warn('数组中包含无效对象或对象缺少 versionNum 属性，已跳过：', obj) // 或者抛出错误，根据需要决定
      continue
    }
    const version = obj.versionNum
    if (!grouped[version]) {
      grouped[version] = []
    }
    grouped[version].push(obj)
  }

  // 2. 转换为二维数组并排序
  const result = Object.entries(grouped)
    .sort((a, b) => Number(b[0]) - Number(a[0])) // 按 versionNum 降序排序
    .map(entry => entry[1]) // 提取分组后的数组

  // 更新版本数量信息到父组件
  emit('updateCount', result.length - 1)

  return result
})
</script>

<style lang="scss" scoped>
.pane {
  height: 100%;

  .version {
    & + .version {
      margin-top: 50px;
    }

    h2 {
      color: rgb(0 0 0 / 45%);
      font-size: 18px;

      &.active {
        color: #0073ff;
      }
    }
  }
}
</style>
