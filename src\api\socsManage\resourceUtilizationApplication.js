import request from '@/utils/request'

// 获取应用申请分页列表
export function getGzCloudResourceRequestPageList(params) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/pageList',
    method: 'post',
    data: params,
  })
}

// 提交资源申请
export function submitResourceRequest(data) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/submit-resource-request',
    method: 'post',
    data,
  })
}

// 提交资源更新
export function submitResourceUpdate(data) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/submit-resource-update',
    method: 'post',
    data,
  })
}

// 根据应用id和项目编码 查询已审核通过的数据
export function getGzCloudResourceRequestHisByAppidProjectcode(appId, projectCode, saleContractNumber, alreadySettled) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/his-by-appid-projectcode',
    method: 'get',
    params: {
      appId,
      projectCode,
      saleContractNumber,
      alreadySettled,
    },
  })
}

// 下载资源使用申请单
export function downloadResourceRequest(params) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/download',
    method: 'get',
    params,
  })
}

// 获取详情
export function getGzCloudResourceRequestDetail(params) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/detail',
    method: 'get',
    params,
  })
}

// 删除资源申请
export function deleteResourceRequest(params) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/del',
    method: 'get',
    params,
  })
}

// 暂存、更新资源申请信息
export function createOrUpdateResourceRequest(data) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/createOrUpdate',
    method: 'post',
    data,
  })
}

// 提交并审核
export function submitWithApprove(data) {
  return request({
    url: '/cloudorder/gz-cloud-resource-request/submit-with-approve',
    method: 'post',
    data,
  })
}
