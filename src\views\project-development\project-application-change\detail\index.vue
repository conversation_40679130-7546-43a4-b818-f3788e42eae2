<template>
  <Container show-back>
    <div class="wrapper">
      <ElForm label-position="top">
        <ProjectChangeDescription v-model="formData" />
        <BaseInfo v-model="formData" />
        <BudgetInfo v-model="formData" />
        <OwnProductsAndServices v-model="formData" />
        <DevProjectRevenueForecast v-model="formData" />
      </ElForm>
    </div>
  </Container>
</template>

<script setup>
import { getApplicationChangeDetailByBid } from '@/api/project-development/project-application-change.js'
import Container from '@/components/Container/index.vue'
import { useRoute } from 'vue-router'
import BaseInfo from './baseInfo.vue'
import BudgetInfo from './budgetInfo.vue'
import DevProjectRevenueForecast from './devProjectRevenueForecast.vue'
import OwnProductsAndServices from './ownProductsAndServices.vue'
import ProjectChangeDescription from './projectChangeDescription.vue'

const props = defineProps({
  id: {
    default: '',
  },
})

const route = useRoute()

const formData = ref({
  projectChangeDraft: {
    budgetInfo: {
      outsourcingCost: [],
      jobLaborCost: [],
      manageCost: [],
      budgetOverview: {
        manageCost: [],
      },
    },
    incomeDetails: [],
    selfOwnedProductsServicesDetail: [],
  },
  prdProjectHistory: {
    budgetInfo: {
      outsourcingCost: [],
      jobLaborCost: [],
      manageCost: [],
      budgetOverview: {
        manageCost: [],
      },
    },
    incomeDetails: [],
    selfOwnedProductsServicesDetail: [],
  },
})

onMounted(async () => {
  if (route.query.id) {
    const res = await getApplicationChangeDetailByBid(route.query.id)
    formData.value = res
  } else if (props.id) {
    const res = await getApplicationChangeDetailByBid(props.id)
    formData.value = res
  }
})

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(formData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve(readonly(unref(formData)))
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.wrapper {
  flex: auto;
  overflow-y: auto;
  padding: 10px 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
