<template>
  <ElDivider>市场经理核对</ElDivider>
  <ElRow :gutter="20">
    <ElCol :span="20">
      1、销售合同(包括补充协议)已全部回款 ?
    </ElCol>
    <ElCol :span="4">
      <ElRadioGroup
        v-model="formData.marketManagerVerification.contractAllPaid"
        :disabled="formType !== 'process'">
        <ElRadio
          label="是"
          :value="1" />
        <ElRadio
          label="否"
          :value="0" />
      </ElRadioGroup>
    </ElCol>
  </ElRow>
  <ElRow :gutter="20">
    <ElCol :span="20">
      2、市场、方案人员已完成项目相关报销 ?
    </ElCol>
    <ElCol :span="4">
      <ElRadioGroup
        v-model="formData.marketManagerVerification.marketReimbursementDone"
        :disabled="formType !== 'process'">
        <ElRadio
          label="是"
          :value="1" />
        <ElRadio
          label="否"
          :value="0" />
      </ElRadioGroup>
    </ElCol>
  </ElRow>
</template>

<script setup>
const { formType } = defineProps({
  formType: {
    type: String,
    default: '',
  },
})
const formData = defineModel('formData', {
  type: Object,
  default: () => ({}),
})
</script>

<style lang="scss" scoped>

</style>
