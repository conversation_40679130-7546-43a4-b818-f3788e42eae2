<template>
  <ElDialog
    v-model="model"
    title="选择结算单"
    width="1000px"
    @open="onOpen">
    <ElTable
      v-loading="loading"
      style="margin-top: 20px"
      max-height="calc(70vh - 100px)"
      :data="tableData">
      <ElTableColumn
        label=""
        width="80">
        <template #default="{ row }">
          <ElButton
            link
            type="primary"
            @click="selectItem(row)">
            选择
          </ElButton>
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="序号"
        type="index"
        :index="indexMethod"
        width="50"
        fixed="left"
        align="center" />
      <ElTableColumn
        label="结算单编号"
        prop="formNo"
        min-width="120"
        align="center" />
      <ElTableColumn
        label="结算单名称"
        min-width="120"
        prop="statementName"
        align="center" />
      <ElTableColumn
        label="项目名称"
        min-width="120"
        prop="projectName"
        align="center" />
      <ElTableColumn
        label="销售合同名称"
        min-width="120"
        prop="saleContractName"
        align="center" />
      <ElTableColumn
        label="合同中云服务履约周期(天)"
        min-width="120"
        prop="contractServicePerformanceCycle"
        align="center" />
      <ElTableColumn
        label="项目云预算金额(元)"
        min-width="120"
        prop="budgetAmount"
        align="center" />
      <ElTableColumn
        label="预算有效周期(天)"
        min-width="120"
        prop="budgetEffectivePeriod"
        align="center" />
      <ElTableColumn
        label="合同中的云服务金额(元)"
        min-width="120"
        prop="contractServiceAmount"
        align="center" />
      <ElTableColumn
        label="云回款金额(元)"
        min-width="120"
        prop="collectionAmount"
        align="center" />
    </ElTable>
    <div
      style="
              display: flex;
              justify-content: flex-end;
              align-items: center;
              margin-top: 10px;
            ">
      <div class="pagination-container">
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total,prev,pager,next,sizes,jumper"
          :total="pagination.total"
          @size-change="onSizeChange"
          @current-change="onSearch" />
      </div>
    </div>
  </ElDialog>
</template>

<script setup>
import { getSettlementDocPageList } from '@/api/ctyOrderManagement/settlementDoc.js'
import { usePagination } from '@/utils/hooks.js'

const emit = defineEmits(['selectItem'])
const model = defineModel()
const tableData = ref([])

const { pagination, indexMethod } = usePagination()
const loading = ref(false)

function selectItem(row) {
  emit('selectItem', row)
  model.value = false
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getSettlementDocPageFun()
}

/**
      /**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  getSettlementDocPageFun()
}

function onOpen() {
  pagination.pageNum = 1 // 重置到第一页
  pagination.pageSize = 10
  getSettlementDocPageFun()
}

/**
 * 获取分页列表
 */
function getSettlementDocPageFun() {
  loading.value = true
  const searchData = {
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    params: {
      status: '3',
    },
  }

  getSettlementDocPageList(searchData)
    .then((res) => {
      console.log(res, '---- getSettlementDocPageList')
      tableData.value = res.data.list
      pagination.total = res.data.total
    })
    .catch((err) => {
      console.log(err, '---- getSettlementDocPageList')
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<style lang="scss" scoped></style>
