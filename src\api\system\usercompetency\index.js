import request from '@/utils/request'
//   表格内容
export function gettabledata(data) {
  return request({
    url: '/system/user/competency/list',
    method: 'get',
    params: data,
  })
}

// 职能修改
export function competencyupdate(data) {
  return request({
    url: '/system/user/competency',
    method: 'post',
    data,
  })
}

// 新增职能
export function competencyadd(data) {
  return request({
    url: '/system/user/competency',
    method: 'put',
    data,
  })
}

// 删除职能
export function competencyDel(bid) {
  return request({
    url: `/system/user/competency/${bid}`,
    method: 'delete',
  })
}

// 职能详情
export function competencydetaild(bid) {
  return request({
    url: `/system/user/competency/${bid}`,
    method: 'get',
  })
}

// 菜单下拉框列表
export function selecttree() {
  return request({
    url: '/system/menu/treeselect',
    method: 'get',
  })
}
const prefix = '/sso'
export function getRouters() {
  return request({
    url: `${prefix}/getRouters`,
    method: 'get',
  })
}
