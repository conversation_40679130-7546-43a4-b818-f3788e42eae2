import request from '@/utils/request'

// 商机库列表
export function list(query) {
  return request({
    url: '/purchase/psmContract/list',
    method: 'get',
    params: query,
  })
}

// 合同保存
export function save(query) {
  return request({
    url: '/purchase/psmContract/save',
    method: 'post',
    data: query,
  })
}

// 商机库列表
export function getBusinessList(query) {
  return request({
    url: '/project/business/library/list',
    method: 'post',
    data: query,
  })
}
// 我方主体名称

export function getEntityList(query) {
  return request({
    url: '/purchase/psmContract/odsCorporateEntity/list',
    method: 'get',
    params: query,
  })
}

export function getLabelTree(query) {
  return request({
    url: '/purchase/psmContract/label/tree',
    method: 'get',
    params: query,
  })
}
export function saveLabelTree(query) {
  return request({
    url: '/purchase/psmContract/label/save',
    method: 'post',
    data: query,
  })
}
export function delLabelTree(query) {
  return request({
    url: '/purchase/psmContract/label/delete',
    method: 'get',
    params: query,
  })
}

export function otherSubjectList(query) {
  return request({
    url: '/purchase/psmContract/otherSubject/list',
    method: 'get',
    params: query,
  })
}

export function getContractTypeList(query) {
  return request({
    url: '/purchase/odsContract/type/tree',
    method: 'get',
    params: query,
  })
}

export function getContractDetail(query) {
  return request({
    url: '/purchase/psmContract/detail',
    method: 'get',
    params: query,
  })
}
