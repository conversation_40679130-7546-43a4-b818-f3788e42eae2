<template>
  <div
    v-loading="loading"
    class="client_nature">
    <div class="client_nature_header">
      <p class="text-[#000]/[.45] text-[16px]">
        总合同额
      </p>
      <p class="text-[#000]/[.88] text-[28px]">
        {{ formatTotalAmount }}
        <span class="text-[16px]">
          万元
        </span>
      </p>
    </div>
    <div class="client_nature_chart">
      <VChart
        :option="option"
        autoresize />
    </div>
  </div>

  <ElDialog
    v-model="dialogVisible"
    width="1140px"
    title="客户企业性质">
    <div
      ref="search"
      class="mb-[24px]">
      <ElPopover
        placement="bottom-start"
        trigger="click"
        :width="searchWidth"
        :show-arrow="false"
        :teleported="false">
        <template #reference>
          <ElButton>
            <i
              class="iconfont icon-sift"
              :style="{
                marginRight: '8px',
              }" />
            所有筛选
            <ElIcon
              :style="{
                marginLeft: '10px',
              }">
              <ArrowDown />
            </ElIcon>
          </ElButton>
        </template>
        <ElForm
          ref="searchFormRef"
          :model="dialogFormData">
          <ElFormItem
            label="企业性质"
            prop="customerType">
            <ElCascader
              v-model="dialogFormData.customerType"
              :options="customerTypeOptions"
              :teleported="false" />
          </ElFormItem>
        </ElForm>
        <div>
          <ElButton
            type="primary"
            :loading="loading"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">
            重置
          </ElButton>
        </div>
      </ElPopover>
    </div>

    <ElTable
      v-loading="loading"
      :data="dialogTableData"
      :border="true"
      :show-summary="true"
      :summary-method="getSummaries">
      <ElTableColumn
        label="序号"
        width="80"
        type="index" />
      <ElTableColumn
        label="企业性质"
        prop="type" />
      <ElTableColumn
        label="合同金额（含税：万元）"
        prop="amount">
        <template #default="{ row }">
          {{ row.amount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="合同额占比"
        prop="contractRatio">
        <template #default="{ row }">
          {{ row.contractRatio }}%
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="合同额累计占比"
        prop="cumulativeContractRatio">
        <template #default="{ row }">
          {{ row.cumulativeContractRatio }}%
        </template>
      </ElTableColumn>
    </ElTable>
  </ElDialog>
</template>

<script setup>
import { getClientNatureData } from '@/api/dashboard/client.js'
import { customerTypeOptions } from '@/config/select-options.js'
import { useElementSize } from '@vueuse/core'

const { year, analysisSubject, isAdmin, deptId } = defineProps({
  year: Number,
  analysisSubject: String,
  isAdmin: Boolean,
  deptId: [String, Number],
})

const totalAmount = ref(0)
const formatTotalAmount = computed(() => {
  return totalAmount.value.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
})

const image = new URL('../../../assets/images/pie-logo.png', import.meta.url).href
const option = ref({
  tooltip: {
    trigger: 'item',
  },
  graphic: {
    elements: [{
      type: 'image',
      left: 'center',
      top: 80,
      style: {
        image,
        width: 120,
        height: 120,
      },
    }],
  },
  series: [
    {
      name: '',
      type: 'pie',
      left: 'center',
      top: 20,
      width: 240,
      height: 240,
      radius: [68, 120],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 5,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: true,
        position: 'outside',
      },
      labelLine: {
        show: true,
        length: 7,
        length2: 30,
      },
      data: [],
    },
  ],
})

const { width: searchWidth } = useElementSize(useTemplateRef('search'))
const searchFormRef = useTemplateRef('searchFormRef')

const dialogVisible = ref(false)
const dialogFormData = reactive({
  customerType: [],
})
const dialogTableData = ref([
  {
    customerType: '国有企业',
    amount: 1000,
    percentage: 10,
    cumulativePercentage: 10,
  },
  {
    customerType: '私营企业',
    amount: 1000,
    percentage: 10,
    cumulativePercentage: 20,
  },
  {
    customerType: '外资企业',
    amount: 1000,
    percentage: 10,
    cumulativePercentage: 30,
  },
  {
    customerType: '合资企业',
    amount: 1000,
    percentage: 10,
    cumulativePercentage: 40,
  },
])

function getSummaries(param) {
  const { columns, data } = param
  const sums = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计'
      return
    }
    if (index === 1 || index === 3 || index === 4) {
      sums[index] = ''
      return
    }
    if (index === 2) {
      sums[index] = data.reduce((prev, curr) => {
        return prev + curr[column.property]
      }, 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
    }
  })

  return sums
}

function handleSearch() {
  init(true)
}

function handleReset() {
  searchFormRef.value.resetFields()
}

const loading = ref(false)
function init(isSearch = false) {
  loading.value = true

  getClientNatureData({
    analysisYear: year,
    analysisEntityName: isAdmin ? analysisSubject?.[analysisSubject.length - 1] : '',
    analysisEntityId: isAdmin ? undefined : deptId,
    enterpriseNature: dialogFormData.customerType.join('/') || undefined,
  }).then((res) => {
    if (isSearch) {
      for (let i = 0; i < res.data.list.length; i++) {
        if (i === 0) {
          res.data.list[i].cumulativeContractRatio = res.data.list[i].contractRatio
        } else {
          res.data.list[i].cumulativeContractRatio = Number(
            (
              res.data.list[i].contractRatio + res.data.list[i - 1].cumulativeContractRatio
            ).toFixed(2),
          )
        }
      }
      dialogTableData.value = res.data.list
    } else {
      totalAmount.value = res.data.totalAmount
      option.value.series[0].data = res.data.list.map(item => ({
        value: item.amount,
        name: item.type,
      }))
    }
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  init(false)
})

watch(() => [year, analysisSubject, deptId], () => {
  init(false)
})

defineExpose({
  showDialog: () => {
    dialogVisible.value = true
    init(true)
  },
})
</script>

<style lang="scss" scoped>
.client_nature {
  &_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 56px;
    padding: 0 18px;
    border-radius: 8px;
    background-color: rgb(0 0 0 / 2%);
  }

  &_chart {
    width: 100%;
    height: 308px;
    margin-top: 12px;
  }
}
</style>
