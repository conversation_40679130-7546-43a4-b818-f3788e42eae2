<template>
  <ElDescriptions
    border
    :column="4"
    label-width="120">
    <ElDescriptionsItem
      :span="2"
      label="项目名称">
      {{ record.projectName }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      :span="2"
      label="项目编号">
      {{ record.projectCode }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目金额">
      {{ record.projectAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目状态">
      {{ projectStatus[record.projectStatus] }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="客户名称"
      :span="2">
      {{ record.clientName }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="项目内容"
      :span="4">
      {{ record.projectContent }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      :span="2"
      label="支撑组别">
      <ElTag
        v-if="!!record.supportGroup"
        :type="getSupportGroup(record.supportGroup)">
        {{ record.supportGroup }}
      </ElTag>
    </ElDescriptionsItem>
    <ElDescriptionsItem
      :span="2"
      label="工作内容">
      <ElTag
        v-if="!!record.workContent"
        :type="getWorkContent(record.workContent)">
        {{ record.workContent }}
      </ElTag>
    </ElDescriptionsItem>
    <ElDescriptionsItem
      v-if="
        record.workContent !== '材料编写' && record.workContent !== '生态工作'
      "
      :span="2"
      label="客户成员">
      {{ record.clientMember }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      v-if="
        record.workContent !== '材料编写' && record.workContent !== '生态工作'
      "
      :span="2"
      label="拜访地点">
      {{ record.visitPlace }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      v-if="
        record.workContent !== '材料编写' && record.workContent !== '生态工作'
      "
      :span="4"
      label="拜访事项">
      {{ record.visitItems }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      :span="4"
      label="具体工作内容">
      {{ record.specificWorkContent }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      :span="4"
      label="重大进展事项">
      {{ record.breakthroughItem }}
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import { projectStatus } from '@/views/man-hour/approval/config.js'
import {
  supportGroupOptions,
  workContentOptions,
} from '@/views/man-hour/report/config.js'

defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
})

function getSupportGroup(type) {
  return (
    supportGroupOptions.find(item => item.value === type)?.type || 'primary'
  )
}

function getWorkContent(type) {
  return (
    workContentOptions.find(item => item.value === type)?.type || 'primary'
  )
}
</script>

<style scoped lang="scss"></style>
