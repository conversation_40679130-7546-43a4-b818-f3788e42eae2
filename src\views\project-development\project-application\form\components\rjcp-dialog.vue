<template>
  <ElDialog
    v-model="model"
    @open="onOpen">
    <template #header>
      <Title is-hidden>
        软件产品/服务报价明细
      </Title>
    </template>
    <ElForm
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="140"
      label-position="left">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem
            prop="name"
            label="产品名称">
            <ElInput
              v-model="form.name"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="category"
            label="产品分类">
            <ElSelect
              v-model="form.category"
              placeholder="请选择">
              <ElOption
                v-for="(item, index) in self_project_category"
                :key="index"
                :value="item.value"
                :label="item.label" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="categoryName"
            label="类目">
            <ElInput
              v-model="form.categoryName"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="standardPrice"
            label="标准价格(元)">
            <ElInput
              v-model.number="form.standardPrice"
              placeholder="请输入"
              type="number" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="quantity"
            label="数量">
            <ElInput
              v-model.number="form.quantity"
              placeholder="请输入"
              type="number" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="discount"
            label="折扣">
            <ElInputNumber
              v-model="form.discount"
              placeholder="请输入"
              style="width: 100%;"
              class="custom_input_number"
              :step="0.01"
              :precision="2"
              controls-position="right"
              type="number"
              :min="0"
              :max="1">
              <template #decrease-icon>
                <ElIcon>
                  <Minus />
                </ElIcon>
              </template>
              <template #increase-icon>
                <ElIcon>
                  <Plus />
                </ElIcon>
              </template>
            </ElInputNumber>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="discountPrice"
            label="折扣价格(元)">
            <ElInput
              v-model="form.discountPrice"
              type="number"
              placeholder="请输入"
              disabled />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="historicalDealPrice"
            label="历史成交价格(元)">
            <ElInput
              v-model="form.historicalDealPrice"
              placeholder="请输入"
              disabled />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            prop="selectProductReason"
            label="选品理由">
            <ElInput
              v-model="form.selectProductReason"
              type="textarea"
              maxlength="20"
              placeholder="请输入"
              show-word-limit
              :rows="2" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton @click="onCancel">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="onConfirm">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import Title from '@/components/Title'
import { isEmpty } from '@/utils/hooks'
import regexp from '@/utils/regexp'

const { info } = defineProps({
  info: {
    type: [Object, null],
    default: null,
  },
})
const emit = defineEmits(['confirm', 'cancel'])

const { proxy } = getCurrentInstance()
const { self_project_category } = proxy.useDict('self_project_category')

const rules = reactive({
  name: [{ required: true, message: '请输入产品名称', trigger: ['blur', 'change'] }],
  categoryName: [{ required: true, message: '请输入类目', trigger: ['blur', 'change'] }],
  quantity: [
    { required: true, message: '请输入数量', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的数量', trigger: ['blur', 'change'] },
  ],
  standardPrice: [
    { required: true, message: '请输入标准价格', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  category: [
    { required: true, message: '请选择产品分类', trigger: 'change' },
  ],
  discountPrice: [
    { required: true, message: '请输入折扣价格', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的折扣价格', trigger: ['blur', 'change'] },
  ],
  discount: [
    { required: true, message: '请输入折扣', trigger: ['blur', 'change'] },
  ],
})
const formRef = useTemplateRef('formRef')
const form = ref({
  name: '',
  category: '',
  categoryName: '',
  standardPrice: '0.00',
  quantity: 0,
  discount: 0.00,
  discountPrice: 0,
  historicalDealPrice: '',
  selectProductReason: '',
})
const model = defineModel()
function onCancel() {
  formRef.value.resetFields()
  emit('cancel')
  model.value = false
}
function onConfirm() {
  formRef.value.validate().then(() => {
    emit('confirm', {
      formData: { ...form.value },
      dialogType: 'rjcp',
    })
    onCancel()
  })
}
function onOpen() {
  if (!isEmpty(info)) {
    form.value = { ...info }
  } else {
    delete form.value.uuid
    delete form.value.id
  }
}

watchEffect(() => {
  form.value.discountPrice = form.value.quantity * form.value.standardPrice * form.value.discount
})
</script>

<style lang="scss" scoped>
:deep(.custom_input_number) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
