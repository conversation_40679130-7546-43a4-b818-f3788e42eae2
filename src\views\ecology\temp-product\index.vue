<template>
  <Container>
    <div class="title">
      <p class="title-text">
        临时产品库
      </p>
      <ElButton
        :plain="true"
        @click="handleCreate">
        <template #icon>
          <ElIcon>
            <Plus />
          </ElIcon>
        </template>
        新增
      </ElButton>

      <ProductDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :row-data="dialogRow"
        :partner-options="partnerOptions"
        @confirm="handleConfirm" />
    </div>
    <div class="content">
      <div class="container-left">
        <ElTree
          ref="treeRef"
          :data="partnerOptions"
          node-key="value"
          :current-node-key="currentNodeKey"
          highlight-current
          @node-click="handleNodeClick">
          <template #default="{ data }">
            <span>{{ data.value }}</span>
          </template>
        </ElTree>
      </div>
      <div class="container-right">
        <ProductList
          ref="productListRef"
          :type="currentNodeKey"
          @reset-search="handleResetSearch"
          @edit-item="handleEdit" />
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getCompanyClass } from '@/api/ecology/company.js'
import Container from '@/components/Container/index.vue'
import ProductDialog from './components/product-dialog.vue'
import ProductList from './components/product-list.vue'

const partnerOptions = ref([])
async function getPartnerOptions() {
  try {
    const res = await getCompanyClass()
    partnerOptions.value = res.data
  } catch (error) {
    console.log(error)
  }
}
onMounted(() => {
  getPartnerOptions()
})

const currentNodeKey = ref('')
function handleNodeClick(item) {
  currentNodeKey.value = item.value
}

const treeRef = useTemplateRef('treeRef')
function handleResetSearch() {
  currentNodeKey.value = ''
  treeRef.value.setCurrentKey(null)
}

const dialogVisible = ref(false)
const dialogType = ref('')
const dialogRow = ref(null)

function handleCreate() {
  dialogVisible.value = true
  dialogType.value = 'add'
  dialogRow.value = null
}

function handleEdit(item) {
  dialogVisible.value = true
  dialogType.value = 'edit'
  dialogRow.value = item
}

const productListRef = useTemplateRef('productListRef')
function handleConfirm() {
  productListRef.value.getListData()
}
</script>

<style scoped lang="scss">
.title {
  display: flex;
  justify-content: space-between;
  padding: 18px 18px 12px;
  border-bottom: 1px solid rgb(0 0 0 / 6%);
  background-color: #fff;

  &-text {
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }
}

.content {
  display: flex;
  flex: 1;
  overflow: auto;
  border-radius: 10px;
  background: #fff;

  .container-left {
    flex: none;
    width: 216px;
    padding: 18px;
  }

  .container-right {
    flex: 1;
    padding: 18px;
    border-left: 1px solid rgb(0 0 0 / 6%);
  }
}
</style>
