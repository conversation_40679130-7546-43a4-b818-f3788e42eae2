<template>
  <div class="wrapper">
    <div class="left_chart">
      <VChart :option="option" />
    </div>
    <div class="right_content">
      <div class="title_box">
        <span style="color: #808080;">{{ props.option.totalTitle }}</span>
        <div style="margin-top: 10px;">
          <span class="total_value">{{ props.option.totalValue }}</span>
          <span style="font-weight: 700;"> 元</span>
        </div>
      </div>
      <div style="margin-top: 20px;">
        <div style="display: flex; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <div
              :style="{ backgroundColor: props.option.color }"
              class="text_icon" />
            <span style="margin-left: 5px;">{{ props.option.title }}</span>
          </div>
          <span>{{ props.option.value }}元</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  option: {
    totalTitle: String,
    title: String,
    totalValue: Number,
    value: Number,
    color: {
      type: String,
      required: true,
    },
  },
})
const option = {
  tooltip: {
    trigger: 'item',
    show: true,
    borderColor: 'transparent',
    // 相对位置，放置在容器正中间
    position: ['70%', '20%'],
    formatter(params) {
      // params 包含数据信息，如 value, color, percent
      const color = params.color //  获取数据的颜色
      const value = params.value // 获取数据的值（例如 46654.00）
      const percent = params.percent // 获取百分比（例如 43）

      return `
                <div style="border-radius:8px;display:flex;">
                  <p style="font-size: 14px; margin-right: 26px;">
                    <span style="display: inline-block; margin-right: 5px; border-radius: 10px; width: 10px; height: 10px; background-color: ${color};"></span>
                    ${props.option.title}
                  </p>
                  <div style="font-size: 14px; margin: 0; font-weight: bold; color: #000; display: flex; align-items: center;">
                    ${percent}%
                    <div style="width: 1px; height: 10px; background: #D8D8D8; border-radius: 8px; margin-left: 8px; margin-right: 8px;"></div>
                    ${value.toFixed(2)}元
                  </div>
                </div>
            `
    },
  },
  legend: {
    show: false,
    icon: 'circle',
    top: 'center',
    right: '0%',
    orient: 'vertical',
    itemWidth: 10,
    itemGap: 15,
    textStyle: {
      // 个
      color: '#3D3D3D',
      fontSize: 12,
    },
  },

  series: [
    {
      name: '',
      type: 'gauge',
      splitNumber: 10,
      radius: '80%',
      center: ['50%', '50%'],
      startAngle: 0,
      endAngle: 360,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#F0F0F0',
        },
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      detail: {
        show: false,
      },
    },
    {
      type: 'pie',
      clockwise: false,
      startAngle: 90,
      radius: ['65%', '85%'],
      data: [100],
      itemStyle: {
        color: '#eee',
      },
      animation: false,
    },
    {
      name: props.title,
      type: 'pie',
      radius: ['65%', '85%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 2,
        borderColor: '#fff',
        borderWidth: 0,
      },
      labelLine: {
        show: false,
      },
      percentPrecision: 0,
      data: [
        {
          name: props.option.totalTitle,
          value: props.option.value,
          label: {
            show: true,
            position: 'center',
            formatter: '{a_style|{d}%}',
            rich: {
              a_style: {
                color: '#000',
                fontSize: 24,
              },
            },
          },
          itemStyle: {
            // 颜色渐变
            color: props.option.color,
          },
        },
        // 第二项直接透明
        {
          value: props.option.totalValue - props.option.value,
          tooltip: {
            trigger: 'item',
            show: false,
          },
          label: {
            show: false,
          },
          itemStyle: {
            color: 'transparent',
            borderCap: 'round',
          },
        },
      ],
    },
  ],
}
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  margin-top: 10px;

  .left_chart {
    width: 200px;
    height: 200px;
  }

  .right_content {
    flex: 1;
    margin-left: 20px;

    .title_box {
      padding: 10px;
      border-radius: 8px;
      background-color: #f8f8f9;

      .total_value {
        margin-top: 10px;
        font-weight: 700;
        font-size: 20px;
      }
    }

    .text_icon {
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }
  }
}
</style>
