<template>
  <Container>
    <TableContainer title="纸质清单台账">
      <template #search="{ searchBoxWidth }">
        <FilterCriteria
          :popover-width="searchBoxWidth"
          @search="onSearch"
          @reset="onReset">
          <ElForm
            ref="formRef"
            label-position="left"
            label-width="90px"
            :model="filterFormData">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem label="项目编号">
                  <ElInput v-model="filterFormData.projectCode" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="项目名称">
                  <ElInput v-model="filterFormData.projectName" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem
                  label="项目归属部门："
                  prop="deliveryEntityId">
                  <ElTreeSelect
                    v-model="filterFormData.deliveryEntityId"
                    :multiple="true"
                    collapse-tags
                    :teleported="false"
                    :render-after-expand="false" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="验收年份">
                  <ElDatePicker
                    v-model="filterFormData.acceptanceDate"
                    :teleported="false"
                    type="year"
                    value-format="YYYY"
                    placeholder="请选择年份"
                    clearable />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </FilterCriteria>
      </template>

      <template #toolbar>
        <div>
          <ElButton @click="exportHandler">
            <template #icon>
              <i class="iconfont icon-UploadOutlined" />
            </template>
            下载
          </ElButton>
        </div>
      </template>

      <template #default="{ contentHeight }">
        <ElTabs
          v-model="activePhase"
          @tab-change="handlePhaseChange">
          <ElTabPane
            v-if="currentRoles.includes(1)"
            label="售前阶段"
            name="售前阶段" />
          <ElTabPane
            v-if="currentRoles.includes(2)"
            label="交付阶段"
            name="交付阶段" />
          <ElTabPane
            v-if="currentRoles.includes(3)"
            label="采购阶段"
            name="采购阶段" />
        </ElTabs>
        <ElTable
          :max-height="contentHeight"
          :data="tableData"
          border
          style="width: 100%">
          <ElTableColumn
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
            width="100" />
          <ElTableColumn
            prop="projectCode"
            label="项目编号" />
          <ElTableColumn
            prop="projectName"
            label="项目名称" />
          <ElTableColumn
            prop="deliveryEntity"
            label="项目归属部门" />
          <ElTableColumn
            prop="projectManager"
            label="项目经理" />
          <!-- <ElTableColumn
            prop="bolProjectName"
            label="文档一级分类" /> -->
          <ElTableColumn
            prop="acceptanceDate"
            label="验收年份" />
          <ElTableColumn
            prop="paperMissingCount"
            label="缺失纸质文档个数">
            <template #default="{ row }">
              <span style="color: #67C23A">{{ row.paperMissingCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="expectedSubmitDate"
            label="预计提交日期" />
          <ElTableColumn
            fixed="right"
            width="150"
            label="操作类型">
            <template #default="{ row }">
              <ElButton
                :disabled="row.expectedSubmitDate"
                type="primary"
                link
                @click="onCreate(row)">
                编辑
              </ElButton>
              <!-- <ElButton
                type="danger"
                link
                @click="onDelete(row)">
                删除
              </ElButton> -->
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <template #footer>
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @current-change="onSearch"
          @size-change="(size) => (onReset({ size, isClear: false }))" />
      </template>
    </TableContainer>
    <ElDialog
      v-model="dateDialogVisible"
      title="选择提交日期"
      width="400px">
      <ElForm
        :model="formData"
        :rules="rules">
        <ElFormItem
          label="预计提交日期"
          prop="expectedSubmitDate">
          <ElDatePicker
            v-model="formData.expectedSubmitDate"
            value-format="YYYYMMDD"
            type="date"
            placeholder="选择日期" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="dateDialogVisible = false">
          取消
        </ElButton>
        <ElButton
          type="primary"
          @click="handleDateSubmit">
          确定
        </ElButton>
      </template>
    </ElDialog>
  </Container>
</template>

<script setup>
import { getPaperDocsPageList, updateProjectDocs } from '@/api/system/docs.js'
import Container from '@/components/Container/index.vue'
import TableContainer from '@/components/Container/table-container.vue'
import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'
import useUserStore from '@/store/modules/user.js'
import { usePagination } from '@/utils/hooks.js'
import { onMounted } from 'vue'

const { proxy } = getCurrentInstance()
const currentRoles = ref([])
const currentRow = ref('')
const activePhase = ref('售前阶段')

const dateDialogVisible = ref(false)
const currentEditRow = ref(null)
const formData = reactive({
  expectedSubmitDate: '',
})
const rules = reactive({
  expectedRectifyDate: [
    { required: true, message: '请选择提交日期', trigger: 'change' },
  ],
})

const filterFormData = reactive({
  projectPhase: '售前阶段',
  projectCode: '',
  projectName: '',
  acceptanceDate: '',
})

const tableData = ref([])
const { pagination } = usePagination()

function handlePhaseChange(projectPhase) {
  filterFormData.projectPhase = projectPhase
  onSearch()
}

async function onSearch() {
  console.log('filterFormData', filterFormData)
  try {
    const res = await getPaperDocsPageList({
      page: { ...pagination },
      params: {
        ...filterFormData,
      },
    })
    const { total, list } = res.data
    tableData.value = list
    pagination.total = total
  } catch (error) {
    console.log(error)
  }
}

const formRef = useTemplateRef('formRef')
function onReset(data = {}) {
  const config = { isClear: true, size: 10, ...data }
  if (config.isClear) {
    formRef.value.resetFields()
  }
  filterFormData.projectCode = ''
  filterFormData.projectName = ''
  filterFormData.deliveryEntityId = null
  filterFormData.acceptanceDate = ''
  pagination.pageSize = config.size
  pagination.pageNum = 1
  onSearch()
}

// function onCreate(config = {}) {
//   router.push({
//     path: '/project-manage/project-end/apply/form',
//     query: {
//       ...config,
//     },
//   })
// }

async function handleDateSubmit() {
  try {
    await updateProjectDocs(
      currentRow.value.id,
      { expectedSubmitDate: formData.expectedSubmitDate },
    )
    ElMessage.success('提交成功')
    dialogVisible.value = false
    onSearch() // 刷新列表
  } catch (e) {
    console.error(e)
  }
}

function onCreate(row) {
  currentEditRow.value = row
  dateDialogVisible.value = true
}
// 导出
function exportHandler() {
  proxy.download(
    '/project/doc/paper/export',
    {
      ...filterFormData,
    },
    `纸质清单台账_${new Date().getTime()}.xlsx`,
  )
}

onMounted(async () => {
  onSearch() // 添加首次加载数据
  const { user, roles } = await useUserStore().getInfo()
  // 市场拓展部 207
  // 经营管理部 227
  // 综合办公室 203
  // 采购管理部 224
  // 企业服务事业部 240
  // 集成交付部 211
  if (roles.includes('admin') || user.deptId === 227 || user.deptId === 203) {
    currentRoles.value = [1, 2, 3]
  } else if (user.deptId === 224) {
    currentRoles.value = [1]
  } else if (user.deptId === 207) {
    currentRoles.value = [2]
  } else if (user.deptId === 240 || user.deptId === 211) {
    currentRoles.value = [3]
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-tabs) {
  margin-bottom: 16px;
  background: #fff;
  border-radius: 4px;
}
:deep(.el-tabs__nav-wrap:after) {
  background-color: #fff;
}
</style>
