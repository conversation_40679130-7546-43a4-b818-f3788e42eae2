<template>
  <DefaultContainer v-loading="loading">
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          结算单
        </div>
      </div>
      <div
        v-if="!props.id"
        class="right">
        <ElButton @click="router.back()">
          取消
        </ElButton>
        <ElButton
          :loading="saveLoading"
          plain
          type="primary"
          @click="onSubmit(false)">
          暂存
        </ElButton>
        <ElButton
          :loading="saveLoading"
          type="primary"
          @click="onSubmit(true)">
          提交
        </ElButton>
      </div>
    </div>
    <div class="content">
      <ElForm
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="170px"
        :disabled="props.id !== undefined">
        <ElDivider />
        <div class="sub-title">
          楚天云结算单
        </div>
        <ElFormItem
          class="form-item"
          label="结算年度"
          prop="year">
          <ElDatePicker
            v-model="form.year"
            value-format="YYYY"
            type="year"
            placeholder="请选择结算年度" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单部门"
          prop="deptId">
          <ElSelect
            v-model="form.deptName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef2.show())
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="结算单编号"
          prop="formNo">
          <ElInput
            v-model="form.formNo"
            disabled
            placeholder="自动生成" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="结算单名称"
          prop="statementName">
          <ElInput
            v-model="form.statementName"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="结算发起单位"
          prop="initiateUnit">
          <ElInput
            v-model="form.initiateUnit"
            disabled
            placeholder="请输入" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="应用系统名称"
          prop="ctAppId"
          style="width: 95%">
          <ElSelect
            v-model="form.ctAppName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectApplicationModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="客户名称"
          prop="customerCode">
          <ElInput
            v-model="form.customerName"
            disabled
            placeholder="选择应用系统后带入" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="结算依据单价合同名称"
          prop="protocolName"
          style="width: 95%">
          <ElSelect
            v-model="form.protocolName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectFrameworkAgreementModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="结算依据单价合同编号"
          prop="protocolCode">
          <ElInput
            v-model="form.protocolCode"
            disabled
            placeholder="选择单价合同后自动填入" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="项目名称"
          style="width: 95%"
          prop="projectName">
          <ElSelect
            v-model="form.projectName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectProjectModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="项目编号"
          prop="projectCode">
          <ElInput
            v-model="form.projectCode"
            disabled
            placeholder="选择项目后自动填入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="数产项目经理"
          prop="projectManager">
          <ElSelect
            v-model="form.projectManagerName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef4.show())
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="市场经理"
          prop="marketManager">
          <ElSelect
            v-model="form.marketManagerName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef5.show())
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="结算开始计费日期"
          prop="chargeDateStart">
          <ElDatePicker
            v-model="form.chargeDateStart"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledStartDate"
            placeholder="请选择" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="结算结束计费日期"
          prop="chargeDateEnd">
          <ElDatePicker
            v-model="form.chargeDateEnd"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledEndDate"
            placeholder="请选择" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="结算周期(天)"
          prop="statementCycle">
          <ElInput
            v-model="form.statementCycle"
            disabled
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="结算总金额(元)"
          prop="totalAmount">
          <ElInputNumber
            v-model="form.totalAmount"
            :precision="2"
            controls-position="right"
            style="width: 100%"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="经办人"
          prop="handledBy">
          <ElSelect
            v-model="form.handledByName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef3.show())
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="申请日期"
          prop="applicationTime">
          <ElDatePicker
            v-model="form.applicationTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="备注"
          prop="remark"
          style="width: 95%">
          <ElInput
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注" />
        </ElFormItem>

        <div
          class="attachment_wrapper"
          style="margin-bottom: 10px">
          <ElUpload
            ref="uploadRef"
            v-model:file-list="fileList"
            class="custom_upload"
            :action="uploadUrl"
            auto-upload
            :on-success="onFileUploadSuccess"
            :on-error="onFileUploadFail"
            multiple
            :on-preview="handlePreview"
            style="padding: 0 20px">
            <template #trigger>
              <ElButton
                link
                icon="link">
                <span style="color: red">* </span>结算单明细附件
              </ElButton>
            </template>
            <ElButton
              type="primary"
              style="float: right"
              @click="selectFile()">
              上传
            </ElButton>
          </ElUpload>
        </div>
      </ElForm>
      <div v-if="isProjectManagerChecks.isShow || isMarketManagerChecks.isShow || isCloudServiceChecks.isShow">
        <div
          class="sub-title"
          style="margin-bottom: 0; color: #333; font-weight: bold;">
          数产集团人员核对
        </div>
        <p style=" margin: 8px 0;color: #666; font-size: 14px;">
          数产人核对说明
        </p>
        <p style=" margin: 8px 0;color: #666; font-size: 14px; line-height: 1.6;">
          1、项目经理核对添加交付项目信息，填写合同中的云服务履约周期、预算有效周期以及本次拟结算金额;选择对应市场经理人员
        </p>
        <p style=" margin: 8px 0;color: #666; font-size: 14px; line-height: 1.6;">
          2、市场经理填写合同中云服务的金额以及对应云服务部分回款金额
        </p>
        <p style=" margin: 8px 0;color: #666; font-size: 14px; line-height: 1.6;">
          3、经营管理部核实最终拟同意付款金额
        </p>
        <ElDivider />
      </div>
      <div
        v-if="isProjectManagerChecks.isShow">
        <ElForm
          ref="formRef2"
          :inline="true"
          :model="form"
          :rules="rules2"
          label-position="left"
          :disabled="!isProjectManagerChecks.isEdit"
          label-width="200px">
          <ElDivider />
          <div
            class="sub-title">
            项目经理核对
          </div>
          <ElFormItem
            class="form-item"
            label="数产市场经理"
            prop="marketManager">
            <ElSelect
              v-model="form.marketManagerName"
              placeholder="请选择"
              @visible-change="
                (change) => change && (orgPickerRef5.show())
              " />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="关联结算单编号"
            prop="formNo">
            <ElInput
              v-model="form.formNo"
              disabled
              placeholder="" />
          </ElFormItem>
          <br>
          <ElFormItem
            class="form-item"
            label="项目名称"
            prop="projectName">
            <ElSelect
              v-model="form.projectName"
              placeholder="请选择"
              @visible-change="
                (change) => change && (modelIsShow.selectProjectModal = true)
              " />
          </ElFormItem>
          <br>
          <ElFormItem
            class="form-item"
            label="项目编号"
            prop="projectCode">
            <ElInput
              v-model="form.projectCode"
              disabled
              placeholder="选择项目后自动填入" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="销售合同名称"
            prop="saleContractName">
            <ElSelect
              v-model="form.saleContractName"
              placeholder="请选择"
              :disabled="!form.projectCode"
              @visible-change="
                (change) => change && (modelIsShow.contractModal = true)
              " />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="销售合同编号"
            prop="saleContractNumber">
            <ElInput
              v-model="form.saleContractNumber"
              disabled
              placeholder="选择销售合同后带入" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="项目云预算"
            prop="budgetAmount">
            <ElInput
              v-model="form.budgetAmount"
              disabled
              placeholder="选择项目后带入" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="合同中的云服务开始日期"
            prop="contractServiceStartTime">
            <ElDatePicker
              v-model="form.contractServiceStartTime"
              :disabled-date="disabledContractServiceStartTime"
              type="date"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="合同中的云服务结束日期"
            prop="contractServiceEndTime">
            <ElDatePicker
              v-model="form.contractServiceEndTime"
              :disabled-date="disabledContractServiceEndTime"
              type="date"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="合同中的云服务履约周期(天)"
            prop="contractServicePerformanceCycle">
            <ElInput
              v-model="form.contractServicePerformanceCycle"
              disabled
              placeholder="自动计算" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="预算有效周期(天)"
            prop="budgetEffectivePeriod">
            <ElInputNumber
              v-model="form.budgetEffectivePeriod"
              controls-position="right"
              :precision="2"
              :min="0"
              style="width: 100%"
              placeholder="请输入" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="本次拟结算金额(元)"
            prop="proposedSettlementAmount">
            <ElInputNumber
              v-model="form.proposedSettlementAmount"
              controls-position="right"
              :precision="2"
              :min="0"
              style="width: 100%"
              placeholder="请输入" />
          </ElFormItem>
        </ElForm>
      </div>
      <div
        v-if="isMarketManagerChecks.isShow">
        <ElForm
          ref="formRef3"
          :inline="true"
          :model="form"
          :rules="rules3"
          label-position="left"
          :disabled="!isMarketManagerChecks.isEdit"
          label-width="200px">
          <ElDivider />
          <div
            class="sub-title">
            市场经理核对
          </div>
          <ElFormItem
            class="form-item"
            label="云回款金额(元)"
            prop="collectionAmount">
            <ElInputNumber
              v-model="form.collectionAmount"
              controls-position="right"
              :precision="2"
              :min="0"
              style="width: 100%"
              placeholder="请输入云回款金额" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="合同中的云服务金额(元)"
            prop="contractServiceAmount">
            <ElInputNumber
              v-model="form.contractServiceAmount"
              controls-position="right"
              :precision="2"
              style="width: 100%"
              :min="0"
              placeholder="请输入合同中的云服务金额" />
          </ElFormItem>
        </ElForm>
      </div>
      <div
        v-if="isCloudServiceChecks.isShow">
        <ElForm
          ref="formRef4"
          :inline="true"
          :model="form"
          :rules="rules4"
          label-position="left"
          :disabled="!isCloudServiceChecks.isEdit"
          label-width="200px">
          <ElDivider />
          <div
            class="sub-title">
            楚天云服务处理信息
          </div>
          <ElFormItem
            class="form-item"
            label="拟同意付款金额(元)"
            prop="proposedPaymentAmount">
            <ElInputNumber
              v-model="form.proposedPaymentAmount"
              controls-position="right"
              :precision="2"
              style="width: 100%"
              :min="0"
              placeholder="请输入拟同意付款金额" />
          </ElFormItem>
        </ElForm>
      </div>
    </div>
    <SelectProjectModal
      v-model="modelIsShow.selectProjectModal"
      @select-item="onChangeProject" />
    <ApplicationSelectModal
      v-model="modelIsShow.selectApplicationModal"
      @select-item="onChangeApplication" />
    <FrameworkAgreementSelectModal
      v-model="modelIsShow.selectFrameworkAgreementModal"
      @select-item="onChangeFrameworkAgreement" />
    <OrgPicker
      ref="orgPickerRef2"
      type="dept"
      :multiple="false"
      title="选择订单部门"
      @ok="handleSelectedDepartment" />
    <OrgPicker
      ref="orgPickerRef3"
      type="user"
      :multiple="false"
      title="选择报价人"
      @ok="handleSelectedBidder" />
    <OrgPicker
      ref="orgPickerRef4"
      type="user"
      :multiple="false"
      title="选择数产项目经理"
      @ok="handleSelectedProjectManager" />
    <OrgPicker
      ref="orgPickerRef5"
      type="user"
      :multiple="false"
      title="选择数产市场经理"
      @ok="handleSelectedMarketManager" />
    <SelectContractModal
      v-model="modelIsShow.contractModal"

      :project-code="searchProjectCode"
      @select-item="onChangeContract" />
  </DefaultContainer>
</template>

<script setup>
import {
  createOrUpdateSettlementDoc,
  getSettlementDocDetail,
  submitSettlementDoc,
  submitSettlementDocWithApprove,
} from '@/api/ctyOrderManagement/settlementDoc.js'

import { getCurrentProcessId } from '@/api/wflow-pro'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import useUserStore from '@/store/modules/user.js'
import { deepClone } from '@/utils'
import ApplicationSelectModal from '@/views/ctyOrderManagement/modules/ApplicationSelectModal.vue'
import FrameworkAgreementSelectModal from '@/views/ctyOrderManagement/modules/FrameworkAgreementSelectModal.vue'
import SelectContractModal from '@/views/financialManagement/modules/SelectContractModal.vue'
import SelectProjectModal from '@/views/financialManagement/modules/SelectProjectModal.vue'
import { useCloudBudget } from '@/views/socsManage/utils/hook.js'
import { Back } from '@element-plus/icons-vue'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { dayjs, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
  instanceId: {
    type: String,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  forms: {
    type: Object,
    default: () => ({}),
  },
})

const router = useRouter()
const formRef = ref(null)
const form = ref({
  initiateUnit: '省楚天云',
})

const modelIsShow = reactive({
  selectProjectModal: false,
  contractModal: false,
  selectApplicationModal: false,
  selectContractModal: false,
  selectFrameworkAgreementModal: false,
})

const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址
const fileList = ref([])

const searchProjectCode = computed(() => {
  if (form.value.projectCode) {
    return form.value.projectCode.split('-D')[0]
  }
  return ''
})

// 监听服务计划开始和结束时间的变化
watch(
  () => [form.value.chargeDateStart, form.value.chargeDateEnd],
  ([start, end]) => {
    if (start && end) {
      const cycle = dayjs(end).diff(dayjs(start), 'day')
      form.value.statementCycle = cycle || 0
    } else {
      form.value.statementCycle = 0
    }
  },
)

watch(
  () => [form.value.contractServiceStartTime, form.value.contractServiceEndTime],
  ([start, end]) => {
    if (start && end) {
      const cycle = dayjs(end).diff(dayjs(start), 'day')
      form.value.contractServicePerformanceCycle = cycle || 0
    } else {
      form.value.contractServicePerformanceCycle = 0
    }
  },
)

const rules = ref({
  year: [
    { required: true, message: '请选择结算年度', trigger: 'change' },
  ],
  deptId: [
    { required: true, message: '请选择订单部门', trigger: 'change' },
  ],
  handledBy: [
    { required: true, message: '请选择经办人', trigger: 'change' },
  ],
  statementName: [
    { required: true, message: '请输入结算单名称', trigger: 'change' },
  ],
  ctAppId: [
    { required: true, message: '请选择应用系统名称', trigger: 'change' },
  ],
  customerCode: [
    { required: true, message: '请选择客户名称', trigger: 'change' },
  ],
  protocolName: [
    { required: true, message: '请选择协议名称', trigger: 'change' },
  ],
  protocolCode: [
    { required: true, message: '请输入协议编号', trigger: 'change' },
  ],
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' },
  ],
  projectManager: [
    { required: true, message: '请选择数产项目经理', trigger: 'change' },
  ],
  marketManager: [
    { required: true, message: '请选择市场经理', trigger: 'change' },
  ],
  chargeDateStart: [
    { required: true, message: '请选择结算开始日期', trigger: 'change' },
  ],
  chargeDateEnd: [
    { required: true, message: '请选择结算结束日期', trigger: 'change' },
  ],
  totalAmount: [
    { required: true, message: '请输入结算总金额', trigger: 'change' },
  ],
})

const rules2 = ref({
  marketManager: [
    { required: true, message: '请选择数产市场经理', trigger: 'change' },
  ],
  formNo: [
    { required: true, message: '请输入关联结算单编号', trigger: 'change' },
  ],
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' },
  ],
  projectCode: [
    { required: true, message: '请输入项目编号', trigger: 'change' },
  ],
  saleContractName: [
    { required: true, message: '请选择销售合同名称', trigger: 'change' },
  ],
  saleContractNumber: [
    { required: true, message: '请输入销售合同编号', trigger: 'change' },
  ],
  budgetAmount: [
    { required: true, message: '请输入项目云预算', trigger: 'change' },
  ],
  contractServiceStartTime: [
    { required: true, message: '请选择合同中的云服务开始日期', trigger: 'change' },
  ],
  contractServiceEndTime: [
    { required: true, message: '请选择合同中的云服务结束日期', trigger: 'change' },
  ],
  contractServicePerformanceCycle: [
    { required: true, message: '请输入合同中的云服务履约周期', trigger: 'change' },
  ],
  budgetEffectivePeriod: [
    { required: true, message: '请输入预算有效周期', trigger: 'change' },
  ],
  proposedSettlementAmount: [
    { required: true, message: '请输入本次拟结算金额', trigger: 'change' },
  ],
})

const rules3 = ref({
  collectionAmount: [
    { required: true, message: '请输入云回款金额', trigger: 'change' },
  ],
  contractServiceAmount: [
    { required: true, message: '请输入合同中的云服务金额', trigger: 'change' },
  ],
})

const rules4 = ref({
  proposedPaymentAmount: [
    { required: true, message: '请输入拟同意付款金额', trigger: 'change' },
  ],
})

const id = ref('')
const loading = ref(false)
const saveLoading = ref(false)

onMounted(() => {
  if (props.id || router.currentRoute.value.query.id) {
    id.value = props.id || router.currentRoute.value.query.id
    getFormData()
    console.log(props.forms, '--- props.forms')
    console.log(props.config, '--- props.config')
  } else {
    form.value.handledByName = useUserStore().userInfo.nickName
    form.value.handledBy = useUserStore().userInfo.userId
    form.value.applicationTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
  }
})

const isProjectManagerChecks = ref({
  isShow: false,
  isEdit: false,
})
const isMarketManagerChecks = ref({
  isShow: false,
  isEdit: false,
})
const isCloudServiceChecks = ref({
  isShow: false,
  isEdit: false,
})

async function getFormData() {
  loading.value = true
  try {
    const res = await getSettlementDocDetail({
      id: id.value,
    })
    console.log(res)
    form.value = deepClone(res.data)
    if (form.value.attachment) {
      try {
        let parsedArr = JSON.parse(form.value.attachment)
        parsedArr = parsedArr.map(item => ({
          name: item.match(/[^/\\?#]+$/)[0],
          url: item,
        }))
        fileList.value = parsedArr
      } catch (error) {
        console.log(error)
      }
    }
    if (props?.forms[0]?.propName === 'projectManager' && props?.forms[0]?.perm === 'E') {
      isProjectManagerChecks.value.isShow = true
      isProjectManagerChecks.value.isEdit = true
    }
    if (props?.forms[1]?.propName === 'marketManager' && props?.forms[1]?.perm === 'E') {
      isProjectManagerChecks.value.isShow = true
      isMarketManagerChecks.value.isShow = true
      isMarketManagerChecks.value.isEdit = true
    }
    if (props?.forms[2]?.propName === 'JYGLB' && props?.forms[2]?.perm === 'E') {
      isProjectManagerChecks.value.isShow = true
      isMarketManagerChecks.value.isShow = true
      isCloudServiceChecks.value.isShow = true
      isCloudServiceChecks.value.isEdit = true
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
const { getCtyBudgetAmount } = useCloudBudget()
function onChangeProject(row) {
  console.log(row, '--- row')
  form.value.projectName = row.projectName || row.bolProjectName
  form.value.projectCode = row.projectCode
  form.value.projectManagerName = row.projectManager
  form.value.projectManager = row.projectManagerId
  // 分管领导
  form.value.projectLeaderName = row.projectLeader
  form.value.projectLeader = row.projectLeaderId
  // 市场经理
  form.value.marketManagerName = row.marketingManager
  form.value.marketManager = row.marketingManagerId
  // 部门
  form.value.projectDeptName = row.applicantDept
  form.value.projectDeptId = row.applicantDeptId
  form.value.budgetAmount = getCtyBudgetAmount(row, '1')
}

function onChangeApplication(row) {
  console.log(row, '--- row')
  form.value.ctAppName = row.appName
  form.value.ctAppId = row.id
  form.value.customerName = row.customerName
  form.value.customerCode = row.customerCode
}

function onChangeFrameworkAgreement(row) {
  console.log(row, '--- row')

  form.value.protocolName = row.protocolName
  form.value.protocolCode = row.protocolCode
}

function onChangeContract(row) {
  console.log(row, '--- row')
  // 合同
  if (row) {
    form.value.saleContractName = row.name
    form.value.saleContractNumber = row.code
  } else {
    form.value.saleContractName = ''
    form.value.saleContractNumber = ''
  }
}

function disabledStartDate(time) {
  if (form.value.chargeDateEnd) {
    const endTime = new Date(form.value.chargeDateEnd).getTime()
    const currentTime = time.getTime()
    // 不能选择开始日期当天及之前的日期
    return currentTime >= endTime
  }
  return false
}

function disabledEndDate(time) {
  if (form.value.chargeDateStart) {
    const startTime = new Date(form.value.chargeDateStart).getTime()
    const currentTime = time.getTime()
    // 不能选择开始日期当天及之前的日期
    return currentTime <= startTime
  }
  return false
}

function disabledContractServiceStartTime(time) {
  if (form.value.contractServiceEndTime) {
    const endTime = new Date(form.value.contractServiceEndTime).getTime()
    const currentTime = time.getTime()
    return currentTime >= endTime
  }
  return false
}

function disabledContractServiceEndTime(time) {
  if (form.value.contractServiceStartTime) {
    const startTime = new Date(form.value.contractServiceStartTime).getTime()
    const currentTime = time.getTime()
    return currentTime <= startTime
  }
  return false
}

const uploadRef = useTemplateRef('uploadRef')
function selectFile() {
  uploadRef.value.$el.querySelector('input').click()
}

function handlePreview(file) {
  if (file?.url) {
    window.open(file.url)
  }
  if (file?.response?.data) {
    window.open(file.response.data)
  }
}

function onFileUploadSuccess() {
  ElMessage.success('附件上传成功')
}

function onFileUploadFail() {
  ElMessage.error('附件上传失败')
}

async function onSubmit(isSubmit) {
  let valid = true
  if (isSubmit) {
    if (fileList.value.length === 0) {
      ElMessage.error('请上传续费订单附件')
      return
    }
    valid = await formRef.value.validate()
  }
  if (valid) {
    const data = getData()

    saveLoading.value = true
    try {
      if (isSubmit) {
        data.processDefId = await getCurrentProcessId('cty-settlement-doc')
        console.log(data.processDefId, '--- data.processDefId')
        //  提交
        const res = await submitSettlementDoc(data)
        if (res.code === 200) {
          ElMessage.success('提交成功')
          router.back()
        } else {
          ElMessage.error('提交失败')
        }
      } else {
        // 暂存
        const res = await createOrUpdateSettlementDoc(data)
        if (res.code === 200) {
          ElMessage.success('暂存成功')
          router.back()
        } else {
          ElMessage.error('暂存失败')
        }
      }
    } finally {
      saveLoading.value = false
    }
  }
}

function getData() {
  const data = deepClone(form.value)
  if (fileList.value.length > 0) {
    const addressArr = fileList.value
      .map(item => item.url || item.response.data)
      .filter(address => address)
    if (addressArr.length > 0) {
      data.attachment = JSON.stringify(addressArr)
    } else {
      data.attachment = ''
    }
  } else {
    data.attachment = ''
  }
  console.log(data, '--- data')
  return data
}

const orgPickerRef2 = ref()
function handleSelectedDepartment(rows) {
  if (rows.length > 0) {
    form.value.deptName = rows[0].name
    form.value.deptId = rows[0].id
  } else {
    form.value.deptName = ''
    form.value.deptId = ''
  }
}

const orgPickerRef3 = ref()
function handleSelectedBidder(val) {
  if (val && val.length > 0) {
    form.value.handledByName = val[0].name
    form.value.handledBy = val[0].id
  } else {
    form.value.handledByName = ''
    form.value.handledBy = ''
  }
}

const orgPickerRef4 = ref()
function handleSelectedProjectManager(val) {
  if (val && val.length > 0) {
    form.value.projectManagerName = val[0].name
    form.value.projectManager = val[0].id
  } else {
    form.value.projectManagerName = ''
    form.value.projectManager = ''
  }
}

const orgPickerRef5 = ref()
function handleSelectedMarketManager(val) {
  if (val && val.length > 0) {
    form.value.marketManagerName = val[0].name
    form.value.marketManager = val[0].id
  } else {
    form.value.marketManagerName = ''
    form.value.marketManager = ''
  }
}

// 项目经理核对
const formRef2 = ref(null)
// 市场经理核对
const formRef3 = ref(null)
// 楚天云服务处理
const formRef4 = ref(null)

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(form))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      const data = getData()
      let valid = false
      if (isProjectManagerChecks.value.isEdit) {
        valid = await formRef2.value.validate()
      }
      if (isMarketManagerChecks.value.isEdit) {
        valid = await formRef3.value.validate()
      }
      if (isCloudServiceChecks.value.isEdit) {
        valid = await formRef4.value.validate()
      }
      if (!valid) {
        return
      }
      data.processDefId = props.instanceId
      const res = await submitSettlementDocWithApprove(data)
      if (res.code === 200) {
        ElMessage.success('提交成功')
        return Promise.resolve()
      } else {
        ElMessage.warning('提交失败')
        return Promise.reject(new Error('提交失败'))
      }
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
  .header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important;

  /* 设置最小高度 */

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .plan-item {
    width: 100%;

    .form-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 10px;
    margin-bottom: 40px;
    border: 1px dashed #1677ff;
    border-radius: 6px;
    color: #1677ff;
    font-size: 14px;
    cursor: pointer;
  }

  .form-item {
    width: 360px;
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 12px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}
</style>

  <style lang="scss">
  .custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
