import request from '@/utils/request'

/**
 * 商机库列表
 */
export function getBusinessLibraryList(data) {
  return request({
    url: '/project/business/library/list',
    method: 'post',
    data,
  })
}

/**
 * 权限共享
 */
export function shareOperation(data) {
  return request({
    url: '/project/business/library/share',
    method: 'post',
    data,
  })
}

/**
 * 商机变更
 */
export function changeBusiness(data) {
  return request({
    url: '/project/business/library/charge',
    method: 'post',
    data,
  })
}

/**
 * 商机转移
 */
export function transferBusiness(data) {
  return request({
    url: '/project/business/library/transfer',
    method: 'post',
    data,
  })
}

/**
 * 商机删除
 */
export function removeBusiness(id) {
  return request({
    url: `/project/business/library/del/${id}`,
    method: 'post',
  })
}

/**
 * 商机重启
 */
export function restartBusiness(id) {
  return request({
    url: `/project/business/library/restart/${id}`,
    method: 'post',
  })
}

/**
 * 获取商机库基本信息
 */
export function getBusinessBaseInfo(id) {
  return request({
    url: `/project/business/library/${id}`,
    method: 'get',
  })
}

/**
 * 主要相关方修改
 */
export function updateBusinessRelate(data) {
  return request({
    url: `/project/business/library/relate/update`,
    method: 'post',
    data,
  })
}

/**
 * 查询售前计划
 */
export function getBusinessPlan(id) {
  return request({
    url: `/project/business/library/get/plan/${id}`,
    method: 'get',
  })
}

/**
 * 修改售前计划
 */
export function updateBusinessPlan(data) {
  return request({
    url: `/project/business/library/update/plan/`,
    method: 'post',
    data,
  })
}

/**
 * 查询投标列表
 */
export function getBidList(params) {
  return request({
    url: `/project/business/library/get/bid`,
    method: 'get',
    params,
  })
}

/**
 * 查询合同列表
 */
export function getContractList(params) {
  return request({
    url: `/purchase/psmContract/list`,
    method: 'get',
    params,
  })
}

/**
 * 查询关联流程
 */
export function getRelateProcess(params) {
  return request({
    url: `/project/business/library/relate/process`,
    method: 'get',
    params,
  })
}

/**
 * 查询变更记录
 */
export function getChangeRecord(params) {
  return request({
    url: `/project/business/library/charge`,
    method: 'get',
    params,
  })
}

/**
 * 查询转移记录
 */
export function getTransferRecord(params) {
  return request({
    url: `/project/business/library/transfer`,
    method: 'get',
    params,
  })
}

/**
 * 查询跟进记录
 */
export function getFollowRecord(params) {
  return request({
    url: `/project/business/library/followRecord`,
    method: 'post',
    params,
  })
}
