<template>
  <ProjectCenter>
    <div style="padding: 20px">
      <div class="title">
        <span class="title-text">楚天云</span>
        <div class="desc">
          <div class="desc-item">
            <div class="label">
              项目名称：
            </div>
            <div class="value">
              {{ route.query.name }}
            </div>
          </div>
          <ElDivider direction="vertical" />
          <div class="desc-item">
            <div class="label">
              项目编号：
            </div>
            <div class="value">
              {{ route.query.pcode }}
            </div>
          </div>
        </div>
      </div>
      <div class="sub-title">
        楚天云订单申请
      </div>
      <ElTable
        v-loading="orderApplicationLoading"
        :data="orderApplicationList"
        border>
        <ElTableColumn
          fixed="left"
          type="index"
          label="序号"
          width="60" />
        <ElTableColumn
          prop="deptName"
          label="订单部门"
          min-width="120px" />
        <ElTableColumn
          prop="orderNumber"
          label="订单编号"
          min-width="120px" />
        <ElTableColumn
          prop="orderName"
          label="订单名称"
          min-width="130px" />
        <ElTableColumn
          prop="orderNature"
          label="订单性质"
          min-width="130px" />
        <ElTableColumn
          prop="ctAppName"
          label="应用系统名称"
          min-width="120px" />
        <ElTableColumn
          prop="customerName"
          label="客户名称"
          min-width="120px" />
        <ElTableColumn
          prop="serviceStartTime"
          label="云服务实际开通日期"
          min-width="130px" />
        <ElTableColumn
          prop="servicePerformanceCycle"
          label="履约周期(天)"
          min-width="130px" />
        <ElTableColumn
          prop="orderAmount"
          label="订单总金额(元)"
          min-width="130px" />
      </ElTable>
      <div class="sub-title">
        楚天云结算单
      </div>
      <ElTable
        v-loading="settlementDocLoading"
        :data="settlementDocList"
        border>
        <ElTableColumn
          fixed="left"
          type="index"
          label="序号"
          width="60" />
        <ElTableColumn
          prop="year"
          label="年度"
          min-width="120px" />
        <ElTableColumn
          prop="formNo"
          label="结算单编号"
          min-width="120px" />
        <ElTableColumn
          prop="statementName"
          label="结算单名称"
          min-width="130px" />
        <ElTableColumn
          prop="chargeDateStart"
          label="合同中的云服务开始日期"
          min-width="130px" />
        <ElTableColumn
          prop="chargeDateEnd"
          label="合同中的云服务结束日期"
          min-width="120px" />
        <ElTableColumn
          prop="statementCycle"
          label="合同中的云服务履约周期(天)"
          min-width="120px" />
        <ElTableColumn
          prop="statementCycle"
          label="预算有效周期(天)"
          min-width="130px" />
        <ElTableColumn
          prop="totalAmount"
          label="本次拟结算金额(元)"
          min-width="130px" />
        <ElTableColumn
          prop="totalAmount"
          label="云回款金额(元)"
          min-width="130px" />
        <ElTableColumn
          prop="totalAmount"
          label="合同中的云服务金额(元)"
          min-width="130px" />
        <ElTableColumn
          prop="totalAmount"
          label="拟同意付款金额(元)"
          min-width="130px" />
      </ElTable>
    </div>
  </ProjectCenter>
</template>

<script setup>
import { getOrderPaperLedgerPageList } from '@/api/ctyOrderManagement/orderPaperLedger.js'
import { getSettlementDocPageList } from '@/api/ctyOrderManagement/settlementDoc.js'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import ProjectCenter from '../components/layout/index.vue'

const route = useRoute()

const orderApplicationList = ref([])
const settlementDocList = ref([])
const orderApplicationLoading = ref(false)
const settlementDocLoading = ref(false)

async function getOrderApplicationList() {
  orderApplicationLoading.value = true
  try {
    const res = await getOrderPaperLedgerPageList({
      page: {
        pageNum: 1,
        pageSize: 9999,
      },
      params: {
        projectCode: route.query.pcode,
        statusList: [3],
      },
    })
    orderApplicationList.value = res.data.list
  } catch (error) {
    console.log(error)
  } finally {
    orderApplicationLoading.value = false
  }
}

async function getSettlementDocList() {
  settlementDocLoading.value = true
  try {
    const res = await getSettlementDocPageList({
      page: {
        pageNum: 1,
        pageSize: 9999,
      },
      params: {
        projectCode: route.query.pcode,
        statusList: [3],
        totalAmount: [null, null],
      },
    })
    settlementDocList.value = res.data.list
  } catch (error) {
    console.log(error)
  } finally {
    settlementDocLoading.value = false
  }
}

onMounted(() => {
  getOrderApplicationList()
  getSettlementDocList()
})
</script>

<style scoped lang="scss">
.title {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid rgb(0 0 0 / 6%);

  .title-text {
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .desc {
    display: flex;
    align-items: center;
    margin-left: 20px;
    padding: 2px 8px;
    border-radius: 26px;
    background: rgb(0 0 0 / 3%);
    font-size: 14px;

    .desc-item {
      display: flex;
      align-items: center;

      .label {
        color: rgb(0 0 0 / 45%);
      }

      .value {
        color: rgb(0 0 0 / 88%);
      }
    }
  }
}

.sub-title {
  width: 100%;
  margin-top: 20px;
  margin-bottom: 20px;
  color: rgb(0 0 0 / 88%);
  font-weight: 600;
  font-size: 20px;
}
</style>
