import request from '@/utils/request'

// 新增、编辑楚天云应用系统
export function createOrUpdateCtCloudAppSystem(data) {
  return request({
    url: `/cloudorder/ct-cloud-app-system/createOrUpdate`,
    method: 'post',
    data,
  })
}

// 删除楚天云应用系统
export function deleteCtCloudAppSystem(params) {
  return request({
    url: `/cloudorder/ct-cloud-app-system/del`,
    method: 'get',
    params,
  })
}

// 获取楚天云应用系统详情
export function getCtCloudAppSystemDetail(params) {
  return request({
    url: `/cloudorder/ct-cloud-app-system/detail`,
    method: 'get',
    params,
  })
}

// 楚天云应用系统分页列表
export function getCtCloudAppSystemPageList(data) {
  return request({
    url: `/cloudorder/ct-cloud-app-system/pageList`,
    method: 'post',
    data,
  })
}

// 校验是否关联其他数据 false否true是
export function checkAssociationOther(params) {
  return request({
    url: `/cloudorder/ct-cloud-app-system/check-association-other`,
    method: 'get',
    params,
  })
}
