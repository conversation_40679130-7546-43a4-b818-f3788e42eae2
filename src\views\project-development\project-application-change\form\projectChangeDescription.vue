<template>
  <Title>项目变更说明</Title>
  <ElRow :gutter="30">
    <ElCol :span="8">
      <ElFormItem label="选择变更项目">
        <ElButton
          v-if="!formData.baseInfo.projectChangeDraft.prdProjectCode"
          type="primary"
          @click="isShowAssociationDialog = true">
          请选择
        </ElButton>
        <ElButton
          v-else
          type="primary"
          @click="resetData">
          清除选中
        </ElButton>
      </ElFormItem>
    </ElCol>
    <ElCol :span="8">
      <ElFormItem
        label="变更类型"
        prop="changeType">
        <ElSelect
          v-model="formData.baseInfo.changeType"
          placeholder="请选择">
          <ElOption
            v-for="(item, index) in yfxm_change_type"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
    </ElCol>
    <ElCol :span="8">
      <ElFormItem
        label="是否重大变更"
        prop="isImportantModification">
        <ElSelect
          v-model="formData.baseInfo.isImportantModification"
          placeholder="请选择">
          <ElOption
            v-for="(item, index) in [{ label: '是', value: '是' }, { label: '否', value: '否' }]"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        label="变更原因"
        prop="changeReason">
        <ElInput
          v-model="formData.baseInfo.changeReason"
          :rows="2"
          type="textarea"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        label="变更内容（变更前）"
        prop="changeBeforeContent">
        <ElInput
          v-model="formData.baseInfo.changeBeforeContent"
          :rows="2"
          type="textarea"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        label="变更内容（变更后）"
        prop="changeAfterContent">
        <ElInput
          v-model="formData.baseInfo.changeAfterContent"
          :rows="2"
          type="textarea"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        label="变更影响分析"
        prop="changeImpactAnalysis">
        <ElInput
          v-model="formData.baseInfo.changeImpactAnalysis"
          :rows="2"
          type="textarea"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
  </ElRow>
  <YfxmDialog
    v-model="isShowAssociationDialog"
    @associate="onAssociate" />
</template>

<script setup>
import { getApplicationChangeDetailByCode } from '@/api/project-development/project-application-change.js'
import Title from '@/components/Title/index.vue'
import YfxmDialog from './components/yfxmDialog.vue'

const props = defineProps({
  resetForm: {
    type: Function,
    required: true,
  },
})

const { proxy } = getCurrentInstance()

const { yfxm_change_type } = proxy.useDict('yfxm_change_type')

const formData = defineModel()

const isShowAssociationDialog = ref(false)

async function onAssociate(data) {
  // 这里选择项目之后查询详情
  const res = await getApplicationChangeDetailByCode(data.prdProjectCode)
  // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
  res.projectChangeDraft.fileUrlList = res.projectChangeDraft.attachments.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
  formData.value.baseInfo = res
}

function resetData() {
  formData.value = {
    baseInfo: {
      projectChangeDraft: {
        budgetInfo: { // 项目预算信息
          outsourcingCost: [], // 外采成本
          jobLaborCost: [], // 人力成本
          manageCost: [], // 项目管理费
          budgetOverview: { // 预算概览
            manageCost: [],
          },
        },
        selfOwnedProductsServicesDetail: [], // 自有产品服务明细
        incomeDetails: [], // 研发项目收益预测
      },
    },
  }
  props.resetForm()
}
</script>

<style lang="scss" scoped></style>
