import { post, remove } from '@/utils/alova.js'
/**
 * 工时管理-工时填报
 */

// 工时填报分页列表
export function getReportList(data) {
  return post('/workhour/timesheet/pageList', data)
}
// 删除工时填报
export function deleteReport(bid, config = { transformRes: false }) {
  return remove(`/workhour/timesheet/deleted/${bid}`, config)
}
// 工时填报详情
export function getReportDetail(bid) {
  return post(`/workhour/timesheet/detail/${bid}`)
}
// 可填报时长
export function getAccpetSubmitDays(date, submitterUserId) {
  return post(`/workhour/timesheet/getAcceptSubmitDays?date=${date}&submitterUserId=${submitterUserId}`)
}
// 工时填报创建
export function submitReport(data, config = { transformRes: false }) {
  return post('/workhour/timesheet/submit', data, config)
}
// 工时填报更新
export function updateReport(data, config = { transformRes: false }) {
  return post('/workhour/timesheet/modify', data, config)
}
