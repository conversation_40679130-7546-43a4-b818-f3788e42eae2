import { get, post, remove } from '@/utils/alova'
import { download } from '@/utils/request'

export const uploadUrl = `${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`
/**
 * 招采分页列表
 * @param {*} data
 * @returns
 */
export const getBiddingList = data => post('/purchaseManage/procurement/pageList', data)

/**
 * 获取采购预算
 * @param {*} data
 * @returns
 */
export const getPurchaseBudget = data => get('/purchaseManage/procurement/get/purchaseBudget', data)

/**
 * 获取详情
 * @param {*} data
 * @returns
 */
export const getBiddingDetail = data => get('/purchaseManage/procurement/get', data)

/**
 * 新增招采发起（提交审批）
 * @param {*} data
 * @returns
 */
export const addBidding = data => post('/purchaseManage/procurement/submit/add', data)
/**
 * 新增招采发起（暂存）
 * @param {*} data
 * @returns
 */
export const saveBidding = data => post('/purchaseManage/procurement/tempStore/add', data)
/**
 * 更新招采发起
 * @param {*} data
 * @returns
 */
export const updateBidding = data => post('/purchaseManage/procurement/update', data)
/**
 * 删除招采需求
 * @param {*} data
 * @returns
 */
export const rmBidding = data => remove (`/purchaseManage/procurement/del/${data}`)
/**
 * 供应商分页列表
 * @param {*} data
 * @returns
 */
export const getSupplierList = data => post('/purchaseManage/supplier/pageList', data)

export function exportBiddingList(data, filename) {
  return download('/purchaseManage/procurement/export', data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}
