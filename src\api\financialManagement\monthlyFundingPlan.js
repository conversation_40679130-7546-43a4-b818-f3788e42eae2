import request from '@/utils/request'

// 获取计划详情
export function getFundPlanDetail(params) {
  return request({
    url: `/finance/fund_plan/detail`,
    method: 'get',
    params,
  })
}

// 月度资金计划 分页列表
export function getFundPlanPageList(params) {
  return request({
    url: `/finance/fund_plan/pageList`,
    method: 'post',
    data: params,
  })
}

// 历史项目数据
export function getHistoryPlanProjectData(params) {
  return request({
    url: `/finance/fund_plan/historyPlanProjectData`,
    method: 'get',
    params,
  })
}

// 历史采购合同数据
export function getHistoryPlanPurchaseContractData(params) {
  return request({
    url: `/finance/fund_plan/historyPlanPurchaseContractData`,
    method: 'get',
    params,
  })
}

// 发起填报
export function initiatePlan(data) {
  return request({
    url: `/finance/fund_plan/initiatePlan`,
    method: 'post',
    data,
  })
}

// 资金计划明细，参数二传一
export function getFundPlanProjectList(params) {
  return request({
    url: `/finance/fund_plan/planProjectList`,
    method: 'get',
    params,
  })
}

// 保存填报
export function savePlan(data) {
  return request({
    url: `/finance/fund_plan/savePlan`,
    method: 'post',
    data,
  })
}

// 提交填报
export function submitPlan(data) {
  return request({
    url: `/finance/fund_plan/submitPlan`,
    method: 'post',
    data,
  })
}

// 新增项目销售合同填充数据
export function historyPlanProjectFillData(params) {
  return request({
    url: `/finance/fund_plan/historyPlanProjectFillData`,
    method: 'get',
    params,
  })
}

// 历史采购合同数据
export function historyPlanPurchaseContractData(params) {
  return request({
    url: `/finance/fund_plan/historyPlanPurchaseContractData`,
    method: 'get',
    params,
  })
}

// 获取采购合同列表
export function getPsmContractList(data) {
  return request({
    url: `/purchase/psmContractMoneyPlan/all/list`,
    method: 'post',
    data,
  })
}
