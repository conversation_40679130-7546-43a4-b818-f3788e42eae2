<template>
  <ElDialog
    v-model="dialogVisible"
    title="选择发票"
    width="1200px"
    :before-close="handleClose">
    <ElPopover
      placement="bottom-start"
      trigger="click"
      width="800px"
      :hide-after="0"
      :show-arrow="false">
      <template #reference>
        <ElButton>
          <i
            class="iconfont icon-sift"
            :style="{
              marginRight: '8px',
            }" />
          所有筛选
          <ElIcon
            :style="{
              marginLeft: '10px',
            }">
            <ArrowDown />
          </ElIcon>
        </ElButton>
      </template>
      <div style="padding: 20px 4px">
        <ElForm label-width="auto">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="合同名称">
                <ElInput
                  v-model="queryParams.contractName"
                  placeholder="请输入合同名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="发票号码">
                <ElInput
                  v-model="queryParams.invoiceNumber"
                  placeholder="请输入发票号码" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="是否虚拟票">
                <ElSelect
                  v-model="queryParams.isVirtualInvoice"
                  placeholder="请选择"
                  clearable>
                  <ElOption
                    :value="1"
                    label="是" />
                  <ElOption
                    :value="0"
                    label="否" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <div
                style="
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <ElButton @click="onReset">
                  重置
                </ElButton>
                <ElButton
                  type="primary"
                  @click="onSearch">
                  搜索
                </ElButton>
              </div>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>
    </ElPopover>

    <ElTable
      ref="tableRef"
      border
      :data="tableData"
      height="500px"
      style="margin-top: 20px"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick">
      <!-- 复选框列 -->
      <ElTableColumn
        type="selection"
        width="55"
        fixed="left"
        align="center" />
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        width="60"
        fixed="left"
        align="center"
        row-key="id" />
      <ElTableColumn
        prop="projectName"
        label="项目名称"
        width="120"
        align="center" />
      <ElTableColumn
        prop="contractName"
        label="合同名称"
        width="120"
        align="center" />
      <ElTableColumn
        prop="contractNumber"
        label="合同编号"
        width="120"
        align="center" />
      <ElTableColumn
        prop="invoiceNumber"
        label="发票号码"
        width="120"
        align="center" />
      <ElTableColumn
        prop="invoiceDate"
        label="开票日期"
        width="120"
        align="center">
        <template #default="{ row }">
          {{ row.invoiceDate ? new Date(row.invoiceDate).toLocaleDateString() : '' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="invoiceType"
        label="发票类型"
        width="120"
        align="center">
        <template #default="{ row }">
          {{ row.invoiceType === 1 ? '增值税电子普通发票' : '增值税电子专用发票' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="taxRate"
        label="税率"
        width="80"
        align="center">
        <template #default="{ row }">
          {{ row.taxRate }}%
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="taxAmount"
        label="税额"
        width="100"
        align="center">
        <template #default="{ row }">
          {{ row.taxAmount?.toFixed(2) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="invoiceAmountIncludeTax"
        label="开票金额（含税：元）"
        width="150"
        align="center">
        <template #default="{ row }">
          {{ row.invoiceAmountIncludeTax?.toFixed(2) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="invoiceAmount"
        label="开票金额（不含税：元）"
        width="150"
        align="center">
        <template #default="{ row }">
          {{ row.invoiceAmount?.toFixed(2) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="isVirtualInvoice"
        label="是否虚拟票"
        width="100"
        align="center">
        <template #default="{ row }">
          {{ row.isVirtualInvoice === 1 ? '是' : '否' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="purchaser"
        label="购买方"
        width="150"
        align="center" />
      <ElTableColumn
        prop="seller"
        label="销售方"
        width="150"
        align="center" />
    </ElTable>
    <div class="pagination-container">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="getList" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleConfirm"> 确定 </ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup>
import { getCanConfirmIncomeList } from '@/api/financialManagement/managementOfInvoices/invoiceLedger.js'
import { usePagination } from '@/utils/hooks.js'
import { nextTick, reactive, ref } from 'vue'

// 定义组件属性和事件
const props = defineProps({
  currentId: {
    type: Array,
    default: () => [],
  },
  projectCode: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['change'])

// 表格和分页相关
const { pagination, indexMethod } = usePagination()
const dialogVisible = ref(false)
const tableData = ref([])
const tableRef = ref(null)
const selectedRows = ref([])
const selectedIds = ref([])

// 查询参数
const queryParams = reactive({
  contractName: '',
  isVirtualInvoice: '',
  invoiceNumber: '',
})

function handleRowClick(row) {
  tableRef.value.toggleRowSelection(row)
}

/**
 * 处理表格选择变化
 * @param {Array} rows - 当前选中的行
 */
function handleSelectionChange(rows) {
  // 更新当前页面选中的行
  const currentPageSelectedRows = rows
  // 保留其他页面已选中但当前页面不存在的行
  const otherPagesSelectedRows = selectedRows.value.filter((row) => {
    const rowId = row.id
    return !tableData.value.some((currentRow) => {
      const currentRowId = currentRow.id
      return currentRowId === rowId
    })
  })
  // 合并当前页面选中的行和其他页面已选中的行
  selectedRows.value = [...currentPageSelectedRows, ...otherPagesSelectedRows]
  // 更新选中的ID列表
  selectedIds.value = selectedRows.value.map(row => row.id)
  console.log('选择变化后的ID列表:', selectedIds.value)
}

/**
 * 打开对话框
 */
function showDialog() {
  dialogVisible.value = true
  // 获取发票列表
  onReset()
}

/**
 * 确认选择
 */
function handleConfirm() {
  // 触发change事件，传递选中的ID和完整数据
  console.log(selectedIds.value, selectedRows.value, '--- keys rows')
  emit('change', selectedIds.value, selectedRows.value)
  handleClose()
}

/**
 * 关闭对话框
 */
function handleClose() {
  tableData.value = []
  selectedIds.value = []
  selectedRows.value = []
  pagination.pageNum = 1
  dialogVisible.value = false
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getList()
}

/**
 * 重置查询条件
 */
function onReset() {
  queryParams.contractName = ''
  queryParams.isVirtualInvoice = ''
  queryParams.invoiceNumber = ''
  pagination.pageNum = 1
  getList()
}

/**
 * 执行搜索
 */
function onSearch() {
  pagination.pageNum = 1
  getList()
}

/**
 * 获取发票列表
 */
function getList() {
  // 构建查询参数
  const searchData = {
    page: {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    },
    params: {
      projectCode: props.projectCode,
      idFilterList: props.currentId,
      status: 3,
      type: 1,
      ...queryParams,
    },
  }

  console.log('查询前选中ID:', selectedIds.value)

  // 调用API获取数据
  getCanConfirmIncomeList(searchData)
    .then((res) => {
      console.log('API返回数据:', res)
      if (res.code === 200) {
        const list = res.data?.list || []
        console.log('获取到的发票列表:', list)
        tableData.value = list
        pagination.total = res.data?.total || 0
        nextTick(() => {
          if (tableRef.value) {
            tableRef.value.clearSelection()
          }
        })
      } else {
        console.error('API返回错误:', res.message || '未知错误')
      }
    })
    .catch((err) => {
      console.error('获取发票列表失败:', err)
    })
}

// 暴露方法给父组件
defineExpose({
  showDialog,
})
</script>

  <style lang="scss" scoped>
  .dialog-footer button:first-child {
  margin-right: 10px;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
