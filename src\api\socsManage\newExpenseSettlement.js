import request from '@/utils/request'

// 暂存、更新服务费用结算
export function createOrUpdateServiceFeeSettlement(data) {
  return request({
    url: '/cloudorder/gz-cloud-service-fee-settlement/createOrUpdate',
    method: 'post',
    data,
  })
}

// 删除服务费用结算
export function deleteServiceFeeSettlement(params) {
  return request({
    url: '/cloudorder/gz-cloud-service-fee-settlement/del',
    method: 'get',
    params,
  })
}

// 获取详情
export function getServiceFeeSettlementDetail(params) {
  return request({
    url: '/cloudorder/gz-cloud-service-fee-settlement/detail',
    method: 'get',
    params,
  })
}

// 服务费用结算分页列表
export function getServiceFeeSettlementPageList(data) {
  return request({
    url: '/cloudorder/gz-cloud-service-fee-settlement/pageList',
    method: 'post',
    data,
  })
}

// 提交服务费用结算
export function submitServiceFeeSettlement(data) {
  return request({
    url: '/cloudorder/gz-cloud-service-fee-settlement/submit-resource-request',
    method: 'post',
    data,
  })
}

// 审核中提交结算单详情
export function submitWithApprove(data) {
  return request({
    url: '/cloudorder/gz-cloud-service-fee-settlement/submit-with-approve',
    method: 'post',
    data,
  })
}
