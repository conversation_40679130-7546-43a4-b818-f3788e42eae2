import { get, post } from '@/utils/alova.js'
import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 暂存（创建）项目结项
 * @param {*} data
 * @returns
 */
export function createProjectKnot(data) {
  return post(`${prefix}/knot/create`, data)
}

/**
 * 提交项目结项
 * @param {*} data
 * @returns
 */
export function submitProjectKnot(data) {
  return post(`${prefix}/knot/submit`, data)
}

/**
 * 修改项目结项
 * @param {*} id
 * @param {*} data
 * @returns
 */
export function updateProjectKnot(id, data) {
  return post(`${prefix}/knot/update/${id}`, data)
}

/**
 * 获取项目结项列表
 * @param {*} data
 * @returns
 */
export function selectProjectKnotList(data) {
  return request({
    url: `${prefix}/knot/pageList`,
    method: 'post',
    data,
  })
}

/**
 * 获取项目结项详情
 * @param {*} id
 * @returns
 */
export function getProjectKnotDetail(id) {
  return request({
    url: `${prefix}/knot/details/${id}`,
    method: 'post',
  })
}

/**
 * 批量删除项目结项
 * @param {*} ids
 * @returns
 */
export function removeProjectKnot(ids) {
  return request({
    url: `${prefix}/knot/removeBatch`,
    method: 'post',
    data:{idList:ids},
  })
}
