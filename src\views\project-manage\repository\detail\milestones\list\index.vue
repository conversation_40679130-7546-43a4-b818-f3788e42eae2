<template>
  <CenterContent
    title="里程碑"
    :milestone-count="changeCount">
    <template #table>
      <ElTabs v-model="activeName">
        <ElTabPane
          label="基线里程碑"
          name="base">
          <BasePane @update-count="updateCount" />
        </ElTabPane>
        <ElTabPane
          label="计划里程碑"
          name="plan">
          <PlanPane />
        </ElTabPane>
      </ElTabs>
    </template>
  </CenterContent>
</template>

<script setup>
import CenterContent from '@/views/project-manage/repository/detail/components/CenterContent.vue'
import BasePane from './base-pane.vue'
import PlanPane from './plan-pane.vue'

const activeName = ref('base')

const changeCount = ref(0)
function updateCount(count) {
  changeCount.value = count
}
</script>

<style lang="scss" scoped>
:deep(.el-tabs) {
  width: 100%;
  height: 100%;

  .el-tabs__content {
    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
