<template>
  <Title>项目基本信息</Title>
  <ElDescriptions
    border
    label-width="160"
    :column="4">
    <ElDescriptionsItem
      label="项目名称"
      :span="2">
      {{ formData.prdProjectName }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="项目编号"
      :span="1">
      {{ formData.prdProjectCode }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发负责人"
      :span="1">
      {{ formData.principal }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="业务主体">
      {{ formData.prdEntityDept }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分类">
      {{ formData.prdProjectCategory }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="研发方式">
      {{ formData.rdMethod }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分级">
      {{ formData.projectClassification }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="立项日期">
      {{ formData.projectInitiationDate }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="预算金额（万元）">
      {{ formData.budgetAmount }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="项目分管领导"
      :span="2">
      {{ formData.projectLeader }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发项目基本情况"
      :span="4">
      {{ formData.projectBasicInformation }}
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const formData = defineModel()
</script>

<style lang="scss" scoped>

</style>
