import request from '@/utils/request'

// 暂存、更新结算单
export function createOrUpdateSettlementDoc(data) {
  return request({
    url: '/cloudorder/cloud_ct_statement/createOrUpdate',
    method: 'post',
    data,
  })
}

// 删除结算单
export function deleteSettlementDoc(params) {
  return request({
    url: '/cloudorder/cloud_ct_statement/del',
    method: 'get',
    params,
  })
}

// 获取详情
export function getSettlementDocDetail(params) {
  return request({
    url: '/cloudorder/cloud_ct_statement/detail',
    method: 'get',
    params,
  })
}

// 下载结算单
export function downloadSettlementDoc(params) {
  return request({
    url: '/cloudorder/cloud_ct_statement/download',
    method: 'get',
    params,
  })
}

// 结算单分页列表
export function getSettlementDocPageList(data) {
  return request({
    url: '/cloudorder/cloud_ct_statement/pageList',
    method: 'post',
    data,
  })
}

// 提交结算单
export function submitSettlementDoc(data) {
  return request({
    url: '/cloudorder/cloud_ct_statement/submit',
    method: 'post',
    data,
  })
}

// 审核中 提交结算单详情
export function submitSettlementDocWithApprove(data) {
  return request({
    url: '/cloudorder/cloud_ct_statement/submit-with-approve',
    method: 'post',
    data,
  })
}
