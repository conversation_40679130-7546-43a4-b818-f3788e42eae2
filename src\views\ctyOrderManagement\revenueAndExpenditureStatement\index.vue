<template>
  <DefaultContainer v-loading="isLoading">
    <!-- 折叠面板组件，用于查询条件 -->
    <Collapse>
      <template #header />
      <!-- 查询表单 -->
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="200px"
        label-position="top">
        <ElFormItem
          prop="projectName"
          label="项目名称">
          <ElSelect
            v-model="formData.projectName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectProjectModal = true)
            " />
        </ElFormItem>
      </ElForm>
    </Collapse>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
    </div>
    <!-- 数据表格 -->
    <div class="table-box">
      <div class="sub-title">
        对应合同和应用系统信息
        <ElButton
          type="primary"
          @click="handleExport()">
          导出
        </ElButton>
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 10px;"
        label-width="200">
        <ElDescriptionsItem
          width="398">
          <template #label>
            <div>项目编号</div>
          </template>
          {{ formData.projectCode }}
        </ElDescriptionsItem>
        <ElDescriptionsItem
          width="398">
          <template #label>
            <div>项目云预算金额(元)</div>
          </template>
          {{ projectCloudBudgetAmount }}
        </ElDescriptionsItem>
        <ElDescriptionsItem
          width="398">
          <template #label>
            <div>预算有效周期(天)</div>
          </template>
          {{ budgetEffectivePeriod }}
        </ElDescriptionsItem>
      </ElDescriptions>
      <ElTable
        :max-height="1000"
        border
        :summary-method="getSummary1"
        show-summary
        :data="tableData1">
        <ElTableColumn
          type="index"
          label="序号"
          width="60"
          fixed="left"
          align="center" />
        <ElTableColumn
          prop="saleContractNumber"
          label="销售合同编号"
          min-width="120" />
        <ElTableColumn
          prop="saleContractName"
          label="销售合同名称"
          min-width="120" />
        <ElTableColumn
          prop="ctAppName"
          label="应用系统名称"
          min-width="120" />
        <ElTableColumn
          prop="customerName"
          label="客户名称"
          min-width="120" />
        <ElTableColumn
          prop="contractServiceAmount"
          label="合同中的云服务金额(元)"
          min-width="180" />
        <ElTableColumn
          prop="contractServiceStartTime"
          label="合同中的云服务开始日期"
          min-width="180" />
        <ElTableColumn
          prop="contractServiceEndTime"
          label="合同中的云服务结束日期"
          min-width="180" />
        <ElTableColumn
          prop="contractServicePerformanceCycle"
          label="合同中的云服务履约周期(天)"
          min-width="190" />
        <ElTableColumn
          prop="chargeDateStart"
          label="结算开始计费日期"
          min-width="170" />
        <ElTableColumn
          prop="chargeDateEnd"
          label="结算结束计费日期"
          min-width="170" />
        <ElTableColumn
          prop="statementCycle"
          label="结算周期(天)"
          min-width="120" />
      </ElTable>
      <ElDescriptions
        border
        :column="1"
        style="margin-top: 10px;"
        label-width="200">
        <ElDescriptionsItem
          width="398">
          <template #label>
            <div>合同中系统云服务所占合计金额</div>
          </template>
          {{ theTotalAmountOfSystemCloudServicesInTheContract }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
    <SelectProjectModal
      v-model="modelIsShow.selectProjectModal"
      @select-item="onChangeProject" />
  </DefaultContainer>
</template>

<script setup>
import { getCtCloudIncomeAndExpenseStatement } from '@/api/ctyOrderManagement/revenueAndExpenditureStatement.js'
import Collapse from '@/components/Collapse/index.vue'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import SelectProjectModal from '@/views/financialManagement/modules/SelectProjectModal.vue'
import { useCloudBudget } from '@/views/socsManage/utils/hook.js'
import { ElMessage } from 'element-plus'
import { computed, reactive, ref } from 'vue'

/**
 * 表单数据与引用
 */
const formData = reactive({
  projectName: '',
  projectCode: '',
}) // 查询表单数据
const formRef = ref() // 表单引用，用于重置
const projectCloudBudgetAmount = ref(0)
const budgetEffectivePeriod = ref(0)
/**
 * 表格数据与加载状态
 */
const tableData1 = ref([]) // 表格数据
const loadingIndex = ref(0) // 加载计数器，用于处理多个并发请求的loading状态

/**
 * 计算属性：是否显示加载状态
 * 当loadingIndex > 0时显示加载状态
 */
const isLoading = computed(() => {
  return loadingIndex.value !== 0
})

const modelIsShow = reactive({
  selectProjectModal: false,
})

const theTotalAmountOfSystemCloudServicesInTheContract = computed(() => {
  return formatAmount(tableData1.value.reduce((acc, curr) => acc + curr.contractServiceAmount, 0))
})

/**
 * 获取应用系统信息分页列表
 */
async function getPage() {
  loadingIndex.value++ // 开始加载，计数器加1

  try {
    if (formData.projectCode) {
      const searchData = {
        projectCode: formData.projectCode,
        params: {},
      }
      const res1 = await getCtCloudIncomeAndExpenseStatement(searchData)
      if (res1.code === 200) {
        tableData1.value = res1.data
        budgetEffectivePeriod.value = res1.data[0]?.budgetEffectivePeriod || 0
      }
    } else {
      ElMessage.warning('请选择项目')
      tableData1.value = []
    }
  } finally {
    // 确保无论成功还是失败，都将 loadingIndex 重置为 0
    loadingIndex.value = Math.max(0, loadingIndex.value - 1)
  }
}

/**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  if (formData.projectCode) {
    getPage()
  } else {
    ElMessage.warning('请选择项目')
    tableData1.value = []
  }
}

/**
 * 重置方法
 * 重置表单并查询第一页数据
 */
function onReset() {
  formRef.value.resetFields() // 重置表单字段
  formData.projectName = ''
  formData.projectCode = ''
  projectCloudBudgetAmount.value = 0
  budgetEffectivePeriod.value = 0
  tableData1.value = []
}

const { getCtyBudgetAmount } = useCloudBudget()

function onChangeProject(row) {
  formData.projectName = row.projectName || row.bolProjectName
  formData.projectCode = row.projectCode
  projectCloudBudgetAmount.value = getCtyBudgetAmount(row, '1')

  onSearch()
}

const { proxy } = getCurrentInstance()
function handleExport() {
  if (formData.projectCode) {
    const params = {
      projectCode: formData.projectCode,
    }
    proxy.download(
      '/cloudorder/ct-cloud-income-and-expense/downloadStatement',
      {
        ...params,
      },
      `对应合同和应用系统信息_${new Date().getTime()}.xlsx`,
    )
  } else {
    ElMessage.warning('请选择项目')
  }
}

function getSummary1(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 定义需要计算合计的列
    const sumCols = [
      'contractServiceAmount',
    ]

    if (!sumCols.includes(column.property)) {
      sums[index] = ''
      return
    }

    // 计算需要合计的列的总和
    const values = data.map(item => Number(item[column.property]))
    if (!values.every(value => Number.isNaN(value))) {
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        }
        return prev
      }, 0)
      sums[index] = formatAmount(sum)
    } else {
      sums[index] = ''
    }
  })
  return sums
}

function formatAmount(amount) {
  if (!amount && amount !== 0)
    return '-'
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}
</script>

<style lang="scss" scoped>
.sub-title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin: 10px 0;
  color: rgb(0 0 0 / 88%);
  font-weight: 600;
  font-size: 20px;
}

.table-box {
  flex: 1;
  overflow: auto;
  width: 100%;
  min-height: 0;
  margin-top: 10px;
  padding-right: 20px;
}
</style>
