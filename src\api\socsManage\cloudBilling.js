import request from '@/utils/request'

// 校验是否关联其他数据 false否true是
export function checkAssociationOther(params) {
  return request({
    url: '/cloudorder/gz-cloud-bill/check-association-other',
    method: 'get',
    params,
  })
}

// 校验是否国资云负责人 false否true是
export function checkGzCloudLeader(params) {
  return request({
    url: '/cloudorder/gz-cloud-bill/check-gzcloud-leader',
    method: 'get',
    params,
  })
}

// 新增、编辑账单
export function createOrUpdate(data) {
  return request({
    url: '/cloudorder/gz-cloud-bill/createOrUpdate',
    method: 'post',
    data,
  })
}

// 删除账单
export function del(params) {
  return request({
    url: '/cloudorder/gz-cloud-bill/del',
    method: 'get',
    params,
  })
}

// 国资云账单详情
export function getDetail(params) {
  return request({
    url: '/cloudorder/gz-cloud-bill/detail',
    method: 'get',
    params,
  })
}

// 下载账单
export function download(params) {
  return request({
    url: '/cloudorder/gz-cloud-bill/download',
    method: 'get',
    params,
  })
}

// 账单分页列表
export function pageList(data) {
  return request({
    url: '/cloudorder/gz-cloud-bill/pageList',
    method: 'post',
    data,
  })
}
