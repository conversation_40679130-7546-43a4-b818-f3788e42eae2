<template>
  <Container>
    <TableContainer title="项目文档列表">
      <template #search="{ searchBoxWidth }">
        <FilterCriteria
          :popover-width="searchBoxWidth"
          @search="onSearch"
          @reset="onReset">
          <ElForm
            ref="formRef"
            label-position="left"
            label-width="90px"
            :model="filterFormData">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem
                  label="项目归属部门："
                  prop="deliveryEntityId">
                  <ElTreeSelect
                    v-model="filterFormData.deliveryEntityId"
                    :multiple="true"
                    collapse-tags
                    :teleported="false"
                    :render-after-expand="false" />
                </ElFormItem>
                <!-- <ElFormItem label="项目归属部门">
                  <ElInput v-model="filterFormData.deliveryEntityId" />
                </ElFormItem> -->
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="验收年份">
                  <ElDatePicker
                    v-model="filterFormData.acceptanceDate"
                    type="year" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="项目编号">
                  <ElInput v-model="filterFormData.projectCode" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="项目名称">
                  <ElInput v-model="filterFormData.projectName" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </FilterCriteria>
      </template>

      <template #toolbar>
        <div class="flex justify-between">
          <ElButton
            type="primary"
            @click="() => onCreate(null, 'form')">
            新建
          </ElButton>
          <ElButton @click="exportHandler">
            <template #icon>
              <i class="iconfont icon-UploadOutlined" />
            </template>
            下载
          </ElButton>
        </div>
      </template>

      <template #default="{ contentHeight }">
        <div style="padding: 8px 0px;display: flex;align-items: center;">
          <span style="font-size: 14px;color: #515a6e;">验收项目个数</span>  <ElInput
            v-model="projectCount"
            style="width: 150px;margin-left: 12px;" />
        </div>
        <ElTable
          :max-height="contentHeight"
          :data="tableData"
          border
          style="width: 100%"
          @row-dblclick="row => onCreate(row, 'view')">
          <ElTableColumn
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
            width="100" />
          <ElTableColumn
            align="center"
            width="100"
            prop="status"
            label="状态">
            <template #default="{ row }">
              <ElTag :type="getStatusTagType(row.status)?.type">
                {{ getStatusTagType(row.status)?.text }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="projectCode"
            label="项目编号" />
          <ElTableColumn
            prop="projectName"
            label="项目名称" />
          <ElTableColumn
            prop="deliveryEntity"
            label="项目归属部门" />
          <ElTableColumn
            width="160"
            prop="acceptanceDate"
            label="验收年份" />
          <ElTableColumn
            width="160"
            prop="electronicSubmittedCount"
            label="已提交电子文档个数">
            <template #default="{ row }">
              <span style="color: #67C23A">{{ row.electronicSubmittedCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            width="160"
            prop="electronicMissingCount"
            label="缺失电子文档个数">
            <template #default="{ row }">
              <span style="color: #F56C6C">{{ row.electronicMissingCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            width="160"
            prop="electronicPendingCount"
            label="待整改电子文档个数">
            <template #default="{ row }">
              <span style="color: #E6A23C">{{ row.electronicPendingCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            width="160"
            prop="paperArchivedCount"
            label="纸质文档入档个数">
            <template #default="{ row }">
              <span style="color: #E6A23C">{{ row.paperArchivedCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            width="160"
            prop="paperMissingCount"
            label="缺失纸质文档个数">
            <template #default="{ row }">
              <span style="color: #E6A23C">{{ row.paperMissingCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            fixed="right"
            width="150"
            label="操作类型">
            <template #default="{ row }">
              <ElButton
                v-if="row.status === 0"
                type="primary"
                link
                @click="onCreate(row, 'edit')">
                编辑
              </ElButton>
              <ElButton
                v-if="row.status === 0"
                type="danger"
                link
                @click="onDelete(row)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <template #footer>
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="(size) => (onReset({ size, isClear: false }))" />
      </template>
    </TableContainer>
  </Container>
</template>

<script setup>
import { getProjectDocsList, removeProjectDocs } from '@/api/system/docs.js'
import Container from '@/components/Container/index.vue'
import TableContainer from '@/components/Container/table-container.vue'
import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'
import { usePagination } from '@/utils/hooks.js'
import { ElMessage } from 'element-plus'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const router = useRouter()
const filterFormData = reactive({
  deliveryEntityId: null, // 项目归属部门
  acceptanceDate: '', // 验收年份
  projectCode: '', // 项目编号
  projectName: '', // 项目名称
})
const tableData = ref([])
const projectCount = ref(0)

const { pagination, indexMethod } = usePagination()

onMounted(() => {
  onSearch()
})

async function onSearch() {
  try {
    const res = await getProjectDocsList({
      page: {
        ...pagination,
      },
      params: {
        ...filterFormData,
      },
    })
    const { total, list } = res.data
    tableData.value = list
    pagination.total = total
    // TODO: 获取项目数量
    projectCount.value = 0
  } catch (error) {
    console.log(error)
  }
}
const formRef = useTemplateRef('formRef')
function onReset(data = {}) {
  const config = { isClear: true, size: 10, ...data }
  if (config.isClear) {
    formRef.value.resetFields()
  }
  filterFormData.deliveryEntityId = null
  filterFormData.acceptanceDate = ''
  filterFormData.projectCode = ''
  filterFormData.projectName = ''
  pagination.pageSize = config.size
  pagination.pageNum = 1
  onSearch()
}

function onCreate(config = {}, pageType = 'form') {
  router.push({
    path: '/project-docs-manage/docs/create/form',
    query: {
      id: config?.id,
      bid: config?.bid,
      pageType,
    },
  })
}

// 导出
function exportHandler() {
  proxy.download(
    '/project/doc/export',
    {
      ...filterFormData,
    },
    `项目终止、挂起_${new Date().getTime()}.xlsx`,
  )
}

// 状态标签样式
function getStatusTagType(status) {
  const statusList = [
    { status: 0, type: 'info', text: '草稿' },
    { status: 1, type: 'warning', text: '进行中' },
    { status: 2, type: 'success', text: '已完成' },
  ]
  return statusList.find(item => item.status === status)
}

async function onDelete(row) {
  try {
    const ids = [row.id] // 单条删除时取当前行ID
    await removeProjectDocs(ids)
    // 强制重置分页并重新请求
    pagination.pageNum = 1
    await onSearch()
    ElMessage.success('删除成功')
  } catch (error) {
    console.log(error)
  }
}
</script>

<style lang="scss" scoped>

</style>
