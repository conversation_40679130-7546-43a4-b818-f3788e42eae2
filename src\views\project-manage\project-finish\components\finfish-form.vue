<template>
  <div class="main_container">
    <ElForm
      ref="formRef"
      label-position="top"
      :model="formData"
      :rules="rules">
      <Title is-hidden>
        结项申请
      </Title>
      <ApplyForm
        v-model:form-data="formData"
        :form-type="formType" />
      <ProjectForm
        v-model:form-data="formData"
        :form-type="formType" />
      <ProjectManageForm
        v-if="user.admin || user.roleId === 109"
        v-model:form-data="formData"
        :form-type="currentFormType" />
      <MarketManageForm
        v-if="user.admin || user.roleId === 108"
        v-model:form-data="formData"
        :form-type="currentFormType" />
      <ElDivider v-if="user.admin || user?.dept?.deptId === 203 || user?.dept?.deptId === 227">
        资料审查归档
      </ElDivider>
      <ElRow :gutter="20">
        <ElCol :span="6">
          <ElFormItem
            v-if="user.admin || user?.dept?.deptId === 203"
            label="项目电子文档检查"
            prop="financialSettlement.docCheckComment">
            <ElInput
              v-model="formData.financialSettlement.docCheckComment"
              :disabled="currentFormType !== 'process'" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            v-if="user.admin || user?.dept?.deptId === 227"
            label="纸质文件归档说明"
            prop="financialSettlement.paperFileComment">
            <ElInput
              v-model="formData.financialSettlement.paperFileComment"
              :disabled="currentFormType !== 'process'" />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <FinanceForm
        v-if="user.admin || user.roleId === 106"
        v-model:form-data="formData"
        :form-type="currentFormType" />
      <ElCol :span="24">
        <ElFormItem
          label="补充说明"
          prop="additionalNotes">
          <ElInput
            v-model="formData.projectBasicInfo.additionalNotes"
            :disabled="formType === 'view'"
            type="textarea"
            :rows="4"
            placeholder="请输入补充说明" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem
          label="附件"
          prop="attachments">
          <ElUpload
            v-model:file-list="formData.projectBasicInfo.attachments"
            :disabled="formType === 'view'"
            style="width: 100%;"
            :action="uploadUrl"
            drag
            multiple
            :on-preview="on_preview_or_downFile">
            <ElIcon class="el-icon--upload">
              <UploadFilled />
            </ElIcon>
            <div class="el-upload__text">
              拖动文件或者 <em>点击此处上传</em>
            </div>
          </ElUpload>
        </ElFormItem>
      </ElCol>
    </ElForm>
  </div>
</template>

<script setup>
import { createProjectKnot, getProjectKnotDetail, submitProjectKnot, updateProjectKnot } from '@/api/project-manage/projectFinish.js'
import { getCurrentProcessId } from '@/api/wflow-pro.js'
import Title from '@/components/Title/index.vue'
import useUserStore from '@/store/modules/user.js'
import { on_preview_or_downFile } from '@/utils/hooks.js'
import { UploadFilled } from '@element-plus/icons-vue'
import { uploadUrl } from '@wflow-pro/api/request'
import { dayjs, ElMessage } from 'element-plus'
import { klona } from 'klona'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import ApplyForm from './apply-form.vue'
import FinanceForm from './finance-form.vue'
import MarketManageForm from './market-manage-form.vue'
import ProjectForm from './project-form.vue'
import ProjectManageForm from './project-manage-form.vue'

const { id, formType } = defineProps({
  id: {
    type: String,
    default: '',
  },
  formType: {
    type: String,
    default: 'view',
  },
})
const route = useRoute()
const currentFormType = ref(formType)
const userStore = useUserStore()
const user = ref({})
const formData = reactive({
  applicationInfo: {
    applicant: userStore.nickName,
    applicantDept: userStore.deptName,
    applyDate: dayjs().format('YYYY-MM-DD'),
    knotDate: '',
  },
  projectBasicInfo: {
    projectName: '',
    additionalNotes: '',
    projectManager: '',
    projectManagerId: '',
    marketingManager: '',
    marketingManagerId: '',
    projectLeader: '',
    projectLeaderId: '',
    deliveryEntity: '',
    deliveryEntityId: '',
    // TODO:字段暂定
    projectStatus: '',
    acceptanceDate: '',
    knotType: null,
    abnormalClosing: '',
    attachments: [],
  },
  projectManagerVerification: {
    contractCompleted: 0,
    procurementPaid: 0,
    reimbursementDone: 0,
    docsUploaded: 0,
    paperArchived: 0,
  },
  marketManagerVerification: {
    contractAllPaid: 0,
    marketReimbursementDone: 0,
  },
  documentReview: {
    electronicCheck: '',
    paperArchive: '',
  },
  financialSettlement: {
    income: null,
    costProcurement: '',
    costLabor: '',
    costManagement: '',
    costPresale: '',
    costDelivery: '',
    paymentReceived: '',
    paymentPaid: '',
    grossProfit: '',
    profitMargin: '',
  },
})
const processDefId = ref('')

onMounted(async () => {
  const bid = route.query?.businessId
  if (bid) {
    currentFormType.value = 'process'
  }
  const pid = await getCurrentProcessId('project-finish')
  processDefId.value = pid
  if (id || bid) {
    try {
      const res = await getProjectKnotDetail(id || bid)
      const data = res.data
      // applicationInfo
      formData.applicationInfo.applicant = data.applicant
      formData.applicationInfo.applicantDept = data.applicantDept
      formData.applicationInfo.createTime = data.applyDate
      formData.applicationInfo.knotDate = data.knotDate

      // projectBasicInfo
      formData.projectBasicInfo.projectName = data.projectName
      formData.projectBasicInfo.projectCode = data.projectCode
      formData.projectBasicInfo.projectManager = data.projectManager
      formData.projectBasicInfo.projectManagerId = data.projectManagerId
      formData.projectBasicInfo.marketingManager = data.marketingManager
      formData.projectBasicInfo.marketingManagerId = data.marketingManagerId
      formData.projectBasicInfo.projectLeader = data.projectLeader
      formData.projectBasicInfo.projectLeaderId = data.projectLeaderId
      formData.projectBasicInfo.deliveryEntity = data.deliveryEntity
      formData.projectBasicInfo.deliveryEntityId = data.deliveryEntityId
      formData.projectBasicInfo.projectStatus = data.projectStatus
      formData.projectBasicInfo.acceptanceDate = data.acceptanceDate
      formData.projectBasicInfo.knotType = data.knotType
      formData.projectBasicInfo.abnormalClosing = data.abnormalClosing
      formData.projectBasicInfo.additionalNotes = data.additionalNotes

      // ===== 附件处理 =====
      if (data.appendix) {
        formData.projectBasicInfo.attachments = data.appendix.split(',').map((url) => {
          const name = url.match(/([^/]+)(?=\.[^.]+$|$)/) ? url.match(/([^/]+)(?=\.[^.]+$|$)/)[0] : ''
          return { url, name }
        })
      }
      // formData.projectBasicInfo.attachments = data.appendix.split(',').map(url => (
      //   { url: url , name :  url.lastIndexOf('/') !== -1 ? url.slice(url.lastIndexOf('/')+1) : "" }
      // ));

      // projectManagerVerification
      formData.projectManagerVerification.contractCompleted = data.contractCompleted
      formData.projectManagerVerification.procurementPaid = data.procurementPaid
      formData.projectManagerVerification.reimbursementDone = data.reimbursementDone
      formData.projectManagerVerification.docsUploaded = data.docsUploaded
      formData.projectManagerVerification.paperArchived = data.paperArchived

      // marketManagerVerification
      formData.marketManagerVerification.contractAllPaid = data.contractAllPaid
      formData.marketManagerVerification.marketReimbursementDone = data.marketReimbursementDone

      // documentReview
      formData.documentReview.electronicCheck = data.electronicCheck
      formData.documentReview.paperArchive = data.paperArchive

      // financialSettlement
      formData.financialSettlement.income = data.income
      formData.financialSettlement.costProcurement = data.costProcurement
      formData.financialSettlement.costLabor = data.costLabor
      formData.financialSettlement.costManagement = data.costManagement
      formData.financialSettlement.costPresale = data.costPresale
      formData.financialSettlement.costDelivery = data.costDelivery
      formData.financialSettlement.paymentReceived = data.paymentReceived
      formData.financialSettlement.paymentPaid = data.paymentPaid
      formData.financialSettlement.grossProfit = data.grossProfit
      formData.financialSettlement.profitMargin = data.profitMargin
    } catch (error) {
      ElMessage.error('获取结项数据失败')
      console.error('Error fetching project detail:', error)
    }
  }
  // 项目经理   roleId 109
  // 市场经理   roleId 108
  // 财务       roleId 106
  // 综合办公室 deptId  203
  // 经营管理部 deptId  227
  // 管理员     roles['admin']
  const userInfo = await useUserStore().getInfo()
  user.value = userInfo.user
})
const formRef = useTemplateRef('formRef')
const rules = {
  'applicationInfo.applicant': [
    { required: true, message: '请输入申请人', trigger: 'blur' },
  ],
  'applicationInfo.department': [
    { required: true, message: '请输入申请部门', trigger: 'blur' },
  ],
  'applicationInfo.applyDate': [
    { required: true, message: '请输入申请时间', trigger: 'blur' },
  ],
  // 'applicationInfo.knotDate': [
  //   { required: true, message: '审批通过后显示日期', trigger: 'blur' },
  // ],
  'projectBasicInfo.projectName': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.projectCode': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.projectManager': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.marketingManager': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.projectLeader': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.deliverySubject': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.projectStatus': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.acceptanceDate': [
    { required: true, message: '请选择项目', trigger: 'blur' },
  ],
  'projectBasicInfo.knotType': [
    { required: true, message: '请选择结项类型', trigger: 'blur' },
  ],
  'projectBasicInfo.abnormalReason': [
    { required: true, message: '请填写异常结项原因', trigger: 'blur' },
  ],
}
const project_dialog_options = reactive({
  visible: false,
  project_info: {
    projectManagerName: '',
    projectManagerId: '',
  },
  onOpen: () => {
    project_dialog_options.visible = true
  },
  checked: (data) => {
    console.log(data)
    project_dialog_options.pre_project_info = klona(data)
    formData.projectBasicInfo.projectName = data.projectName
    formData.projectBasicInfo.projectCode = data.projectCode
    formData.projectBasicInfo.projectManager = data.projectManagerName
    formData.projectBasicInfo.projectManagerId = data.projectManagerId
    formData.projectBasicInfo.marketingManager = data.marketManagerName
    formData.projectBasicInfo.marketingManagerId = data.marketManagerId
    formData.projectBasicInfo.projectLeader = data.leadershipName
    formData.projectBasicInfo.projectLeaderId = data.leadershipId
    formData.projectBasicInfo.deliverySubject = data.deliverySubject
    // TODO:字段暂定
    formData.projectBasicInfo.projectStatus = data.status
    formData.projectBasicInfo.acceptanceDate = data.acceptanceDate
  },
})
// 是否为当前表单填报页面
const isEdit = computed(() => route.path === '/project-manage/project-finish/apply/form')
provide('isEdit', isEdit)
// 当前身份权限
const editPermission = computed(() => {
  const roles = userStore.userInfo.roles
  return {
    // 项目经理 权限Key
    isProjectManager: roles.some(item => item.roleKey === 'projectManager'),
    // 市场经理
    isMarketManager: roles.some(item => item.roleKey === 'marketingManager'),
    // 经营管理部部门的人员
    isOperateManage: userStore.deptName === '经营管理部',
    // 综合办公室部门的人员
    isGeneralOffice: userStore.deptName === '综合办公室（董事会办公室）',
    // 财务人员
    isFinance: roles.some(item => item.roleKey === 'finance'),
  }
})
provide('editPermission', editPermission)

async function setValues() {
  return {
    // applicationInfo
    id,
    applicant: formData.applicationInfo.applicant,
    applicantDept: formData.applicationInfo.applicantDept,
    applyDate: formData.applicationInfo.applyDate,
    knotDate: formData.applicationInfo.knotDate,

    // projectBasicInfo
    projectName: formData.projectBasicInfo.projectName,
    projectCode: formData.projectBasicInfo.projectCode,
    projectManager: formData.projectBasicInfo.projectManager,
    projectManagerId: formData.projectBasicInfo.projectManagerId,
    marketingManager: formData.projectBasicInfo.marketingManager,
    marketingManagerId: formData.projectBasicInfo.marketingManagerId,
    projectLeader: formData.projectBasicInfo.projectLeader,
    projectLeaderId: formData.projectBasicInfo.projectLeaderId,
    deliveryEntity: formData.projectBasicInfo.deliveryEntity,
    deliveryEntityId: formData.projectBasicInfo.deliveryEntityId,
    // TODO:字段暂定
    projectStatus: formData.projectBasicInfo.projectStatus,
    acceptanceDate: formData.projectBasicInfo.acceptanceDate,
    knotType: formData.projectBasicInfo.knotType,
    abnormalClosing: formData.projectBasicInfo.abnormalClosing,
    additionalNotes: formData.projectBasicInfo.additionalNotes,
    appendix: formData.projectBasicInfo.attachments.length > 0
      ? formData.projectBasicInfo.attachments.map(
        item => (item.url ? item.url : item.response.data),
      )?.toString()
      : '',

    // projectManagerVerification
    contractCompleted: formData.projectManagerVerification.contractCompleted,
    procurementPaid: formData.projectManagerVerification.procurementPaid,
    reimbursementDone: formData.projectManagerVerification.reimbursementDone,
    docsUploaded: formData.projectManagerVerification.docsUploaded,
    paperArchived: formData.projectManagerVerification.paperArchived,

    // marketManagerVerification
    contractAllPaid: formData.marketManagerVerification.contractAllPaid,
    marketReimbursementDone: formData.marketManagerVerification.marketReimbursementDone,

    // documentReview
    electronicCheck: formData.documentReview.electronicCheck,
    paperArchive: formData.documentReview.paperArchive,

    // financialSettlement
    income: formData.financialSettlement.income,
    costProcurement: formData.financialSettlement.costProcurement,
    costLabor: formData.financialSettlement.costLabor,
    costManagement: formData.financialSettlement.costManagement,
    costPresale: formData.financialSettlement.costPresale,
    costDelivery: formData.financialSettlement.costDelivery,
    paymentReceived: formData.financialSettlement.paymentReceived,
    paymentPaid: formData.financialSettlement.paymentPaid,
    grossProfit: formData.financialSettlement.grossProfit,
    profitMargin: formData.financialSettlement.profitMargin,
  }
}

async function saveDraftData() {
  const data = await setValues()
  data.reviewStatus = 0
  data.processDefId = processDefId.value
  if (id) {
    await updateProjectKnot(id, data)
  } else {
    // 保存草稿
    await createProjectKnot(data)
  }
}

async function saveData() {
  const data = await setValues()
  data.reviewStatus = 1
  data.processDefId = processDefId.value
  if (id) {
    await updateProjectKnot(id, data)
  } else {
    console.log(data)
    // 提交
    await submitProjectKnot(data)
  }
}

defineExpose({
  onSave: saveData,
  onSaveDraft: saveDraftData,
  // 获取表单数据
  getFormData: () => {
    return readonly(formData)
  },
  // 审批流程
  saveFormData: async () => {
    try {
      // TODO:需要在此处调用保存接口，保存审批过程中特定人员修改的数据
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.main_container {
  flex: 1;
  overflow: auto;
  min-width: 1500px;
  padding: 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
