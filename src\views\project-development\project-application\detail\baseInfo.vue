<template>
  <Title>项目基本信息</Title>
  <ElDescriptions
    border
    :column="4">
    <ElDescriptionsItem
      label="项目名称"
      :span="2">
      {{ form.prdProjectName }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="项目编号"
      :span="1">
      {{ form.prdProjectCode }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发负责人"
      :span="1">
      {{ form.principal }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="申请日期">
      {{ form.submitTime }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="业务主体">
      {{ form.prdEntityDept }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分类">
      {{ form.prdProjectCategory }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="研发方式">
      {{ form.rdMethod }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分级">
      {{ form.projectClassification }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="立项日期">
      {{ form.projectInitiationDate }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分管领导">
      {{ form.projectLeader }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发项目基本情况"
      :span="4">
      {{ form.projectBasicInformation }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="附件"
      :span="4">
      <template v-if="form.attachments">
        <div
          v-for="(item, index) in form.attachments"
          :key="index"
          style="display: flex; align-items: center;">
          <ElIcon>
            <Document />
          </ElIcon>
          <span style="padding: 2px 0 0 2px;">
            {{ item.match(/[^/\\?#]+$/)[0] }}
          </span>
          <ElButton
            link
            type="primary"
            style="margin-left: 8px;"
            @click="handleDownload(item)">
            下载
          </ElButton>
        </div>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const form = defineModel()

// 自定义附件下载逻辑
function handleDownload(address) {
  const link = document.createElement('a')
  link.href = address
  // 设置下载文件名
  link.download = address.match(/[^/\\?#]+$/)[0]
  document.body.appendChild(link)
  link.click()
  // 清理 DOM
  document.body.removeChild(link)
}
</script>

<style lang="scss" scoped>

</style>
