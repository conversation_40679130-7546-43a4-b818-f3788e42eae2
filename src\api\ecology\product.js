import { get, post } from '@/utils/alova.js'
/**
 * 生态管理产品库接口
 */
const prefix = '/ecology/product'
/**
 * 产品列表
 */
export function getProductList(params) {
  return get(`${prefix}/list`, params)
}

/**
 * 产品详情
 */
export function getProductDetail(bid) {
  return get(`${prefix}/details?productBid=${bid}`)
}

/**
 * 添加产品
 */
export function addProduct(data, config = { transformRes: false }) {
  return post(`${prefix}/addProduct`, data, config)
}

/**
 * 修改产品
 */
export function updateProduct(data, config = { transformRes: false }) {
  return post(`${prefix}/updateProduct`, data, config)
}
