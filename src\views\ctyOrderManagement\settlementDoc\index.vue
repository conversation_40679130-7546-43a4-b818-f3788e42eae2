<template>
  <DefaultContainer v-loading="isLoading">
    <!-- 折叠面板组件，用于查询条件 -->
    <Collapse>
      <template #header />
      <!-- 查询表单 -->
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="140px"
        label-position="left">
        <ElRow :gutter="20">
          <ElCol :span="8">
            <ElFormItem
              prop="ctAppName"
              label="应用系统名称">
              <ElInput
                v-model="formData.ctAppName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="year"
              label="结算年度">
              <ElDatePicker
                v-model="formData.year"
                type="year"
                style="width: 100%"
                value-format="YYYY"
                placeholder="请选择结算年度" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="deptName"
              label="订单部门">
              <ElSelect
                v-model="formData.deptName"
                placeholder="请选择"
                @visible-change="
                  (change) => change && (orgPickerRef.show())
                " />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="statusList"
              label="状态">
              <ElSelect
                v-model="formData.statusList"
                placeholder="请选择"
                multiple
                clearable
                style="width: 100%">
                <ElOption
                  label="草稿"
                  :value="0" />
                <ElOption
                  label="待审批"
                  :value="1" />
                <ElOption
                  label="进行中"
                  :value="2" />
                <ElOption
                  label="已完成"
                  :value="3" />
                <ElOption
                  label="已拒绝"
                  :value="4" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="statementName"
              label="结算单名称">
              <ElInput
                v-model="formData.statementName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="totalAmount"
              label="结算总金额(元)">
              <NumberRange
                v-model="formData.totalAmount"
                :precision="2"
                style="width: 100%" />
            </ElFormItem>
            <!-- totalAmountMin totalAmountMax -->
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="chargeDateStart"
              label="结算开始计费日期">
              <ElDatePicker
                v-model="formData.chargeDateStart"
                clearable
                type="datetimerange"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="chargeDateEnd"
              label="结算结束计费日期">
              <ElDatePicker
                v-model="formData.chargeDateEnd"
                clearable
                type="datetimerange"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </Collapse>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
      <ElButton
        v-hasPermi="['settlementDoc:add']"
        type="primary"
        @click="handle('add')">
        新建
      </ElButton>
      <ElButton
        type="primary"
        @click="handleExport">
        导出
      </ElButton>
    </div>
    <!-- 数据表格 -->
    <ElTable
      border
      :data="tableData"
      style="margin-top: 20px"
      @row-dblclick="onRowDbClick">
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        width="60"
        fixed="left"
        align="center" />
      <ElTableColumn
        prop="statusName"
        label="状态"
        width="80" />
      <ElTableColumn
        prop="year"
        label="结算年度"
        width="80" />
      <ElTableColumn
        prop="deptName"
        label="订单部门"
        min-width="100" />
      <ElTableColumn
        prop="formNo"
        label="结算单编号"
        min-width="100" />
      <ElTableColumn
        prop="statementName"
        label="结算单名称"
        min-width="100" />
      <ElTableColumn
        prop="customerName"
        label="客户名称"
        min-width="100" />
      <ElTableColumn
        prop="chargeDateStart"
        label="结算开始计费日期"
        min-width="130" />
      <ElTableColumn
        prop="chargeDateEnd"
        label="结算结束计费日期"
        min-width="130" />
      <ElTableColumn
        prop="statementCycle"
        label="结算周期(天)"
        min-width="120" />
      <ElTableColumn
        prop="totalAmount"
        label="结算总金额(元)"
        min-width="120" />
      <ElTableColumn
        prop="handledByName"
        label="经办人"
        min-width="120" />
      <ElTableColumn
        label="操作"
        width="110"
        fixed="right">
        <template #default="scope">
          <ElButton
            v-if="scope.row.status === 0"
            v-hasPermi="['settlementDoc:edit']"
            type="primary"
            link
            @click="handle('edit', scope.row)">
            编辑
          </ElButton>
          <ElPopconfirm
            title="确定删除吗？"
            @confirm="handle('delete', scope.row)">
            <template #reference>
              <ElButton
                v-if="scope.row.status === 0"
                v-hasPermi="['settlementDoc:delete']"
                type="primary"
                link>
                删除
              </ElButton>
            </template>
          </ElPopconfirm>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onSearch" />
    </div>
    <OrgPicker
      ref="orgPickerRef"
      type="dept"
      :multiple="false"
      title="选择订单部门"
      @ok="handleSelectedDepartment" />
  </DefaultContainer>
</template>

<script setup>
import { deleteSettlementDoc, getSettlementDocPageList } from '@/api/ctyOrderManagement/settlementDoc.js'
import Collapse from '@/components/Collapse/index.vue'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import NumberRange from '@/components/NumberRange/index.vue'
import { usePagination } from '@/utils/hooks.js'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
/**
 * 分页相关
 * pagination: 分页参数对象
 * indexMethod: 表格序号计算方法
 */
const { pagination, indexMethod } = usePagination()

const router = useRouter()

/**
 * 表单数据与引用
 */
let formData = reactive({
  totalAmount: [null, null],
}) // 查询表单数据
const formRef = ref() // 表单引用，用于重置

/**
 * 表格数据与加载状态
 */
const tableData = ref([]) // 表格数据
const loadingIndex = ref(0) // 加载计数器，用于处理多个并发请求的loading状态

/**
 * 计算属性：是否显示加载状态
 * 当loadingIndex > 0时显示加载状态
 */
const isLoading = computed(() => {
  return loadingIndex.value !== 0
})

/**
 * 生命周期钩子：组件挂载完成后初始化
 */
onMounted(() => {
  init()
})

function getParams(isList) {
  const params = { ...formData }
  if (formData.chargeDateStart) {
    params.chargeDateStartStart = formData.chargeDateStart[0]
    params.chargeDateStartEnd = formData.chargeDateStart[1]
    delete params.chargeDateStart
  }
  if (formData.chargeDateEnd) {
    params.chargeDateEndStart = formData.chargeDateEnd[0]
    params.chargeDateEndEnd = formData.chargeDateEnd[1]
    delete params.chargeDateEnd
  }
  if (formData.totalAmount && formData.totalAmount[0] !== null && formData.totalAmount[1] !== null) {
    params.totalAmountMin = formData.totalAmount[0]
    params.totalAmountMax = formData.totalAmount[1]
    if (params.totalAmountMax < params.totalAmountMin) {
      delete params.totalAmountMax
    }
  }
  delete params.useCycle

  delete params.discountedPrice
  if (!isList) {
    if (formData.statusList) {
      params.statusList = formData.statusList.join(',')
    }
    return params
  } else {
    const searchData = {
      page: {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
      },
      params,
    }
    return searchData
  }
}

/**
 * 获取应用系统信息分页列表
 */
function getPage() {
  loadingIndex.value++ // 开始加载，计数器加1
  const searchData = getParams(true)
  getSettlementDocPageList(searchData)
    .then((res) => {
      console.log(res, '---- getSettlementDocPageList')
      tableData.value = res.data.list
      pagination.total = res.data.total
      loadingIndex.value-- // 加载完成，计数器减1
    })
    .catch((err) => {
      console.log(err, '---- getSettlementDocPageList')
      loadingIndex.value-- // 加载出错，计数器减1
    })
}

/**
 * 初始化方法：组件挂载后加载应用系统列表数据
 */
function init() {
  getPage()
}

/**
 * 表格行双击事件处理
 * @param {object} row - 行数据
 */
function onRowDbClick(row) {
  router.push({
    path: '/ctyOrderManagement/settlementDoc/details',
    query: {
      id: row.id,
    },
  })
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getPage()
}

/**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  getPage()
}

/**
 * 重置方法
 * 重置表单并查询第一页数据
 */
function onReset() {
  formData = {
    totalAmount: [null, null],
  }
  formRef.value.resetFields() // 重置表单字段
  pagination.pageNum = 1 // 重置到第一页
  onSearch() // 执行查询
}

/**
 * 操作方法
 * @param {string} type - 操作类型：add-新增应用系统，edit-编辑应用系统，delete-删除应用系统
 * @param {object} row - 行数据对象，包含应用系统信息
 */
function handle(type, row) {
  console.log(type, row, '---- handle')
  switch (type) {
    case 'add':
      console.log('新建应用系统')
      router.push({
        path: '/ctyOrderManagement/settlementDoc/form',
        query: {
          type: 'add',
        },
      })
      break
    case 'edit':
      console.log('编辑应用系统')
      router.push({
        path: '/ctyOrderManagement/settlementDoc/form',
        query: {
          id: row.id,
          type: 'edit',
        },
      })
      break
    case 'delete':
      console.log('删除应用系统')
      deleteSettlementDoc({
        id: row.id,
      })
        .then((res) => {
          console.log(res, '---- deleteSettlementDoc')
          if (res.code === 200) {
            ElMessage.success('删除成功')
            getPage()
          } else {
            ElMessage.warning(res.msg)
          }
        })
      break
    default:
      console.log('未知操作类型')
      break
  }
}

const orgPickerRef = ref()
function handleSelectedDepartment(rows) {
  if (rows.length > 0) {
    formData.deptName = rows[0].name
    formData.deptId = rows[0].id
  } else {
    formData.deptName = ''
    formData.deptId = ''
  }
}

const { proxy } = getCurrentInstance()
function handleExport() {
  const params = getParams(false)
  proxy.download(
    '/cloudorder/cloud_ct_statement/download',
    {
      ...params,
    },
    `结算单_${new Date().getTime()}.xlsx`,
  )
}
</script>

<style lang="scss" scoped>
/* 操作区域样式 */
.operation-area {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
