<template>
  <div class="key_task">
    <VChart
      :option="option"
      autoresize />
  </div>
</template>

<script setup>
const option = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    data: [{
      name: '任务数量',
      icon: 'circle',
    }, '完成率'],
  },
  grid: {
    top: 32,
    right: 0,
    bottom: 0,
    left: 0,
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: ['科技创新', '数创云建设', '数字化建设', '科技创新'],
      axisPointer: {
        type: 'shadow',
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#C1C5CC',
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}%',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
  ],
  series: [
    {
      name: '任务数量',
      color: '#165DFF',
      type: 'bar',
      barWidth: 52,
      tooltip: {
        valueFormatter(value) {
          return `${value}个`
        },
      },
      data: [1, 2, 3, 4],
    },
    {
      name: '完成率',
      color: '#14C9C9',
      type: 'line',
      yAxisIndex: 1,
      tooltip: {
        valueFormatter(value) {
          return `${value}%`
        },
      },
      data: [10, 20, 30, 40],
    },
  ],
})
</script>

<style lang="scss" scoped>
.key_task {
  height: 342px;
  margin-bottom: 18px;
}
</style>
