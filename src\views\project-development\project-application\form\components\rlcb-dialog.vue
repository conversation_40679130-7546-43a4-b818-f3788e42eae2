<template>
  <ElDialog
    v-model="model"
    @open="onOpen">
    <template #header>
      <Title is-hidden>
        人力成本
      </Title>
    </template>
    <ElForm
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120"
      label-position="left">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem
            prop="teamRole"
            label="团队角色">
            <ElInput
              v-model="form.teamRole"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="personType"
            label="人员类型">
            <ElSelect
              v-model="form.personType"
              placeholder="请选择">
              <ElOption
                v-for="item in person_type"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="personNumber"
            label="人数">
            <ElInput
              v-model.number="form.personNumber"
              placeholder="请输入"
              type="number" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="unitPrice"
            label="单价(元/人天)">
            <ElInput
              v-model.number="form.unitPrice"
              placeholder="请输入"
              type="number" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="totalPrice"
            label="总价(元)">
            <ElInput
              v-model="form.totalPrice"
              placeholder="请输入"
              disabled />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="estimateHumanDays"
            label-width="160"
            label="人员投入估算(人天)">
            <ElInput
              v-model.number="form.estimateHumanDays"
              placeholder="请输入"
              type="number" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="remark"
            label="备注">
            <ElInput
              v-model="form.remark"
              type="textarea"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              :rows="2" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton @click="onCancel">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="onConfirm">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import Title from '@/components/Title'
import { isEmpty } from '@/utils/hooks'
import mathjs, { isNumber } from '@/utils/math'
import regexp from '@/utils/regexp'
import { ElButton, ElCol, ElDialog, ElForm, ElFormItem, ElInput, ElRow } from 'element-plus'

const { info } = defineProps({
  info: {
    type: [Object, null],
    default: null,
  },
})
const emit = defineEmits(['confirm', 'cancel'])
const { proxy } = getCurrentInstance()
const { person_type } = proxy.useDict('person_type')
const rules = reactive({
  teamRole: [
    { required: true, message: '请输入团队角色', trigger: ['blur', 'change'] },
  ],
  personNumber: [
    { required: true, message: '请输入人数', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的人数', trigger: ['blur', 'change'] },
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  totalPrice: [
    { required: true, message: '请输入总价', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  personType: [
    { required: true, message: '请选择人员类型', trigger: 'change' },
  ],
  estimateHumanDays: [
    { required: true, message: '人员投入估算(人天)', trigger: 'blur' },
    { pattern: regexp.naturalNumber, message: '请输入正确的人员投入估算', trigger: ['blur', 'change'] },
  ],
})
const formRef = useTemplateRef('formRef', 'cancel')
const form = ref({
  teamRole: '',
  personType: '',
  personNumber: '',
  unitPrice: '',
  totalPrice: '',
  remark: '',
  estimateHumanDays: '',
})
const model = defineModel()
function onCancel() {
  formRef.value.resetFields()
  emit('cancel')
  model.value = false
}
function onConfirm() {
  formRef.value.validate().then(() => {
    emit('confirm', {
      formData: { ...form.value },
      dialogType: 'rlcb',
    })
    onCancel()
  })
}
function onOpen() {
  if (!isEmpty(info)) {
    form.value = { ...info }
  } else {
    delete form.value.uuid
    delete form.value.id
  }
}
watchEffect(() => {
  if (isNumber(form.value.estimateHumanDays) && isNumber(form.value.unitPrice)) {
    form.value.totalPrice = mathjs.multiply(form.value.estimateHumanDays, form.value.unitPrice)
  } else {
    form.value.totalPrice = '请输入正确的单价和人员投入估算'
  }
})
</script>

<style lang="scss" scoped></style>
