import request from '@/utils/request'

// 获取发票冲红申请信息分页列表
export function getInvoiceRedApplyPageList(params) {
  return request({
    url: `/finance/invoice-red-apply/pageList`,
    method: 'post',
    data: params,
  })
}

// 获取发票冲红申请信息详情
export function getInvoiceRedApplyDetail(params) {
  return request({
    url: `/finance/invoice-red-apply/detail`,
    method: 'get',
    params,
  })
}

// 暂存、更新发票冲红
export function createOrUpdateInvoiceRed(data) {
  return request({
    url: `/finance/invoice-red-apply/createOrUpdate`,
    method: 'post',
    data,
  })
}

// 删除发票冲红
export function deleteInvoiceRed(params) {
  return request({
    url: `/finance/invoice-red-apply/del`,
    method: 'get',
    params,
  })
}

// 提交发票冲红
export function submitInvoiceApply(data) {
  return request({
    url: `/finance/invoice-red-apply/submit`,
    method: 'post',
    data,
  })
}

// 税务专员回传发票信息提交
export function submitInvoiceLedger(data) {
  return request({
    url: `/finance/invoice-red-apply/submit-with-ledger`,
    method: 'post',
    data,
  })
}
