import { get, post, remove } from '@/utils/alova'

/**
 * 获取验收申请分页列表
 */
export function getAcceptanceApplicationPageList(data, config) {
  return post('/project/prd/project/checkAccept/pageList', data, config)
}

/**
 * 删除验收申请
 */
export function deleteAcceptanceApplication(checkAcceptBid, config) {
  return remove(`/project/prd/project/checkAccept/deleted/${checkAcceptBid}`, null, config)
}

/**
 * 根据编号获取验收申请详情
 */
export function getAcceptanceApplicationDetailByCode(prdProjectCode, config) {
  return get(`/project/prd/project/checkAccept/startDetail/${prdProjectCode}`, null, config)
}

/**
 * 根据bid获取验收申请详情
 */
export function getAcceptanceApplicationDetailByBid(checkAcceptBid, config) {
  return get(`/project/prd/project/checkAccept/queryDetail/${checkAcceptBid}`, null, config)
}

/**
 * 发起验收申请
 */
export function startAcceptanceApplication(data, config = { transformRes: false }) {
  return post('/project/prd/project/checkAccept/start', data, config)
}

/**
 * 获取销售合同列表
 */
export function getPsmContractList(params, config) {
  return get('/purchase/psmContract/list', params, config)
}

/**
 * 获取销售合同详情（取对方主体列表）
 */
export function getPsmContractDetail(params, config) {
  return get('/purchase/psmContract/detail', params, config)
}

/**
 * 获取印章列表
 */
export function getSealList(data) {
  return get('/system/seal/list', data)
}
