<template>
  <DefaultContainer v-loading="loading">
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          续费资源工单
        </div>
      </div>
      <div
        v-if="!props.id"
        class="right">
        <ElButton @click="router.back()">
          取消
        </ElButton>
        <ElButton
          :loading="saveLoading"
          plain
          type="primary"
          @click="onSubmit(false)">
          暂存
        </ElButton>
        <ElButton
          :loading="saveLoading"
          type="primary"
          @click="onSubmit(true)">
          提交
        </ElButton>
      </div>
    </div>
    <div class="content">
      <ElForm
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="150px"
        :disabled="props.id !== undefined">
        <ElDivider />
        <div class="sub-title">
          楚天云报价信息
        </div>
        <ElFormItem
          class="form-item"
          label="年度"
          prop="year">
          <ElDatePicker
            v-model="form.year"
            value-format="YYYY"
            type="year"
            placeholder="请选择年度" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单部门"
          prop="deptId">
          <ElSelect
            v-model="form.deptName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef2.show())
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="经办人"
          prop="handledBy">
          <ElSelect
            v-model="form.handledByName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef.show())
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="应用系统名称"
          prop="ctAppId"
          style="width: 95%">
          <ElSelect
            v-model="form.ctAppName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectApplicationModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="客户名称"
          prop="customerCode">
          <ElInput
            v-model="form.customerName"
            disabled
            placeholder="选择应用系统后带入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="报价类型"
          prop="quotationType">
          <ElInput
            v-model="form.quotationType"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="续订开始日期"
          prop="continueStartTime">
          <ElDatePicker
            v-model="form.continueStartTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledStartDate"
            placeholder="请选择" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="续订结束日期"
          prop="continueEndTime">
          <ElDatePicker
            v-model="form.continueEndTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledEndDate"
            placeholder="请选择" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="使用周期(天)"
          prop="useCycle">
          <ElInput
            v-model="form.useCycle"
            disabled
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="打折前原总价(元)"
          prop="originalPrice">
          <ElInputNumber
            v-model="form.originalPrice"
            :precision="2"
            controls-position="right"
            style="width: 100%"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="打折后总报价(元)"
          prop="discountedPrice">
          <ElInputNumber
            v-model="form.discountedPrice"
            :precision="2"
            controls-position="right"
            style="width: 100%"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="折扣金额(元)"
          prop="discount">
          <ElInput
            v-model="form.discount"
            disabled
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="综合折扣比例"
          prop="discountRatio">
          <ElInput
            v-model="form.discountRatio"
            disabled
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="报价人"
          prop="bidder">
          <ElSelect
            v-model="form.bidderName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef3.show())
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="报价日期"
          prop="quotationDate">
          <ElDatePicker
            v-model="form.quotationDate"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="项目名称"
          style="width: 95%"
          prop="originalProjectName">
          <ElSelect
            v-model="form.originalProjectName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectProjectModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="项目编号"
          prop="originalProjectCode">
          <ElInput
            v-model="form.originalProjectCode"
            disabled
            placeholder="选择项目后自动填入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="数产项目经理"
          prop="originalProjectManager">
          <ElSelect
            v-model="form.originalProjectManagerName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef4.show())
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="备注"
          prop="remark"
          style="width: 95%">
          <ElInput
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注" />
        </ElFormItem>

        <div
          class="attachment_wrapper"
          style="margin-bottom: 10px">
          <ElUpload
            ref="uploadRef"
            v-model:file-list="fileList"
            class="custom_upload"
            :action="uploadUrl"
            auto-upload
            :on-success="onFileUploadSuccess"
            :on-error="onFileUploadFail"
            multiple
            :on-preview="handlePreview"
            style="padding: 0 20px">
            <template #trigger>
              <ElButton
                link
                icon="link">
                <span style="color: red">* </span>续费订单附件
              </ElButton>
            </template>
            <ElTooltip
              class="box-item"
              effect="dark"
              content="附上明细报价(如果实际报价人和系统上填的报价人不是同一人，则需要实际经手人员签字)"
              placement="top">
              <ElIcon style="font-size: 14px;">
                <InfoFilled />
              </ElIcon>
            </ElTooltip>
            <ElButton
              type="primary"
              style="float: right"
              @click="selectFile()">
              上传
            </ElButton>
          </ElUpload>
        </div>
      </ElForm>
      <div
        v-if="isProjectManagerChecks.isShow">
        <ElForm
          ref="formRef2"
          :inline="true"
          :model="form"
          :rules="rules2"
          label-position="left"
          :disabled="!isProjectManagerChecks.isEdit"
          label-width="150px">
          <ElDivider />
          <div
            class="sub-title">
            项目经理核对
          </div>
          <ElFormItem
            class="form-item"
            label="项目阶段"
            prop="projectPhase">
            <ElSelect
              v-model="form.projectPhase"
              placeholder="请选择"
              @change="onChangeProjectPhase">
              <ElOption
                label="预立项阶段"
                value="预立项阶段" />
              <ElOption
                label="交付阶段"
                value="交付阶段" />
            </ElSelect>
          </ElFormItem>
          <br>
          <ElFormItem
            class="form-item"
            label="项目名称"
            prop="projectName">
            <ElSelect
              v-model="form.projectName"
              placeholder="请选择"
              @visible-change="
                (change) => change && (modelIsShow.selectProjectModal = true)
              " />
          </ElFormItem>
          <br>
          <ElFormItem
            class="form-item"
            label="项目编号"
            prop="projectCode">
            <ElInput
              v-model="form.projectCode"
              disabled
              placeholder="选择项目名称后带入" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="项目所属部门"
            prop="projectDeptId">
            <ElInput
              v-model="form.projectDeptName"
              disabled
              placeholder="选择项目名称后带入" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="数产项目经理"
            prop="projectManager">
            <ElInput
              v-model="form.projectManagerName"
              disabled
              placeholder="选择项目名称后带入" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="数产市场经理"
            prop="marketManager">
            <ElSelect
              v-model="form.marketManagerName"
              placeholder="请选择"
              @visible-change="
                (change) => change && (orgPickerRef5.show())
              " />
          </ElFormItem>
          <br>
          <ElFormItem
            class="form-item"
            label="项目分管领导"
            prop="projectLeader">
            <ElInput
              v-model="form.projectLeaderName"
              disabled
              placeholder="选择项目名称后带入" />
          </ElFormItem>
          <br>
          <ElFormItem
            class="form-item"
            label="项目经理核对备注"
            prop="projectManagerRemark"
            style="width: 95%">
            <ElInput
              v-model="form.projectManagerRemark"
              type="textarea"
              :rows="2"
              placeholder="若未签订合同需通知市场经理签订合同" />
          </ElFormItem>
        </ElForm>
      </div>
      <div
        v-if="isMarketManagerChecks.isShow">
        <ElForm
          ref="formRef3"
          :inline="true"
          :model="form"
          :rules="rules3"
          label-position="left"
          :disabled="!isMarketManagerChecks.isEdit"
          label-width="150px">
          <ElDivider />
          <div
            class="sub-title">
            市场经理核对
          </div>
          <ElFormItem
            class="form-item"
            label="客户意见"
            prop="customerComment">
            <ElSelect
              v-model="form.customerComment"
              placeholder="请选择客户意见">
              <ElOption
                label="同意价格"
                value="同意价格" />
              <ElOption
                label="不同意价格"
                value="不同意价格" />
              <ElOption
                label="进一步协商"
                value="进一步协商" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="新签云服务销售合同"
            prop="cloudServiceSalesContract">
            <ElSelect
              v-model="form.cloudServiceSalesContract"
              placeholder="请选择"
              @visible-change="
                (change) => change && (modelIsShow.contractModal = true)
              " />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="市场经理核对备注"
            prop="marketManagerRemark"
            style="width: 95%">
            <ElInput
              v-model="form.marketManagerRemark"
              type="textarea"
              :rows="2"
              placeholder="填写客户给出的具体意见和建议、风险、问题等" />
          </ElFormItem>
        </ElForm>
      </div>
      <div
        v-if="isCloudServiceChecks.isShow">
        <ElForm
          ref="formRef4"
          :inline="true"
          :model="form"
          :rules="rules4"
          label-position="left"
          :disabled="!isCloudServiceChecks.isEdit"
          label-width="150px">
          <ElDivider />
          <div
            class="sub-title">
            楚天云服务处理信息
          </div>
          <ElFormItem
            class="form-item"
            label="续费订单号"
            prop="formNo">
            <ElInput
              v-model="form.formNo"
              disabled
              placeholder="自动生成" />
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="续费单状态"
            prop="continueOrderStatus">
            <ElSelect
              v-model="form.continueOrderStatus"
              placeholder="续费单状态">
              <ElOption
                label="已接单"
                value="已接单" />
              <ElOption
                label="重新议价"
                value="重新议价" />
              <ElOption
                label="拒接单"
                value="拒接单" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem
            class="form-item"
            label="楚天云服务处理备注"
            prop="ctRemark">
            <ElInput
              v-model="form.ctRemark"
              type="textarea"
              :rows="2"
              placeholder="续订情况备注" />
          </ElFormItem>
        </ElForm>
      </div>
    </div>
    <SelectProjectModal
      v-model="modelIsShow.selectProjectModal"
      :project-phase="form.projectPhase"
      @select-item="onChangeProject" />
    <ApplicationSelectModal
      v-model="modelIsShow.selectApplicationModal"
      @select-item="onChangeApplication" />
    <OrgPicker
      ref="orgPickerRef"
      type="user"
      :multiple="false"
      title="选择经办人"
      @ok="handleSelectedHandledBy" />
    <OrgPicker
      ref="orgPickerRef2"
      type="dept"
      :multiple="false"
      title="选择订单部门"
      @ok="handleSelectedDepartment" />
    <OrgPicker
      ref="orgPickerRef3"
      type="user"
      :multiple="false"
      title="选择报价人"
      @ok="handleSelectedBidder" />
    <OrgPicker
      ref="orgPickerRef4"
      type="user"
      :multiple="false"
      title="选择数产项目经理"
      @ok="handleSelectedProjectManager" />
    <OrgPicker
      ref="orgPickerRef5"
      type="user"
      :multiple="false"
      title="选择数产市场经理"
      @ok="handleSelectedMarketManager" />
    <SelectContractModal
      v-model="modelIsShow.contractModal"
      @select-item="onChangeContract" />
  </DefaultContainer>
</template>

<script setup>
import {
  createOrUpdateRenewalResourceWorkOrderProcess,
  getRenewalResourceWorkOrderProcessDetail,
  submitRenewalResourceWorkOrderProcess,
  submitRenewalResourceWorkOrderProcessWithApprove,
} from '@/api/ctyOrderManagement/renewalResourceWorkOrderProcess.js'
import { getCurrentProcessId } from '@/api/wflow-pro'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import useUserStore from '@/store/modules/user.js'
import { deepClone } from '@/utils'
import ApplicationSelectModal from '@/views/ctyOrderManagement/modules/ApplicationSelectModal.vue'
import SelectContractModal from '@/views/financialManagement/modules/SelectContractModal.vue'
import SelectProjectModal from '@/views/financialManagement/modules/SelectProjectModal.vue'
import { Back, InfoFilled } from '@element-plus/icons-vue'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { dayjs, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
  instanceId: {
    type: String,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  forms: {
    type: Object,
    default: () => ({}),
  },
})

const router = useRouter()
const formRef = ref(null)
const form = ref({
  quotationType: '续订',
})

const modelIsShow = reactive({
  selectProjectModal: false,
  contractModal: false,
  selectApplicationModal: false,
  selectContractModal: false,
})

const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址
const fileList = ref([])

// 监听服务计划开始和结束时间的变化
watch(
  () => [form.value.continueStartTime, form.value.continueEndTime],
  ([start, end]) => {
    if (start && end) {
      const cycle = dayjs(end).diff(dayjs(start), 'day')
      form.value.useCycle = cycle || 0
    } else {
      form.value.useCycle = 0
    }
  },
)
watch(
  () => [form.value.originalPrice, form.value.discountedPrice],
  ([originalPrice, discountedPrice]) => {
    if (originalPrice && discountedPrice) {
      form.value.discount = originalPrice - discountedPrice
      form.value.discountRatio = Number((form.value.discount / originalPrice).toFixed(2))
    } else {
      form.value.discount = 0
      form.value.discountRatio = 0
    }
  },
)
const rules = ref({
  year: [
    { required: true, message: '请选择年度', trigger: 'change' },
  ],
  deptId: [
    { required: true, message: '请选择订单部门', trigger: 'change' },
  ],
  handledBy: [
    { required: true, message: '请选择经办人', trigger: 'change' },
  ],

  ctAppId: [
    { required: true, message: '请选择应用系统名称', trigger: 'change' },
  ],
  customerCode: [
    { required: true, message: '请选择客户名称', trigger: 'change' },
  ],
  continueStartTime: [
    { required: true, message: '请选择续订开始日期', trigger: 'change' },
  ],
  continueEndTime: [
    { required: true, message: '请选择续订结束日期', trigger: 'change' },
  ],
  useCycle: [
    { required: true, message: '请选择使用周期', trigger: 'change' },
  ],
  originalProjectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' },
  ],
  originalPrice: [
    { required: true, message: '请输入打折前原总价', trigger: 'change' },
  ],
  discountedPrice: [
    { required: true, message: '请输入打折后总报价', trigger: 'change' },
  ],
  bidderId: [
    { required: true, message: '请选择报价人', trigger: 'change' },
  ],
  originalProjectManager: [
    { required: true, message: '请选择数产项目经理', trigger: 'change' },
  ],

})

const rules2 = ref({
  projectPhase: [
    { required: true, message: '请选择项目阶段', trigger: 'change' },
  ],
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' },
  ],
  projectCode: [
    { required: true, message: '请选择项目编号', trigger: 'change' },
  ],
  marketManagerName: [
    { required: true, message: '请选择数产市场经理', trigger: 'change' },
  ],
  projectManager: [
    { required: true, message: '请选择数产项目经理', trigger: 'change' },
  ],
})

const rules3 = ref({
  customerComment: [
    { required: true, message: '请选择客户意见', trigger: 'change' },
  ],
  cloudServiceSalesContract: [
    { required: true, message: '请选择新签云服务销售合同', trigger: 'change' },
  ],
})

const rules4 = ref({
  continueOrderStatus: [
    { required: true, message: '请选择续费单状态', trigger: 'change' },
  ],
})

const id = ref('')
const loading = ref(false)
const saveLoading = ref(false)

onMounted(() => {
  if (props.id || router.currentRoute.value.query.id) {
    id.value = props.id || router.currentRoute.value.query.id
    getFormData()
    console.log(props.forms, '--- props.forms')
    console.log(props.config, '--- props.config')
  } else {
    form.value.bidderName = useUserStore().userInfo.nickName
    form.value.bidder = useUserStore().userInfo.userId
    form.value.quotationDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
  }
})

const isProjectManagerChecks = ref({
  isShow: false,
  isEdit: false,
})
const isMarketManagerChecks = ref({
  isShow: false,
  isEdit: false,
})
const isCloudServiceChecks = ref({
  isShow: false,
  isEdit: false,
})

async function getFormData() {
  loading.value = true
  try {
    const res = await getRenewalResourceWorkOrderProcessDetail({
      id: id.value,
    })
    console.log(res)
    form.value = deepClone(res.data)
    if (form.value.attachment) {
      try {
        let parsedArr = JSON.parse(form.value.attachment)
        parsedArr = parsedArr.map(item => ({
          name: item.match(/[^/\\?#]+$/)[0],
          url: item,
        }))
        fileList.value = parsedArr
      } catch (error) {
        console.log(error)
      }
    }
    if (props.forms[0].propName === 'originalProjectManager' && props.forms[0].perm === 'E') {
      isProjectManagerChecks.value.isShow = true
      isProjectManagerChecks.value.isEdit = true
      form.value.projectPhase = '交付阶段'
    }
    if (props.forms[1].propName === 'marketManager' && props.forms[1].perm === 'E') {
      isProjectManagerChecks.value.isShow = true
      isMarketManagerChecks.value.isShow = true
      isMarketManagerChecks.value.isEdit = true
    }
    if (props.forms[2].propName === 'cty' && props.forms[2].perm === 'E') {
      isProjectManagerChecks.value.isShow = true
      isMarketManagerChecks.value.isShow = true
      isCloudServiceChecks.value.isShow = true
      isCloudServiceChecks.value.isEdit = true
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

function onChangeProject(row) {
  if (isProjectManagerChecks.value.isEdit) {
    console.log(row, '--- row')
    form.value.projectName = row.projectName || row.bolProjectName
    form.value.projectCode = row.projectCode
    form.value.projectManagerName = row.projectManager
    form.value.projectManager = row.projectManagerId

    if (form.value.projectPhase === '交付阶段') {
    // 分管领导
      form.value.projectLeaderName = row.projectLeader
      form.value.projectLeader = row.projectLeaderId
      // 市场经理
      form.value.marketManagerName = row.marketingManager
      form.value.marketManager = row.marketingManagerId
      // 部门
      form.value.projectDeptName = row.deliveryEntity
      form.value.projectDeptId = row.deliveryEntityId
    } else {
    // 分管领导
      form.value.projectLeaderName = row.inChargeManager
      form.value.projectLeader = row.inChargeManagerId
      // 市场经理
      form.value.marketManagerName = row.markManager
      form.value.marketManager = row.markManagerId
      // 部门
      form.value.projectDeptName = row.busBelongDept
      form.value.projectDeptId = row.busBelongDeptId
    }
  } else {
    form.value.originalProjectName = row.projectName || row.bolProjectName
    form.value.originalProjectCode = row.projectCode
  }
}

function onChangeApplication(row) {
  console.log(row, '--- row')
  form.value.ctAppName = row.appName
  form.value.ctAppId = row.id
  form.value.customerName = row.customerName
  form.value.customerCode = row.customerCode
}

function onChangeContract(row) {
  console.log(row, '--- row')
  // 合同
  if (row) {
    form.value.cloudServiceSalesContract = row.name
    form.value.cloudServiceSalesContractId = row.id
  } else {
    form.value.cloudServiceSalesContract = ''
    form.value.cloudServiceSalesContractId = ''
  }
}

function disabledStartDate(time) {
  if (form.value.continueEndTime) {
    const endTime = new Date(form.value.continueEndTime).getTime()
    const currentTime = time.getTime()
    // 不能选择开始日期当天及之前的日期
    return currentTime >= endTime
  }
  return false
}

function disabledEndDate(time) {
  if (form.value.continueStartTime) {
    const startTime = new Date(form.value.continueStartTime).getTime()
    const currentTime = time.getTime()
    // 不能选择开始日期当天及之前的日期
    return currentTime <= startTime
  }
  return false
}

const uploadRef = useTemplateRef('uploadRef')
function selectFile() {
  uploadRef.value.$el.querySelector('input').click()
}

function handlePreview(file) {
  if (file?.url) {
    window.open(file.url)
  }
  if (file?.response?.data) {
    window.open(file.response.data)
  }
}

function onFileUploadSuccess() {
  ElMessage.success('附件上传成功')
}

function onFileUploadFail() {
  ElMessage.error('附件上传失败')
}

async function onSubmit(isSubmit) {
  let valid = true
  if (isSubmit) {
    if (fileList.value.length === 0) {
      ElMessage.error('请上传续费订单附件')
      return
    }
    valid = await formRef.value.validate()
  }
  if (valid) {
    const data = getData()

    saveLoading.value = true
    try {
      if (isSubmit) {
        data.processDefId = await getCurrentProcessId('renewal-resource-work-order')
        console.log(data.processDefId, '--- data.processDefId')
        //  提交
        const res = await submitRenewalResourceWorkOrderProcess(data)
        if (res.code === 200) {
          ElMessage.success('提交成功')
          router.back()
        } else {
          ElMessage.error('提交失败')
        }
      } else {
        // 暂存
        const res = await createOrUpdateRenewalResourceWorkOrderProcess(data)
        if (res.code === 200) {
          ElMessage.success('暂存成功')
          router.back()
        } else {
          ElMessage.error('暂存失败')
        }
      }
    } finally {
      saveLoading.value = false
    }
  }
}

function getData() {
  const data = deepClone(form.value)
  if (fileList.value.length > 0) {
    const addressArr = fileList.value
      .map(item => item.url || item.response.data)
      .filter(address => address)
    if (addressArr.length > 0) {
      data.attachment = JSON.stringify(addressArr)
    } else {
      data.attachment = ''
    }
  } else {
    data.attachment = ''
  }
  console.log(data, '--- data')
  return data
}

const orgPickerRef = ref()
function handleSelectedHandledBy(val) {
  console.log(val, '--- val')
  if (val && val.length > 0) {
    form.value.handledByName = val[0].name
    form.value.handledBy = val[0].id
  } else {
    form.value.handledByName = ''
    form.value.handledBy = ''
  }
}

const orgPickerRef2 = ref()
function handleSelectedDepartment(rows) {
  if (rows.length > 0) {
    form.value.deptName = rows[0].name
    form.value.deptId = rows[0].id
  } else {
    form.value.deptName = ''
    form.value.deptId = ''
  }
}

const orgPickerRef3 = ref()
function handleSelectedBidder(val) {
  if (val && val.length > 0) {
    form.value.bidderName = val[0].name
    form.value.bidder = val[0].id
  } else {
    form.value.bidderName = ''
    form.value.bidder = ''
  }
}

const orgPickerRef4 = ref()
function handleSelectedProjectManager(val) {
  if (val && val.length > 0) {
    form.value.originalProjectManagerName = val[0].name
    form.value.originalProjectManager = val[0].id
  } else {
    form.value.originalProjectManagerName = ''
    form.value.originalProjectManager = ''
  }
}

const orgPickerRef5 = ref()
function handleSelectedMarketManager(val) {
  if (val && val.length > 0) {
    form.value.projectManagerName = val[0].name
    form.value.projectManager = val[0].id
  } else {
    form.value.projectManagerName = ''
    form.value.projectManager = ''
  }
}

// 项目经理核对
const formRef2 = ref(null)
function onChangeProjectPhase() {
  // 更改项目阶段 清空项目信息
  form.value.projectName = ''
  form.value.projectCode = ''
}

// 市场经理核对
const formRef3 = ref(null)
// 楚天云服务处理
const formRef4 = ref(null)

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(form))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      const data = getData()
      let valid = false
      if (isProjectManagerChecks.value.isEdit) {
        valid = await formRef2.value.validate()
      }
      if (isMarketManagerChecks.value.isEdit) {
        valid = await formRef3.value.validate()
      }
      if (isCloudServiceChecks.value.isEdit) {
        valid = await formRef4.value.validate()
      }
      if (!valid) {
        return
      }
      data.processDefId = props.instanceId
      const res = await submitRenewalResourceWorkOrderProcessWithApprove(data)
      if (res.code === 200) {
        ElMessage.success('提交成功')
        return Promise.resolve()
      } else {
        ElMessage.warning('提交失败')
        return Promise.reject(new Error('提交失败'))
      }
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
  .header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important;

  /* 设置最小高度 */

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .plan-item {
    width: 100%;

    .form-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 10px;
    margin-bottom: 40px;
    border: 1px dashed #1677ff;
    border-radius: 6px;
    color: #1677ff;
    font-size: 14px;
    cursor: pointer;
  }

  .form-item {
    width: 360px;
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 12px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}
</style>

  <style lang="scss">
  .custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
