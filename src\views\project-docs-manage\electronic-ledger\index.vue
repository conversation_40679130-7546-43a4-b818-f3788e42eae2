<template>
  <Container>
    <TableContainer title="电子清单台账">
      <template #search="{ searchBoxWidth }">
        <FilterCriteria
          :popover-width="searchBoxWidth"
          @search="onSearch"
          @reset="onReset">
          <ElForm
            ref="formRef"
            label-position="left"
            label-width="90px"
            :model="filterFormData">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem label="项目编号">
                  <ElInput v-model="filterFormData.projectCode" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="项目名称">
                  <ElInput v-model="filterFormData.projectName" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem
                  label="项目归属部门："
                  prop="deliveryEntityId">
                  <ElTreeSelect
                    v-model="filterFormData.deliveryEntityId"
                    :multiple="true"
                    collapse-tags
                    :teleported="false"
                    :render-after-expand="false" />
                </ElFormItem>
                <!-- <ElFormItem label="项目归属部门">
                  <ElInput v-model="filterFormData.deliveryEntityId" />
                </ElFormItem> -->
              </ElCol>
              <ElCol :span="6">
                <ElFormItem label="验收年份">
                  <ElDatePicker
                    v-model="filterFormData.acceptanceDate"
                    :teleported="false"
                    type="year"
                    value-format="YYYY"
                    placeholder="请选择年份"
                    clearable />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </FilterCriteria>
      </template>

      <template #toolbar>
        <div>
          <ElButton @click="exportHandler">
            <template #icon>
              <i class="iconfont icon-UploadOutlined" />
            </template>
            下载
          </ElButton>
        </div>
      </template>

      <template #default="{ contentHeight }">
        <ElTabs
          v-model="activePhase"
          @tab-change="handlePhaseChange">
          <ElTabPane
            v-if="currentRoles.includes(1)"
            label="售前阶段"
            name="售前阶段" />
          <ElTabPane
            v-if="currentRoles.includes(2)"
            label="交付阶段"
            name="交付阶段" />
          <ElTabPane
            v-if="currentRoles.includes(3)"
            label="采购阶段"
            name="采购阶段" />
        </ElTabs>
        <ElTable
          :max-height="contentHeight"
          :data="tableData"
          border
          style="width: 100%">
          <ElTableColumn
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
            width="100" />
          <ElTableColumn
            prop="projectCode"
            label="项目编号" />
          <ElTableColumn
            prop="projectName"
            label="项目名称" />
          <!-- <ElTableColumn
            prop="bolProjectName"
            label="文档一级分类" /> -->
          <ElTableColumn
            prop="deliveryEntity"
            label="项目归属部门" />
          <ElTableColumn
            prop="acceptanceDate"
            label="验收年份" />
          <!-- <ElTableColumn
            prop="bolProjectName"
            label="已提交电子文档个数">
            <template #default="{ row }">
              <span style="color: #67C23A">{{ row.bolProjectName }}</span>
            </template>
          </ElTableColumn> -->
          <ElTableColumn
            prop="electronicMissingCount"
            label="缺失电子文档个数">
            <template #default="{ row }">
              <span style="color: #F56C6C">{{ row.electronicMissingCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="electronicPendingCount"
            label="待整改电子文档个数">
            <template #default="{ row }">
              <span style="color: #E6A23C">{{ row.electronicPendingCount }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="expectedRectifyDate"
            label="预计整改完成日期" />
          <ElTableColumn
            fixed="right"
            width="150"
            label="操作类型">
            <template #default="{ row }">
              <ElButton
                :disabled="row.expectedRectifyDate"
                type="primary"
                link
                @click="onCreate(row)">
                编辑
              </ElButton>
              <!-- <ElButton
                type="danger"
                link
                @click="onDelete(row)">
                删除
              </ElButton> -->
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <template #footer>
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @current-change="onSearch"
          @size-change="(size) => (onReset({ size, isClear: false }))" />
      </template>
    </TableContainer>
    <ElDialog
      v-model="dialogVisible"
      title="选择整改日期"
      width="30%">
      <ElForm
        :model="formData"
        :rules="rules">
        <ElFormItem
          label="预计整改完成日期"
          prop="expectedRectifyDate">
          <ElDatePicker
            v-model="formData.expectedRectifyDate"
            type="date"
            value-format="YYYYMMDD"
            placeholder="选择日期" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="dialogVisible = false">
          取消
        </ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit">
          确定
        </ElButton>
      </template>
    </ElDialog>
  </Container>
</template>

<script setup>
import { getElectronicDocsPageList, updateProjectDocs } from '@/api/system/docs.js'
import Container from '@/components/Container/index.vue'
import TableContainer from '@/components/Container/table-container.vue'
import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'
import useUserStore from '@/store/modules/user.js'
import { usePagination } from '@/utils/hooks.js'
import { onMounted } from 'vue'

const { proxy } = getCurrentInstance()
const activePhase = ref('售前阶段')
const currentRoles = ref([])
const currentRow = ref('')
const dialogVisible = ref(false)
const formData = reactive({
  expectedRectifyDate: '',
})
const rules = reactive({
  expectedRectifyDate: [
    { required: true, message: '请选择整改日期', trigger: 'change' },
  ],
})

const filterFormData = reactive({
  projectPhase: '售前阶段',
  projectCode: '',
  projectName: '',
  deliveryEntityId: null,
  acceptanceDate: '',
})

const tableData = ref([])
const { pagination } = usePagination()

function handlePhaseChange(projectPhase) {
  filterFormData.projectPhase = projectPhase
  onSearch()
}

async function onSearch() {
  // const getInfo = useUserStore().getInfo();
  // console.log('getInfo', getInfo)
  // console.log('roles', getInfo.roles())
  // console.log('roles', getInfo.roles)
  // const isAdmin = getInfo.roles().filter(role => role === 'admin').length > 0;
  // console.log('isAdmin', isAdmin)
  // console.log('useUserStore', useUserStore())
  // console.log('filterFormData', filterFormData)
  try {
    const res = await getElectronicDocsPageList({
      page: { ...pagination },
      params: {
        ...filterFormData,
      },
    })
    const { total, list } = res.data
    tableData.value = list
    pagination.total = total
  } catch (error) {
    console.log(error)
  }
}

const formRef = useTemplateRef('formRef')
function onReset(data = {}) {
  const config = { isClear: true, size: 10, ...data }
  if (config.isClear) {
    formRef.value.resetFields()
  }
  filterFormData.projectCode = ''
  filterFormData.projectName = ''
  filterFormData.deliveryEntityId = null
  filterFormData.acceptanceDate = ''
  pagination.pageSize = config.size
  pagination.pageNum = 1
  onSearch()
}

async function handleSubmit() {
  try {
    await updateProjectDocs(
      currentRow.value.id,
      {
        expectedRectifyDate: formData.expectedRectifyDate,
      },
    )
    ElMessage.success('提交成功')
    dialogVisible.value = false
    onSearch() // 刷新列表
  } catch (e) {
    console.error(e)
  }
}

function onCreate(row) {
  currentRow.value = row
  dialogVisible.value = true
}
// 导出
function exportHandler() {
  proxy.download(
    '/project/doc/electronic/export',
    {
      ...filterFormData,
    },
    `纸质清单台账_${new Date().getTime()}.xlsx`,
  )
}

onMounted(async () => {
  onSearch() // 添加首次加载数据
  const { user, roles } = await useUserStore().getInfo()
  // 市场拓展部 207
  // 经营管理部 227
  // 综合办公室 203
  // 采购管理部 224
  // 企业服务事业部 240
  // 集成交付部 211
  if (roles.includes('admin') || user.deptId === 227 || user.deptId === 203) {
    currentRoles.value = [1, 2, 3]
  } else if (user.deptId === 224) {
    currentRoles.value = [1]
  } else if (user.deptId === 207) {
    currentRoles.value = [2]
  } else if (user.deptId === 240 || user.deptId === 211) {
    currentRoles.value = [3]
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-tabs) {
  margin-bottom: 16px;
  background: #fff;
  border-radius: 4px;
}
:deep(.el-tabs__nav-wrap:after) {
  background-color: #fff;
}
</style>
