import request from '@/utils/request.js'

/**
 * 结算分页列表
 */
export function getSettlementList(params) {
  return request({
    url: '/outsource/settlement/list',
    method: 'get',
    params,
  })
}

/**
 * 供应商下拉列表
 */
export function getSupplierList() {
  return request({
    url: '/outsource/supplier/all',
    method: 'get',
  })
}

/**
 * 外包导入结算
 */
export function importSettlement(data) {
  return request({
    url: '/outsource/settlement',
    method: 'put',
    data,
  })
}

/**
 * 导出结算列表
 */
export function exportSettlement(data) {
  return request({
    url: '/outsource/initiateReview/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

/**
 * 结算详情
 */
export function getSettlementDetail(id) {
  return request({
    url: `/outsource/settlement/${id}`,
    method: 'get',
  })
}

/**
 * 外包发起结算审批
 */
export function settlementInitiateReview(data) {
  return request({
    url: `/outsource/settlement/initiateReview`,
    method: 'post',
    data,
  })
}

/**
 * 外包结算明细列表
 */
export function getSettlementDetailList(id, params) {
  return request({
    url: `/outsource/settlementDetail/${id}`,
    method: 'get',
    params,
  })
}

/**
 * 外包明细修改
 */
export function updateSettlementDetail(data) {
  return request({
    url: '/outsource/settlementDetail',
    method: 'post',
    data,
  })
}

/**
 * 外包结算导出明细
 */
export function exportSettlementDetail(id, data) {
  return request({
    url: `/outsource/settlementDetail/export/${id}`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}
