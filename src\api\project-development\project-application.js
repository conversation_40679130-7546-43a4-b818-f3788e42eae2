import { get, post, remove } from '@/utils/alova'

/**
 * 获取立项信息分页列表
 */
export function getProjectApplicationPageList(data, config) {
  return post('/project/prd/project/initiate/pageList', data, config)
}

/**
 * 发起立项申请或暂存或修改
 */
export function projectTemporaryStorageOrApplicationOrChange(data, config = { transformRes: false }) {
  return post('/project/prd/project/initiate/start', data, config)
}

/**
 * 获取立项申请详情
 */
export function getApplicationDetail(prdProjectInitiateBid, config) {
  return get(`/project/prd/project/initiate/detail/${prdProjectInitiateBid}`, null, config)
}

/**
 * 删除立项申请
 */
export function deleteApplication(prdProjectInitiateBid, config) {
  return remove(`/project/prd/project/initiate/delete/${prdProjectInitiateBid}`, null, config)
}

// 根据code获取部门信息
export function getDeptInfo(params, config) {
  return get('/system/dept/getDeptBySysCode', params, config)
}
