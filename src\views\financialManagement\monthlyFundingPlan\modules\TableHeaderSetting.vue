<template>
  <ElPopover
    placement="bottom"
    width="300"
    trigger="click">
    <div style="overflow: auto; max-height: 300px;">
      <ElCheckboxGroup v-model="localCheckedColumns">
        <ElCheckbox
          v-for="col in columns"
          :key="col.key"
          :label="col.key"
          :disabled="alwaysVisibleColumns.includes(col.key)">
          {{ col.label }}
        </ElCheckbox>
      </ElCheckboxGroup>
    </div>
    <template #reference>
      <slot name="reference">
        <ElButton>
          表头显示设置
        </ElButton>
      </slot>
    </template>
  </ElPopover>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  columns: { type: Array, required: true }, // [{key, label}]
  alwaysVisibleColumns: { type: Array, required: true },
  checkedColumns: { type: Array, required: true },
})
const emit = defineEmits(['update:checkedColumns'])
const localCheckedColumns = ref([...props.checkedColumns])

// 只在props变化时同步，不反向emit
watch(() => props.checkedColumns, (val) => {
  if (val.join(',') !== localCheckedColumns.value.join(',')) {
    localCheckedColumns.value = [...val]
  }
})
// 只在用户操作时emit
watch(localCheckedColumns, (val) => {
  if (val.join(',') !== props.checkedColumns.join(',')) {
    emit('update:checkedColumns', val)
  }
})
</script>
