<template>
  <TableContainer title="项目工时明细列表">
    <template #toolbar>
      <div>
        <ElButton
          @click="exportHandle">
          导出
        </ElButton>
      </div>
    </template>
    <template #default="{ contentHeight }">
      <ElTable
        v-loading="loading"
        border
        :data="data"
        row-key="bid"
        :max-height="contentHeight"
        @selection-change="handleSelect">
        <ElTableColumn
          type="selection"
          reserve-selection
          width="55" />
        <ElTableColumn
          type="index"
          label="序号"
          width="60" />
        <ElTableColumn
          prop="submitter"
          label="填报人" />
        <ElTableColumn
          prop="workDate"
          label="工时日期" />
        <ElTableColumn
          prop="workDays"
          label="工作时长（天）" />
        <ElTableColumn
          prop="deptName"
          label="所属部门" />
        <ElTableColumn
          prop="workContent"
          label="工作内容" />
        <ElTableColumn
          prop="remark"
          label="备注" />
        <ElTableColumn
          prop="workAttribute"
          label="工时属性">
          <template #default="{ row }">
            <ElTag
              v-if="row.workAttribute === 0"
              type="success">
              出勤
            </ElTag>
            <ElTag
              v-else
              type="info">
              差旅
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="projectManager"
          label="项目经理" />
        <ElTableColumn
          prop="deptLeader"
          label="部门负责人" />
        <ElTableColumn
          prop="submitTime"
          label="提交日期" />
        <ElTableColumn
          prop="timesheetDetailCode"
          label="工时明细单号" />
      </ElTable>
    </template>
    <template #footer>
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="Number(total)" />
    </template>
  </TableContainer>
</template>

<script setup>
import { getProjectLibraryWorkHourList } from '@/api/project-development/project-library.js'
import TableContainer from '@/components/Container/table-container.vue'
import { usePagination } from 'alova/client'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const { loading, data, pageSize, page, total } = usePagination((pageNum, pageSize) => getProjectLibraryWorkHourList({
  page: {
    orders: [
      {
        asc: true,
        field: '',
      },
    ],
    pageNum,
    pageSize,
  },
  params: {
    approveStatus: '',
    deptName: '',
    endWorkDate: '',
    projectCode: '',
    projectName: '',
    startWorkDate: '',
    submitEndTime: '',
    submitStartTime: '',
    submitter: '',
    timesheetDetailCode: '',
    workAttribute: '',
    workHourType: '',
  },
}), {
  total: res => res.total,
  data: res => res.list,
  initialPage: 1, // 初始页码，默认为1
  initialPageSize: 10, // 初始每页数据条数，默认为10
})

const ids = ref([])
function handleSelect(selection) {
  ids.value = selection.map(item => item.bid)
}

async function exportHandle() {
  if (ids.value.length <= 0) {
    ElMessage.warning('请至少选择一条数据')
  } else {
    proxy.download('/project/prd/project/library/detail/workHour/export', { bids: ids.value, isAll: 0, params: {} }, `研发项目项目库工时明细_${new Date().getTime()}.xlsx`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
