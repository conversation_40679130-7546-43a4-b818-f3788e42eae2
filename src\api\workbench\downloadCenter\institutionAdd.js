import request from '@/utils/request'

export function institutionAdd(data) {
  return request ({
    url: '/system/institution/document',
    method: 'put',
    data,
  })
}
export function institutionDetails(bid) {
  return request({
    url: `/system/institution/document/${bid}`,
    method: 'get',
  })
}

export function institutionEdit(data) {
  return request ({
    url: '/system/institution/document',
    method: 'post',
    data,
  })
}
