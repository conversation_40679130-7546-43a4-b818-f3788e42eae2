import request from '@/utils/request.js'

/**
 * 工时明细审批分页列表
 */
export function getApproveList(data) {
  return request({
    url: '/workhour/timesheetDetail/approvePageList',
    method: 'post',
    data,
  })
}

/**
 * 工时审批
 */
export function approveWork(data) {
  return request({
    url: '/workhour/timesheetDetail/approve',
    method: 'post',
    data,
  })
}

/**
 * 工时明细列表
 */
export function getApproveDetailList(data) {
  return request({
    url: '/workhour/timesheetDetail/pageList',
    method: 'post',
    data,
  })
}

/**
 * 跟进记录详情
 */
export function getFollowRecord(id) {
  return request({
    url: `/workhour/followRecord/detail/${id}`,
    method: 'get',
  })
}

/**
 * 工时明细详情
 */
export function getWorkDetail(id) {
  return request({
    url: `/workhour/timesheetDetail/${id}`,
    method: 'get',
  })
}

/**
 * 审批工时导出
 */
export function exportApproveList(data) {
  return request({
    url: `/workhour/timesheetDetail/exportApproveList`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

/**
 * 工时明细导出
 */
export function exportPageList(data) {
  return request({
    url: `/workhour/timesheetDetail/exportPageList`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

/**
 * 批量通过
 */
export function approveWorkBatch(data) {
  return request({
    url: '/workhour/timesheetDetail/batchApprove',
    method: 'post',
    data,
  })
}

/**
 * 撤销
 */
export function revocationWork(id) {
  return request({
    url: `/workhour/timesheetDetail/revocation/${id}`,
    method: 'post',
  })
}
