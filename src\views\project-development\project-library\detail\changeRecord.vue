<template>
  <TableContainer title="项目变更记录列表">
    <template #toolbar>
      <div>
        <ElButton
          @click="exportHandle">
          导出
        </ElButton>
      </div>
    </template>
    <template #default="{ contentHeight }">
      <ElTable
        v-loading="loading"
        border
        row-key="bid"
        :data="data"
        :max-height="contentHeight"
        @selection-change="handleSelect">
        <ElTableColumn
          type="selection"
          reserve-selection
          width="55" />
        <ElTableColumn
          prop="changeType"
          label="变更类型" />
        <ElTableColumn
          prop="changeReason"
          label="变更原因" />
        <ElTableColumn
          prop="submitter"
          label="申请人" />
        <ElTableColumn
          prop="submitTime"
          label="提交时间" />
      </ElTable>
    </template>
    <template #footer>
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="Number(total)" />
    </template>
  </TableContainer>
</template>

<script setup>
import { getProjectLibraryChangedRecordList } from '@/api/project-development/project-library.js'
import TableContainer from '@/components/Container/table-container.vue'
import { usePagination } from 'alova/client'
import { ElMessage } from 'element-plus'

const props = defineProps({
  projectCode: {
    default: '',
  },
})

const { proxy } = getCurrentInstance()

const { loading, data, pageSize, page, total } = usePagination((pageNum, pageSize) => getProjectLibraryChangedRecordList({
  page: {
    pageNum,
    pageSize,
  },
  params: {
    prdPrdProjectCode: props.projectCode,
  },
}), {
  total: res => res.total,
  data: res => res.list,
  initialPage: 1, // 初始页码，默认为1
  initialPageSize: 10, // 初始每页数据条数，默认为10
})

const ids = ref([])
function handleSelect(selection) {
  ids.value = selection.map(item => item.bid)
}

async function exportHandle() {
  if (ids.value.length <= 0) {
    ElMessage.warning('请至少选择一条数据')
  } else {
    proxy.download('/project/prd/project/library/detail/change/export', { bids: ids.value, isAll: 0, params: {} }, `研发项目项目库变更记录_${new Date().getTime()}.xlsx`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
