<template>
  <ElDialog
    v-model="visible"
    v-loading="loading"
    title="发起填报"
    width="500px"
    @close="handleClose">
    <ElForm
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px">
      <ElFormItem
        label="填报月度"
        prop="fillMonth">
        <ElDatePicker
          v-model="form.fillMonth"
          placeholder="请选择填报月度"
          clearable
          value-format="YYYY-MM"
          style="width: 100%"
          type="month"
          @change="handleMonthChange" />
      </ElFormItem>
      <ElFormItem
        label="填报起始月度"
        prop="planStartMonth">
        <ElInput
          v-model="form.planStartMonth"
          placeholder="选择填报月度后自动计算"
          disabled />
      </ElFormItem>
      <ElFormItem
        label="填报终止月度"
        prop="planEndMonth">
        <ElInput
          v-model="form.planEndMonth"
          placeholder="选择填报月度后自动计算"
          disabled />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton
          :loading="loading"
          @click="handleClose"> 取消</ElButton>
        <ElButton
          type="primary"
          :loading="loading"
          @click="handleConfirm">
          确定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup>
import { initiatePlan } from '@/api/financialManagement/monthlyFundingPlan.js'
import { deepClone } from '@/utils'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const emit = defineEmits(['confirm'])
const visible = ref(false)
const form = ref({
  fillMonth: '',
  planStartMonth: '',
  planEndMonth: '',
})
const formRef = ref(null)
const loading = ref(false)

const rules = {
  fillMonth: [{ required: true, message: '请选择填报月度' }],
}
function open() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.value.fillMonth = ''
  form.value.planStartMonth = ''
  form.value.planEndMonth = ''
  visible.value = true
}

function handleClose() {
  formRef.value.resetFields()
  form.value.fillMonth = ''
  form.value.planStartMonth = ''
  form.value.planEndMonth = ''
  visible.value = false
  loading.value = false
}

function handleConfirm() {
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true
      const data = deepClone(form.value)
      initiatePlan(data)
        .then((res) => {
          console.log(res, '---- initiatePlan')
          if (res.code === 200) {
            ElMessage.success('发起填报成功')
            emit('confirm', data)
            handleClose()
          } else {
            ElMessage.warning(res.msg)
          }
        })
        .catch((err) => {
          console.log(err, '---- initiatePlan')
        })
        .finally(() => {
          loading.value = false
        })
    }
  })
}

function handleMonthChange(value) {
  // 获取填报月度的年月
  const [year, month] = value.split('-')

  // 计算起始月份 (填报月度+1)
  const startDate = new Date(year, month - 1 + 1, 1)
  form.value.planStartMonth = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`

  // 计算终止月份 (填报月度+3)
  const endDate = new Date(year, month - 1 + 3, 1)
  form.value.planEndMonth = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`
}

defineExpose({
  open,
})
</script>
