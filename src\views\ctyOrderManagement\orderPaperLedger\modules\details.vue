<template>
  <DefaultContainer>
    <div
      v-if="!props.id"
      class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          楚天云订单纸质台账详情
        </div>
      </div>
    </div>
    <div class="content">
      <!-- <div class="sub-title">
        基本信息
      </div> -->
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="240">
        <ElDescriptionsItem
          v-for="item in descriptionList1"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
        <ElDescriptionsItem
          :span="3"
          width="398">
          <template #label>
            <div>用印订单附件</div>
          </template>
          <p
            v-for="file in fileList"
            :key="file.url"
            class="file-text"
            @click="downloadFile(file.url, file.name)">
            {{ file.name }}
          </p>
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </DefaultContainer>
</template>

<script setup>
import { getOrderPaperLedgerDetail } from '@/api/ctyOrderManagement/orderPaperLedger.js'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { Back } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
})
const router = useRouter()
const fileList = ref([])
const descriptionList1 = [
  { label: '年度', value: 'year', span: 1 },
  { label: '订单部门', value: 'deptName', span: 1 },
  { label: '经办人', value: 'handledBy', span: 1 },
  { label: '订单编号', value: 'orderNumber', span: 1 },
  { label: '订单名称', value: 'orderName', span: 1 },
  { label: '订单性质', value: 'orderNature', span: 1 },
  { label: '应用系统名称', value: 'ctAppName', span: 3 },
  { label: '客户名称', value: 'customerName', span: 3 },
  { label: '项目阶段', value: 'projectPhase', span: 3 },
  { label: '项目名称', value: 'projectName', span: 3 },
  { label: '项目编号', value: 'projectCode', span: 3 },
  { label: '销售合同名称', value: 'saleContractName', span: 3 },
  { label: '销售合同编号', value: 'saleContractNumber', span: 3 },
  { label: '云服务实际开通日期', value: 'serviceStartTime', span: 1 },
  { label: '云服务实际结算日期', value: 'serviceEndTime', span: 31 },
  { label: '履约周期(天)', value: 'servicePerformanceCycle', span: 1 },
  { label: '订单总金额(元)', value: 'orderAmount', span: 3 },
  { label: '备注', value: 'remark', span: 3 },
]
const detail = ref({})
const id = ref()
onMounted(() => {
  if (props.id) {
    id.value = props.id
  } else {
    id.value = router.currentRoute.value.query.id
  }
  getOrderPaperLedgerDetail({ id: id.value }).then((res) => {
    if (res.code === 200) {
      detail.value = res.data
      if (detail.value.attachment) {
        try {
          let parsedArr = JSON.parse(detail.value.attachment)
          parsedArr = parsedArr.map(item => ({
            name: item.match(/[^/\\?#]+$/)[0],
            url: item,
          }))
          fileList.value = parsedArr
        } catch (error) {
          console.log(error)
        }
      }
    }
  })
})

function downloadFile(url, filename) {
  const link = document.createElement('a')
  link.href = url
  link.download = filename // 设置下载的文件名
  document.body.appendChild(link)
  link.click() // 触发点击事件
  document.body.removeChild(link) // 移除元素
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(detail))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

    <style lang="scss" scoped>
  .header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 8px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  position: relative;
  margin-top: 20px;

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .remark-attachment {
    &:hover {
      color: #409eff;
      cursor: pointer;
    }
  }
}

.file-text {
  cursor: pointer;

  &:hover {
    color: #409eff;
    cursor: pointer;
  }
}
</style>
