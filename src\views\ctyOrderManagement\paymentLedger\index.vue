<template>
  <DefaultContainer v-loading="isLoading">
    <!-- 折叠面板组件，用于查询条件 -->
    <Collapse>
      <template #header />
      <!-- 查询表单 -->
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="200px"
        label-position="top">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElFormItem
              prop="statementFormNo"
              label="关联结算单编号">
              <ElInput
                v-model="formData.statementFormNo"
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="ctAppName"
              label="应用系统名称">
              <ElInput
                v-model="formData.ctAppName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="year"
              label="结算年度">
              <ElDatePicker
                v-model="formData.year"
                type="year"
                style="width: 100%"
                value-format="YYYY"
                placeholder="请选择结算年度" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="projectName"
              label="项目名称">
              <ElInput
                v-model="formData.projectName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="projectManager"
              label="项目经理">
              <ElSelect
                v-model="formData.projectManagerName"
                placeholder="请选择"
                @visible-change="
                  (change) => change && (orgPickerRef2.show())
                " />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="marketManager"
              label="市场经理">
              <ElSelect
                v-model="formData.marketManagerName"
                placeholder="请选择"
                @visible-change="
                  (change) => change && (orgPickerRef3.show())
                " />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="totalPaymentAmount"
              label="合同合计拟付款金额(元)">
              <NumberRange
                v-model="formData.totalPaymentAmount"
                :min-value="0"
                :precision="0"
                style="width: 100%" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="totalCollectionAmount"
              label="合同合计拟回款金额(元)">
              <NumberRange
                v-model="formData.totalCollectionAmount"
                :min-value="0"
                :precision="0"
                style="width: 100%" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </Collapse>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
      <ElButton
        type="primary"
        @click="handle('add')">
        新建
      </ElButton>
      <ElButton
        type="primary"
        @click="handleExport">
        导出
      </ElButton>
    </div>
    <!-- 数据表格 -->
    <ElTable
      border
      :data="tableData"
      style="margin-top: 20px"
      @row-dblclick="onRowDbClick">
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        width="60"
        fixed="left"
        align="center" />
      <ElTableColumn
        prop="statementFormNo"
        label="关联结算单编号"
        min-width="120" />
      <ElTableColumn
        prop="year"
        label="结算年度"
        width="80" />
      <ElTableColumn
        prop="deptName"
        label="订单部门"
        min-width="100" />
      <ElTableColumn
        prop="ctAppName"
        label="应用系统名称"
        min-width="120" />
      <ElTableColumn
        prop="customerName"
        label="客户名称"
        min-width="100" />
      <ElTableColumn
        prop="projectCode"
        label="项目编号"
        min-width="130" />
      <ElTableColumn
        prop="projectName"
        label="项目名称"
        min-width="130" />
      <ElTableColumn
        prop="priceType"
        label="合同价格类型"
        min-width="120" />
      <ElTableColumn
        prop="totalPaymentAmount"
        label="合计拟付款金额(元)"
        min-width="140" />
      <ElTableColumn
        prop="totalCollectionAmount"
        label="合计拟回款金额(元)"
        min-width="140" />
      <ElTableColumn
        label="操作"
        width="110"
        fixed="right">
        <template #default="scope">
          <ElButton
            type="primary"
            link
            @click="handle('edit', scope.row)">
            编辑
          </ElButton>
          <ElPopconfirm
            title="确定删除吗？"
            @confirm="handle('delete', scope.row)">
            <template #reference>
              <ElButton
                type="primary"
                link>
                删除
              </ElButton>
            </template>
          </ElPopconfirm>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onSearch" />
    </div>
    <OrgPicker
      ref="orgPickerRef2"
      type="user"
      title="选择项目经理"
      @ok="handleSelectedProjectManager" />
    <OrgPicker
      ref="orgPickerRef3"
      type="user"
      title="选择市场经理"
      @ok="handleSelectedMarketManager" />
  </DefaultContainer>
</template>

<script setup>
import { deleteCtCloudServicePaymentLedger, getCtCloudServicePaymentLedgerPageList } from '@/api/ctyOrderManagement/paymentLedger.js'
import Collapse from '@/components/Collapse/index.vue'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import NumberRange from '@/components/NumberRange/index.vue'
import { usePagination } from '@/utils/hooks.js'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

/**
 * 分页相关
 * pagination: 分页参数对象
 * indexMethod: 表格序号计算方法
 */
const { pagination, indexMethod } = usePagination()

const router = useRouter()

/**
 * 表单数据与引用
 */
const formData = reactive({
  totalPaymentAmount: [null, null],
  totalCollectionAmount: [null, null],
}) // 查询表单数据
const formRef = ref() // 表单引用，用于重置

/**
 * 表格数据与加载状态
 */
const tableData = ref([]) // 表格数据
const loadingIndex = ref(0) // 加载计数器，用于处理多个并发请求的loading状态

/**
 * 计算属性：是否显示加载状态
 * 当loadingIndex > 0时显示加载状态
 */
const isLoading = computed(() => {
  return loadingIndex.value !== 0
})

/**
 * 生命周期钩子：组件挂载完成后初始化
 */
onMounted(() => {
  init()
})

/**
 * 获取应用系统信息分页列表
 */
function getPage() {
  loadingIndex.value++ // 开始加载，计数器加1
  const searchData = getParams(true)
  getCtCloudServicePaymentLedgerPageList(searchData)
    .then((res) => {
      console.log(res, '---- getCtCloudServicePaymentLedgerPageList')
      tableData.value = res.data.list
      pagination.total = res.data.total
      loadingIndex.value-- // 加载完成，计数器减1
    })
    .catch((err) => {
      console.log(err, '---- getCtCloudServicePaymentLedgerPageList')
      loadingIndex.value-- // 加载出错，计数器减1
    })
}

/**
 * 初始化方法：组件挂载后加载应用系统列表数据
 */
function init() {
  getPage()
}

/**
 * 表格行双击事件处理
 * @param {object} row - 行数据
 */
function onRowDbClick(row) {
  router.push({
    path: '/ctyOrderManagement/paymentLedger/details',
    query: {
      id: row.id,
    },
  })
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getPage()
}

/**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  getPage()
}

/**
 * 重置方法
 * 重置表单并查询第一页数据
 */
function onReset() {
  formRef.value.resetFields() // 重置表单字段
  pagination.pageNum = 1 // 重置到第一页
  onSearch() // 执行查询
}

/**
 * 操作方法
 * @param {string} type - 操作类型：add-新增应用系统，edit-编辑应用系统，delete-删除应用系统
 * @param {object} row - 行数据对象，包含应用系统信息
 */
function handle(type, row) {
  console.log(type, row, '---- handle')
  switch (type) {
    case 'add':
      console.log('新建应用系统')
      router.push({
        path: '/ctyOrderManagement/paymentLedger/form',
        query: {
          type: 'add',
        },
      })
      break
    case 'edit':
      console.log('编辑应用系统')
      router.push({
        path: '/ctyOrderManagement/paymentLedger/form',
        query: {
          id: row.id,
          type: 'edit',
        },
      })
      break
    case 'delete':
      console.log('删除应用系统')
      deleteCtCloudServicePaymentLedger({
        id: row.id,
      })
        .then((res) => {
          console.log(res, '---- deleteCtCloudServicePaymentLedger')
          if (res.code === 200) {
            ElMessage.success('删除成功')
            getPage()
          } else {
            ElMessage.warning(res.msg)
          }
        })
      break
    default:
      console.log('未知操作类型')
      break
  }
}

function getParams(isList) {
  const params = { ...formData }
  if (formData.totalPaymentAmount && formData.totalPaymentAmount[0] !== null && formData.totalPaymentAmount[1] !== null) {
    params.totalPaymentAmountMin = formData.totalPaymentAmount[0]
    params.totalPaymentAmountMax = formData.totalPaymentAmount[1]
    if (params.totalPaymentAmountMax < params.totalPaymentAmountMin) {
      delete params.totalPaymentAmountMax
    }
  }
  delete params.totalPaymentAmount

  if (formData.totalCollectionAmount && formData.totalCollectionAmount[0] !== null && formData.totalCollectionAmount[1] !== null) {
    params.totalCollectionAmountMin = formData.totalCollectionAmount[0]
    params.totalCollectionAmountMax = formData.totalCollectionAmount[1]
    if (params.totalCollectionAmountMax < params.totalCollectionAmountMin) {
      delete params.totalCollectionAmountMax
    }
  }
  delete params.totalCollectionAmount

  delete params.discountedPrice
  if (!isList) {
    return params
  } else {
    const searchData = {
      page: {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
      },
      params,
    }
    return searchData
  }
}

const orgPickerRef2 = ref()
function handleSelectedProjectManager(rows) {
  if (rows.length > 0) {
    formData.projectManager = rows[0].id
    formData.projectManagerName = rows[0].name
  } else {
    formData.projectManager = ''
    formData.projectManagerName = ''
  }
}

const orgPickerRef3 = ref()
function handleSelectedMarketManager(rows) {
  if (rows.length > 0) {
    formData.marketManager = rows[0].id
    formData.marketManagerName = rows[0].name
  } else {
    formData.marketManager = ''
    formData.marketManagerName = ''
  }
}
const { proxy } = getCurrentInstance()
function handleExport() {
  const params = getParams(false)
  proxy.download(
    '/cloudorder/ct-cloud-service-payment-ledger/download',
    {
      ...params,
    },
    `付款台账_${new Date().getTime()}.xlsx`,
  )
}
</script>

<style lang="scss" scoped>
/* 操作区域样式 */
.operation-area {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
