<template>
  <ElDialog
    v-model="model"
    title="关联采购合同"
    @open="onOpen">
    <ElPopover
      placement="bottom-start"
      trigger="click"
      width="calc(50% - 30px)"
      :hide-after="0"
      :show-arrow="false">
      <template #reference>
        <ElButton>
          <i
            class="iconfont icon-sift"
            :style="{
              marginRight: '8px',
            }" />
          所有筛选
          <ElIcon
            :style="{
              marginLeft: '10px',
            }">
            <ArrowDown />
          </ElIcon>
        </ElButton>
      </template>
      <div style="padding: 20px 4px;">
        <ElForm label-width="auto">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="合同名称">
                <ElInput
                  v-model="queryParams.name"
                  placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="合同编号">
                <ElInput
                  v-model="queryParams.code"
                  placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="关联项目名称">
                <ElInput
                  v-model="queryParams.projectName"
                  placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="业务负责人">
                <ElInput
                  v-model="queryParams.attnName"
                  placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="10">
              <ElFormItem label="业务归属部门">
                <ElInput
                  v-model="queryParams.handlingDepartmentName"
                  placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="6">
              <div style="display: flex; justify-content: flex-end; align-items: center;">
                <ElButton @click="onReset">
                  重置
                </ElButton>
                <ElButton
                  type="primary"
                  @click="onSearch">
                  搜索
                </ElButton>
              </div>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>
    </ElPopover>
    <ElTable
      v-loading="loading"
      style="margin-top: 20px;"
      max-height="calc(70vh - 100px)"
      :data="data">
      <ElTableColumn
        label="序号"
        width="60"
        type="index" />
      <ElTableColumn
        label="合同名称"
        prop="name" />
      <ElTableColumn
        label="合同编号"
        prop="code" />
      <ElTableColumn
        label="关联项目名称"
        prop="projectName" />
      <ElTableColumn
        label="业务负责人"
        prop="attnName" />
      <ElTableColumn
        label="业务归属部门"
        prop="handlingDepartmentName" />
      <ElTableColumn
        label="操作"
        width="80">
        <template #default="{ row }">
          <ElButton
            link
            type="primary"
            @click="onAssociate(row)">
            关联
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
    <div style="display: flex; justify-content: flex-end; align-items: center; margin-top: 10px;">
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="Number(total)"
        @size-change="onSizeChange"
        @current-change="onCurrentChange" />
    </div>
  </ElDialog>
</template>

<script setup>
import * as apis from '@/api/purchase/purchase-ledger.js'
import { usePagination } from 'alova/client'

const emit = defineEmits(['associate'])

const model = defineModel()

const queryParams = ref({
  name: '',
  code: '',
  projectName: '',
  attnName: '',
  handlingDepartmentName: '',
})

const { data, total, page, pageSize, loading, refresh } = usePagination((page, pageSize) => {
  return apis.getPsmContractList({ purchaseContractType: 'purchase', pageNum: page, pageSize, ...queryParams.value })
}, {
  append: false, // 数据不追加
  data: res => res.records, // 定义如何取data数据
  total: res => res.total, // 定义如何取total数据
  initialPage: 1, // 设置默认pageNum
  initialPageSize: 10, // 设置默认pageSize
  immediate: false, // 初始化时不立即执行一次
})

function onCurrentChange(pageNum) {
  page.value = pageNum
}

function onSizeChange(newPageSize) {
  page.value = 1
  pageSize.value = newPageSize
}

function onAssociate(row) {
  emit('associate', row)
  model.value = false
}

function onSearch() {
  if (page.value === 1) {
    refresh()
  } else {
    page.value = 1
  }
}

function onReset() {
  queryParams.value = {
    name: '',
    code: '',
    projectName: '',
    attnName: '',
    handlingDepartmentName: '',
  }
  page.value = 1
  pageSize.value = 10
  refresh()
}

function onOpen() {
  page.value = 1
  pageSize.value = 10
  onReset()
}
</script>

<style lang="scss" scoped></style>
