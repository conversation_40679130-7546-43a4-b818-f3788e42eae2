<template>
  <div class="pane">
    <ElTable
      v-loading="loading"
      border
      :data="tableList"
      height="100%">
      <ElTableColumn
        type="index"
        label="序号"
        align="center" />
      <ElTableColumn
        label="里程碑名称"
        prop="milestoneName"
        align="center" />
      <ElTableColumn
        label="里程碑类型"
        prop="milestoneType"
        align="center" />
      <ElTableColumn
        label="计划开始日期"
        prop="planStartDate"
        align="center" />
      <ElTableColumn
        label="计划结束日期"
        prop="planEndDate"
        align="center" />
      <ElTableColumn
        label="计划回款日期"
        prop="refundDate"
        align="center" />
      <ElTableColumn
        label="计划回款金额（元）"
        prop="refundAmount"
        align="center" />
      <ElTableColumn
        label="本次里程碑累计形象进度(%)"
        prop="progress"
        min-width="150"
        align="center">
        <template #default="{ row }">
          <ElProgress :percentage="Number(row.progress)" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="实际开始日期"
        prop="actualStartDate"
        align="center" />
      <ElTableColumn
        label="实际结束日期"
        prop="actualEndDate"
        align="center" />
      <ElTableColumn
        label="完成状态"
        prop="completionStatus"
        min-width="150"
        align="center">
        <template #default="{ row }">
          <DotBadge
            :status="getCompletionStatusClass(row.completionStatus)"
            :text="getCompletionStatusLabel(row.completionStatus)" />
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="核验状态"
        min-width="150"
        align="center">
        <template #default="{ row }">
          <ElTag
            v-if="row.completionStatus?.toString() === '4'"
            :type="getVerificationStatusClass(row.verificationStatus)">
            {{ getVerificationStatusLabel(row.verificationStatus) }}
          </ElTag>
        </template>
      </ElTableColumn>
      <ElTableColumn
        fixed="right"
        label="操作"
        width="150"
        align="center">
        <template #default="{ row }">
          <!-- 完成状态不是已验收，可以做状态更新
              只有项目内的项目经理角色才能做状态更新操作 要做id对比操作
          -->
          <ElButton
            v-if="row.completionStatus?.toString() !== '4'
              && hasProjectManager(row.projectManagerId)"
            v-hasPermi="['project-manage:milestones:status']"
            type="primary"
            :text="true"
            @click="handleStatusChange(row)">
            状态更新
          </ElButton>
          <!-- 完成状态已验收且核验状态未核验  -->
          <ElButton
            v-if="row.completionStatus?.toString() === '4'
              && row.verificationStatus?.toString() === '0'"
            v-hasPermi="['project-manage:milestones:verify']"
            type="primary"
            :text="true"
            @click="handleVerify(row)">
            核验
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
  </div>
</template>

<script setup>
import { getPlanMilestonesList } from '@/api/project-manage/repository.js'
import DotBadge from '@/components/DotBadge/index.vue'
import useUserStore from '@/store/modules/user.js'
import { useRequest } from 'alova/client'

const route = useRoute()

const { loading, data: tableList } = useRequest(getPlanMilestonesList(route.query.pcode), {
  initialData: [],
})

// 获取字典
const { proxy } = getCurrentInstance()
const {
  sys_proj_milestone_progress_status,
  sys_proj_milestone_verify_status,
} = proxy.useDict(
  'sys_proj_milestone_type',
  'sys_proj_milestone_progress_status',
  'sys_proj_milestone_verify_status',
)

// 创建通用的状态获取函数生成器
function createStatusGetter(statusList) {
  return (status) => {
    return statusList.value?.find(item => item.value === status?.toString()) || {}
  }
}

const completionStatusGetter = computed(() => {
  return createStatusGetter(sys_proj_milestone_progress_status)
})

// 根据完成状态获取标签颜色
const getCompletionStatusClass = computed(() => {
  return (status) => {
    return completionStatusGetter.value(status).elTagClass
  }
})

// 根据完成状态获取标签文字
const getCompletionStatusLabel = computed(() => {
  return (status) => {
    return completionStatusGetter.value(status).label
  }
})

const verificationStatusGetter = computed(() => {
  return createStatusGetter(sys_proj_milestone_verify_status)
})

// 根据核验状态获取标签颜色
const getVerificationStatusClass = computed(() => {
  return (status) => {
    return verificationStatusGetter.value(status).elTagClass
  }
})

// 根据核验状态获取标签文字
const getVerificationStatusLabel = computed(() => {
  return (status) => {
    return verificationStatusGetter.value(status).label
  }
})

const router = useRouter()

const userStore = useUserStore()
// 判断当前用户是否为项目经理
const hasProjectManager = computed(() => {
  return managerId => userStore.id?.toString() === managerId?.toString()
})

// 状态更新
function handleStatusChange(row) {
  router.push({
    name: 'ProjectRepositoryDetailMilestonesFormStatus',
    params: {
      mid: row.id,
    },
  })
}
// 核验
function handleVerify(row) {
  router.push({
    name: 'ProjectRepositoryDetailMilestonesFormVerify',
    params: {
      mid: row.id,
    },
  })
}
</script>

<style lang="scss" scoped>
.pane {
  height: 100%;
  padding: 20px;
}
</style>
