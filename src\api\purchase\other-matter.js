import { get, post, remove } from '@/utils/alova'

/**
 * 获取供应商分页列表
 */
export function getOtherMatterPageList(data, config) {
  return post('/purchaseManage/otherMatter/pageList', data, config)
}

/**
 * 删除其它事项
 */
export function deleteOtherMatter(bid, data, config) {
  return remove(`/purchaseManage/otherMatter/delete/${bid}`, data, config)
}

/**
 * 暂存其它事项
 */
export function temporarilyStoreOtherMatter(data, config) {
  return post('/purchaseManage/otherMatter/staging', data, config)
}

/**
 * 提交其它事项
 */
export function submitOtherMatter(data, config) {
  return post('/purchaseManage/otherMatter/submit', data, config)
}

/**
 * 其它事项详情
 */
export function otherMatterDetail(bid, params, config) {
  return get(`/purchaseManage/otherMatter/detail/${bid}`, params, config)
}

/**
 * 招采发起分页列表（关联业务时弹窗中的表格使用，传审批通过状态，只筛选审批通过的项目）
 */
export function getProcurementPageList(data, config) {
  return post('/purchaseManage/procurement/pageList', data, config)
}

/**
 * 供应商审批单分页列表（关联业务时弹窗中的表格使用，传审批通过状态，只筛选审批通过的项目）
 */
export function getSupplierApprovalPageList(data, config) {
  return post('/purchaseManage/supplier/approval/pageList', data, config)
}
