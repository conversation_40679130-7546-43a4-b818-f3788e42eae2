<template>
  <div
    v-loading="loading"
    class="list">
    <ElForm
      ref="searchFormRef"
      :model="searchData"
      inline>
      <ElFormItem
        label="生态合作伙伴:"
        prop="partnerName">
        <ElInput
          v-model="searchData.partnerName"
          clearable
          placeholder="输入合作伙伴名称查询" />
      </ElFormItem>
      <ElFormItem
        label="产品名称:"
        prop="productName">
        <ElInput
          v-model="searchData.productName"
          clearable
          placeholder="输入产品名称查询" />
      </ElFormItem>
      <ElFormItem
        label="标签:"
        prop="tag">
        <ElInput
          v-model="searchData.tag"
          clearable
          placeholder="输入标签名称查询" />
      </ElFormItem>
      <ElFormItem>
        <ElButton
          type="primary"
          @click="handleSearch">
          查询
        </ElButton>
        <ElButton
          type="default"
          @click="handleReset">
          重置
        </ElButton>
      </ElFormItem>
    </ElForm>
    <template v-if="!listData.length">
      <ElEmpty
        :image="emptyImage"
        description="暂无临时产品，点击新增可添加产品" />
    </template>
    <template v-else>
      <div
        class="list_items">
        <div
          v-for="item in listData"
          :key="item.productBid"
          class="product"
          @click="handleClick(item)">
          <div class="product_header">
            <div class="product_header_icon">
              <i class="iconfontcolor icon-color-cptb-ls main" />
              <div class="product_header_icon_sub">
                <i class="iconfont icon-ls" />
              </div>
            </div>
            <ElDropdown
              placement="bottom-end">
              <ElIcon size="18px">
                <MoreFilled />
              </ElIcon>
              <template #dropdown>
                <ElDropdownMenu>
                  <ElDropdownItem @click="handleEdit(item)">
                    编辑
                  </ElDropdownItem>
                  <ElDropdownItem @click="handleDelete(item)">
                    删除
                  </ElDropdownItem>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
          </div>

          <p class="product_name">
            {{ item.productName }}
          </p>

          <div class="product_tags">
            <ElTag
              v-for="(tag, index) in showTags(item)"
              :key="index"
              :type="getTagType(index)">
              {{ tag }}
            </ElTag>
            <ElTooltip
              v-if="item.partnerClass.length > showTagsNum"
              placement="top">
              <ElTag type="info">
                +{{ item.partnerClass.length - showTagsNum }}
              </ElTag>
              <template #content>
                <span>
                  {{ joinedTags(item) }}
                </span>
              </template>
            </ElTooltip>
          </div>

          <div class="product_partner">
            生态伙伴：<span>{{ item.companyName }}</span>
          </div>

          <ElRow :gutter="16">
            <ElCol
              v-for="product in productPriceOptions"
              :key="product.value"
              :span="12">
              <ElTooltip
                effect="dark"
                placement="top">
                <template #default>
                  <p class="product_price">
                    {{ product.label }}：
                    <span>
                      ￥{{ formatPrice(item[product.value]) }}
                    </span>
                  </p>
                </template>
                <template #content>
                  {{ product.label }}：￥{{ formatPrice(item[product.value]) }}
                </template>
              </ElTooltip>
            </ElCol>
          </ElRow>

          <div class="product_time">
            <i class="iconfont icon-ClockCircleOutlined mr-[4px]" />
            <span>{{ item.updateTime || '---' }}</span>
          </div>
        </div>
      </div>

      <div class="list_pagination">
        <ElPagination
          v-model:current-page="paginationData.pageNum"
          v-model:page-size="paginationData.pageSize"
          :total="paginationData.total"
          layout="total, prev, pager, next, sizes, jumper"
          :page-sizes="[8, 12, 16, 20]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </template>
  </div>
</template>

<script setup>
import { deleteTempProduct, getTempProductList } from '@/api/ecology/temp-product.js'
import emptyImage from '@/assets/images/empty-image.png'
import { toFixedAccurate } from '@/utils/math.js'

const { type } = defineProps({
  type: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['resetSearch', 'editItem'])

watch(() => type, () => {
  if (type) {
    getListData()
  }
})

const { proxy } = getCurrentInstance()

const router = useRouter()
const formRef = useTemplateRef('searchFormRef')

const searchData = reactive({
  partnerName: '',
  productName: '',
  tag: '',
  productType: '临时产品',
})
const loading = ref(false)
const listData = ref([])
const paginationData = reactive({
  pageNum: 1,
  pageSize: 8,
  total: 0,
})

function handleSearch() {
  paginationData.pageNum = 1
  getListData()
}

function handleReset() {
  formRef.value.resetFields()
  emit('resetSearch')
  nextTick(() => {
    handleSearch()
  })
}

async function getListData() {
  try {
    loading.value = true
    const res = await getTempProductList({
      types: type ? [type] : [],
      ...searchData,
      ...paginationData,
    })

    listData.value = res.data.records.map((item) => {
      return {
        ...item,
        partnerClass: typeof item.partnerClass === 'string' ? JSON.parse(item.partnerClass) : item.partnerClass,
      }
    })
    paginationData.total = res.data.total
    paginationData.pageNum = res.data.current
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

function handleClick(item) {
  router.push({
    path: '/ecology/temp-product/detail',
    query: {
      productBid: item.bid,
    },
  })
}

function handleEdit(item) {
  emit('editItem', item)
}

function handleDelete(item) {
  proxy.$modal.confirm('确认删除该数据吗？').then(() => {
    return deleteTempProduct({
      productBid: item.bid,
    })
  }).then(() => {
    proxy.$modal.msgSuccess('删除成功')
    getListData()
  }).catch(() => {
    proxy.$modal.msg('已取消')
  })
}

const productPriceOptions = [
  {
    label: '标准产品包',
    value: 'productPackage',
  },
  {
    label: '产品实施',
    value: 'productImplementation',
  },
  {
    label: '产品定制化开发',
    value: 'productDevelopment',
  },
]

const showTagsNum = 2
function showTags(item) {
  return item.partnerClass.slice(0, showTagsNum)
}
function getTagType(index) {
  return ['warning', 'primary', 'success'][index % 3]
}
function joinedTags(item) {
  return item.partnerClass.slice(showTagsNum, item.partnerClass.length).join('、')
}
function formatPrice(value) {
  const num = Number(value)
  if (Number.isNaN(num)) {
    return '0.00'
  }
  if (Number.isInteger(num)) {
    return num.toFixed(2)
  }
  return toFixedAccurate(num)
}

function handleSizeChange(size) {
  paginationData.pageSize = size
  handleCurrentChange(1)
}

function handleCurrentChange(page) {
  paginationData.pageNum = page
  getListData()
}

onMounted(() => {
  getListData()
})

defineExpose({
  getListData,
})
</script>

<style scoped lang="scss">
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  &_items {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 20px;
    align-content: flex-start;
    overflow: auto;

    .product {
      width: 339px;
      height: 248px;
      padding: 18px;
      border: 1px solid rgb(0 0 0 / 6%);
      border-radius: 8px;
      background: #fff;
      cursor: pointer;

      &_header {
        display: flex;
        justify-content: space-between;

        &_icon {
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 36px;
          height: 36px;

          & > i {
            font-size: 36px;
          }

          &_sub {
            position: absolute;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #fd0;
            color: #fff;

            & > i {
              font-size: 8px;
            }
          }
        }
      }

      &_name {
        margin-top: 12px;
        color: rgb(0 0 0 / 88%);
        font-weight: 600;
        font-size: 16px;
      }

      &_tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 6px;
      }

      &_partner {
        margin-top: 12px;
        color: rgb(0 0 0 / 45%);
        font-size: 12px;
        line-height: 20px;

        span {
          text-decoration: underline;
        }
      }

      &_price {
        overflow: hidden;
        margin-top: 4px;
        color: rgb(0 0 0 / 45%);
        font-size: 12px;
        line-height: 20px;
        text-overflow: ellipsis;
        white-space: nowrap;

        & > span {
          color: rgb(0 0 0 / 88%);
        }
      }

      &_time {
        margin-top: 12px;
        color: rgb(0 0 0 / 25%);
      }
    }
  }

  &_pagination {
    display: flex;
    justify-content: flex-end;
    padding: 18px 0;
  }
}

:deep(.el-pager li.is-active) {
  border: 1px solid;
  border-radius: 6px;
}
</style>
