<template>
  <ElDialog
    v-model="model"
    class="custom_project_dialog"
    @open="onOpen">
    <template #header>
      <Title>项目列表</Title>
    </template>
    <ElPopover
      placement="bottom-start"
      trigger="click"
      width="calc(50% - 30px)"
      :hide-after="0"
      :show-arrow="false">
      <template #reference>
        <ElButton>
          <i
            class="iconfont icon-sift"
            :style="{
              marginRight: '8px',
            }" />
          所有筛选
          <ElIcon
            :style="{
              marginLeft: '10px',
            }">
            <ArrowDown />
          </ElIcon>
        </ElButton>
      </template>
      <div style="padding: 20px 4px;">
        <ElForm
          ref="formRef"
          label-width="100"
          label-position="left"
          :model="form">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="项目名称">
                <ElInput v-model="form.projectName" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="项目编号">
                <ElInput v-model="form.projectCode" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="客户名称">
                <ElInput v-model="form.customerName" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="市场经理">
                <ElInput
                  v-model="form.marketManagerName" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="解决方案经理">
                <ElInput
                  v-model="form.solutionManagerName" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <div class="flex justify-end items-center ">
                <ElButton @click="onReset">
                  重置
                </ElButton>
                <ElButton
                  type="primary"
                  @click="onSearch">
                  查询
                </ElButton>
              </div>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>
    </ElPopover>
    <ElTable
      v-loading="loading"
      style="margin-top: 20px;"
      :data="tableData"
      highlight-current-row
      max-height="calc(70vh - 100px)"
      @current-change="handleCurrentChange">
      <ElTableColumn
        label="项目名称"
        prop="projectName" />
      <ElTableColumn
        label="项目编号"
        prop="projectCode" />
      <ElTableColumn
        label="客户名称"
        prop="customerName" />
      <ElTableColumn
        label="项目经理"
        prop="outputProjectManagerName" />
      <ElTableColumn
        label="市场经理"
        prop="marketManagerName" />
      <ElTableColumn
        label="解决方案经理"
        prop="solutionManagerName" />
    </ElTable>
    <div style="display: flex; justify-content: flex-end;margin-top: 10px;">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onSearch" />
    </div>
    <template #footer>
      <ElButton @click="onCancel">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="onConfirm">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import apis from '@/api/presales/bid'
import Title from '@/components/Title'
import { usePagination } from '@/utils/hooks'
import { toNumber } from '@/utils/math'
import { ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn } from 'element-plus'
import { readonly, ref, unref, useTemplateRef } from 'vue'

const emit = defineEmits(['checked'])

const loading = ref(false)

const formRef = useTemplateRef('formRef')
const templateFormData = {
  projectName: '',
  projectCode: '',
  customerName: '',
  marketManagerName: '',
  solutionManagerName: '',
}
const form = ref({ ...templateFormData })
const tableData = ref([])
const model = defineModel()
const { pagination } = usePagination()
async function onSearch() {
  loading.value = true
  try {
    const res = await apis.log.getSuccessAndOnlyPreEstProject({ ...form.value, ...pagination })
    const { list, total, records } = res.data
    tableData.value = list || records || []
    pagination.total = toNumber(total)
  } catch (error) {
    console.log(error)
  }
  loading.value = false
}
const project = ref({})
function handleCurrentChange(val) {
  project.value = val
}
function onCancel() {
  model.value = false
}
function onConfirm() {
  emit('checked', readonly(unref(project)))
  onCancel()
}
function onOpen() {
  // 打开弹窗时重置查询状态
  onReset()
}
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  pagination.pageNum = 1
  onSearch()
}
function onReset() {
  formRef.value.resetFields()
  form.value = { ...templateFormData }
  pagination.pageNum = 1
  onSearch()
}
</script>

<style lang="scss">
.custom_project_dialog header {
  padding-bottom: 0;
}
</style>
