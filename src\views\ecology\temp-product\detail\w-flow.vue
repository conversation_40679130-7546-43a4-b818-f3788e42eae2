<template>
  <div class="w-flow">
    <DetailRender
      :id="id"
      ref="detailRenderRef" />
  </div>
</template>

<script setup>
import DetailRender from './render.vue'

const { id } = defineProps({
  id: {
    type: String,
    default: '',
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  instanceId: {
    type: String,
    default: '',
  },
  forms: {
    type: Array,
    default: () => [],
  },
})

const detailRenderRef = useTemplateRef('detailRenderRef')

onMounted(() => {
  detailRenderRef.value.init()
})

defineExpose({
  getFormData: () => {
    return {}
  },
  saveFormData: () => {
    return Promise.resolve()
  },
})
</script>

<style lang="scss" scoped>

</style>
