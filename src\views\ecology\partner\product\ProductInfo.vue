<template>
  <div class="tab-container">
    <ElForm
      ref="formRef"
      :model="form"
      :rules="partnerSearchRules"
      inline>
      <ElFormItem
        label="生态合作伙伴:"
        prop="partnerName">
        <ElInput
          v-model="form.partnerName"
          clearable
          style="width: 240px;"
          placeholder="输入合作伙伴名称查询" />
      </ElFormItem>
      <ElFormItem
        label="产品名称:"
        prop="productName">
        <ElInput
          v-model="form.productName"
          clearable
          style="width: 240px;"
          placeholder="输入产品名称查询" />
      </ElFormItem>
      <ElFormItem
        label="标签:"
        prop="tag">
        <ElInput
          v-model="form.tag"
          clearable
          style="width: 240px;"
          placeholder="输入标签名称查询" />
      </ElFormItem>
      <ElFormItem>
        <ElButton
          type="primary"
          @click="handleSearch">
          查询
        </ElButton>
        <ElButton
          type="default"
          @click="handleReset">
          重置
        </ElButton>
      </ElFormItem>
    </ElForm>

    <div
      class="procuct-list">
      <div
        v-for="item in productList"
        :key="item.productBid"
        class="procuct-cart"
        @click="router.push(`/ecology/partner/product/detail?productBid=${item.productBid}`)">
        <div class="header">
          <div class="header-top">
            <SvgIcon
              icon-class="sthb_product"
              style=" margin-right: 8px;font-size: 37px;" />
            <div style="display: flex; flex-direction: column;">
              <h3>{{ item.productName }}</h3>
              <div style="display: flex; flex-wrap: wrap; margin-top: 5px;">
                <!-- 循环显示前showTagsNum个标签 -->
                <div
                  v-for="(tag, index) in showTags(item)"
                  :key="index"
                  style="display: flex; margin-right: 6px; margin-bottom: 6px;">
                  <ElTag :type="getTagType(item, index)">
                    {{ tag }}
                  </ElTag>
                </div>
                <!-- 当总标签数超过showTagsNum个时，显示剩余数量 -->
                <div
                  v-if="item.totalTags.length > showTagsNum"
                  style="display: flex; margin-right: 6px; margin-bottom: 6px;">
                  <ElTooltip
                    placement="top">
                    <ElTag type="info">
                      +{{ item.totalTags.length - showTagsNum }}
                    </ElTag>
                    <template #content>
                      <span>
                        {{ joinedTags(item) }}
                      </span>
                    </template>
                  </ElTooltip>
                </div>
              </div>
            </div>
          </div>
          <div class="partner-info">
            生态伙伴：<span style="text-decoration: underline;">{{ item.companyName }}</span>
          </div>
        </div>
        <div class="footer">
          <div class="item">
            <div
              v-for="product in productPriceOptions"
              :key="product.value"
              class="item-price">
              <ElTooltip
                class="box-item"
                effect="dark"
                placement="top">
                <template #default>
                  <div class="trigger-text">
                    <div
                      class="text">
                      ￥{{ formatPrice(item[product.value]) }}
                    </div>
                    <div class="label">
                      {{ product.label }}
                    </div>
                  </div>
                </template>
                <template #content>
                  ￥{{ formatPrice(item[product.value]) }}
                </template>
              </ElTooltip>
            </div>
          </div>
        <!-- <div class="time">
          <i
            class="iconfont icon-ClockCircleOutlined"
            style="margin-right: 2px;font-size: 11px;" />
          <span>2023-01-01</span>
        </div> -->
        </div>
      </div>
    </div>
    <div class="pagination">
      <ElPagination
        :current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[8, 12, 16, 20]"
        layout="total, prev, pager, next, sizes, jumper"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>
  </div>
</template>

<script setup>
import * as partnerApi from '@/api/ecology/partner.js'
import { toFixedAccurate } from '@/utils/math.js'
import { partnerSearchRules } from '@/views/ecology/partner/rules.js'

const router = useRouter()

const formRef = ref(null)

// 伙伴产品搜索表单
const form = ref({
  type: '',
  partnerName: '',
  productName: '',
  tag: '',
})
const pagination = reactive({
  pageNum: 1,
  pageSize: 8,
  total: 0,
})
/**
 * 伙伴列表
 */
const productList = ref([])

// 当前分类数节点
const currentNodeKey = defineModel('currentNodeKey')

/** 显示标签的数据 */
const showTagsNum = ref(2)
function showTags(item) {
  return item.totalTags.slice(0, showTagsNum.value)
}
// 根据索引确定标签类型
function getTagType(item, index) {
  const primaryLength = item.primaryTags.length
  const secondaryLength = item.secondaryTags.length
  if (index < primaryLength) {
    return 'warning'
  } else if (index < primaryLength + secondaryLength) {
    return 'primary'
  } else {
    return 'success'
  }
}
function joinedTags(item) {
  const tagsToJoin = item.totalTags.slice(showTagsNum.value, item.totalTags.length)
  return tagsToJoin.join('、')
}
// 获取伙伴产品列表
async function getPartnerProductList() {
  try {
    const res = await partnerApi.getPartnerProductList({
      ...form.value,
      ...pagination,
    })
    productList.value = res.data.records.map((item) => {
      const parsedClass = JSON.parse(item.partnerClass) || []
      // 一级标签
      const primaryTags = []
      const secondaryTags = []
      const tertiaryTags = []
      const totalTags = []
      parsedClass.forEach((subItem) => {
        if (typeof subItem === 'object' && subItem !== null) {
          if (subItem[0]) {
            if (!primaryTags.includes(subItem[0])) {
              primaryTags.push(subItem[0])
              totalTags.push(subItem[0])
            }
          }
          if (subItem[1]) {
            if (!secondaryTags.includes(subItem[1])) {
              secondaryTags.push(subItem[1])
              totalTags.push(subItem[1])
            }
          }
          if (subItem[2]) {
            tertiaryTags.push(subItem[2])
            totalTags.push(subItem[2])
          }
        } else {
          totalTags.push(subItem)
          if (!primaryTags.includes(totalTags[0]))
            primaryTags.push(totalTags[0])
          if (!secondaryTags.includes(totalTags[1]))
            secondaryTags.push(totalTags[1])
          if (!tertiaryTags.includes(totalTags[2]))
            tertiaryTags.push(totalTags[2])
        }
      })

      return {
        ...item,
        primaryTags,
        secondaryTags,
        tertiaryTags,
        totalTags,
      }
    })
    pagination.total = +res.data.total
  } catch (error) {
    console.log(error)
  }
}
// 价格格式化
function formatPrice(value) {
  const num = Number(value)
  if (Number.isNaN(num)) {
    return '0.00'
  }
  if (Number.isInteger(num)) {
    return num.toFixed(2)
  }
  return toFixedAccurate(num)
}
// 搜索
function handleSearch() {
  pagination.pageNum = 1
  getPartnerProductList()
}

// 重置搜索表单
function handleReset() {
  pagination.pageNum = 1
  form.value.type = undefined
  currentNodeKey.value = undefined
  formRef.value.resetFields()
  getPartnerProductList()
}

function handleCurrentChange(value) {
  pagination.pageNum = value
  getPartnerProductList()
}
function handleSizeChange(value) {
  pagination.pageSize = value
  getPartnerProductList()
}

defineExpose({
  form,
  pagination,
  getPartnerProductList,
})
const productPriceOptions = [
  {
    label: '标准产品包',
    value: 'productPackage',
  },
  {
    label: '产品实施',
    value: 'productImplementation',
  },
  {
    label: '产品定制化开发',
    value: 'productDevelopment',
  },
]
</script>

<style scoped lang="scss">
.tab-container {
  display: flex;
  flex: 1;
  flex-direction: column;

  .procuct-list {
    display: grid;
    flex: 1;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    overflow: auto;

    .procuct-cart {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-width: 0;
      max-height: 200px;
      padding: 18px;
      border: 1px solid rgb(0 0 0 / 6%);
      border-radius: 8px;
      background: #fff;
      cursor: pointer;

      .header {
        display: flex;
        flex: 1;
        flex-direction: column;

        .header-top {
          display: flex;
          align-items: center;

          .iconfont {
            margin-right: 8px;
            font-size: 37px;
          }

          h3 {
            color: rgb(0 0 0 / 88%);
            font-weight: 600;
            font-size: 16px;
          }
        }

        .partner-info {
          margin-top: 13px;
          color: rgb(0 0 0 / 45%);
          font-size: 12px;
          line-height: 20px;

          span {
            text-decoration: underline;
          }
        }
      }

      .footer {
        display: flex;
        height: 56px;
        color: rgb(0 0 0 / 45%);
        font-size: 12px;

        .item {
          display: flex;
          flex: 1;
          gap: 8px;
          justify-content: center;
          overflow: hidden;

          .item-price {
            overflow: hidden;
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            background: rgb(0 0 0 / 2%);

            .trigger-text {
              display: flex;
              flex-direction: column;

              // justify-content: center;

              .text {
                overflow: hidden;
                color: rgb(0 0 0 / 88%);
                font-size: 16px;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }

        .time {
          margin-top: 26px;
          color: rgb(0 0 0 / 25%);
        }
      }
    }
  }

  .pagination {
    display: flex;
    flex: 0 0 62px;
    justify-content: flex-end;
    align-items: center;
  }
}

:deep(.el-pager li.is-active) {
  border: 1px solid;
  border-radius: 6px;
}
</style>
