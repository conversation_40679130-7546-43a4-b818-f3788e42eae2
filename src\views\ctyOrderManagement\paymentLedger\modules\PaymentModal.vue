<template>
  <ElDialog
    v-model="modalVisible"
    title="付款明细"
    width="900px"
    @close="close">
    <ElForm
      ref="formRef"
      :inline="true"
      :model="form"
      :rules="rules"
      label-position="left"
      label-width="140px">
      <ElFormItem
        label="付款日期"
        class="form-item"
        prop="paymentDate">
        <ElDatePicker
          v-model="form.paymentDate"
          type="date"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择付款日期" />
      </ElFormItem>
      <ElFormItem
        label="付款金额(元)"
        class="form-item"
        prop="amount">
        <ElInputNumber
          v-model="form.amount"
          controls-position="right"
          :precision="2"
          style="width: 100%"
          placeholder="请输入" />
      </ElFormItem>
      <ElFormItem
        label="付款人"
        class="form-item"
        prop="operator">
        <ElSelect
          v-model="form.operatorName"
          placeholder="请选择"
          @visible-change="
            (change) => change && (orgPickerRef.show())
          " />
      </ElFormItem>
      <ElFormItem
        label="付款状态"
        class="form-item"
        prop="status">
        <ElSelect
          v-model="form.status"
          placeholder="请选择">
          <ElOption
            label="未付款"
            value="未付款" />
          <ElOption
            label="待付款"
            value="待付款" />
          <ElOption
            label="已付款"
            value="已付款" />
          <ElOption
            label="有争议暂不付款"
            value="有争议暂不付款" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem
        label="备注"
        class="form-item"
        prop="remark">
        <ElInput
          v-model="form.remark"
          :rows="2"
          type="textarea"
          placeholder="请输入" />
      </ElFormItem>
    </Elform>
    <template #footer>
      <ElButton @click="close">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="submit">
        确认
      </ElButton>
    </template>
    <OrgPicker
      ref="orgPickerRef"
      type="user"
      :multiple="false"
      title="选择付款人"
      @ok="handleSelectedPayer" />
  </ElDialog>
</template>

<script setup>
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'

const emit = defineEmits(['add', 'edit'])
const modalVisible = ref(false)
let type = 'add'
const formRef = ref()
const form = ref({})
const rules = ref({
  paymentDate: [
    { required: true, message: '请选择付款日期', trigger: 'blur' },
  ],
  amount: [
    { required: true, message: '请输入付款金额', trigger: 'blur' },
  ],
  operator: [
    { required: true, message: '请选择付款人', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择付款状态', trigger: 'blur' },
  ],

})

const orgPickerRef = ref()
function handleSelectedPayer(val) {
  if (val && val.length > 0) {
    form.value.operatorName = val[0].name
    form.value.operator = val[0].id
  } else {
    form.value.operatorName = ''
    form.value.operator = ''
  }
}

function add(row) {
  modalVisible.value = true
  type = 'add'
  form.value = {
    ...row,
  }
}

function edit(row) {
  add(row)
  type = 'edit'
}

function close() {
  modalVisible.value = false
  form.value = {}
}

function submit() {
  formRef.value.validate((valid) => {
    if (valid) {
      console.log(form.value)
      if (type === 'add') {
        // 新增
        console.log('新增')
        emit('add', form.value)
      } else {
        // 编辑
        console.log('编辑')
        emit('edit', form.value)
      }
      close()
    }
  })
}

defineExpose({
  add,
  edit,
})
</script>

  <style lang="scss" scoped>
  .form-item {
  width: 360px;
}
</style>
