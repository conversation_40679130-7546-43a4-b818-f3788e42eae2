<template>
  <Container show-back>
    <div class="wrapper">
      <SideBar
        :tab-item="tabs"
        :active-tab="activeTab"
        @change="tab => activeTab = tab" />
      <div class="content">
        <BaseInfo
          v-if="activeTab === '基本信息'"
          :project-code="projectCode" />
        <ChangeRecord
          v-else-if="activeTab === '变更记录'"
          :project-code="projectCode" />
        <AcceptanceRecord
          v-else-if="activeTab === '验收记录'"
          :project-code="projectCode" />
        <OutsourcedPerson
          v-else-if="activeTab === '外包人员'"
          :project-code="projectCode" />
        <WorkHourDetail v-else-if="activeTab === '工时明细'" />
        <PurchaseContract v-else-if="activeTab === '采购合同'" />
        <RelatedProject
          v-else-if="activeTab === '相关项目'"
          :project-code="projectCode" />
      </div>
    </div>
  </Container>
</template>

<script setup>
import Container from '@/components/Container/index.vue'
import { useRoute } from 'vue-router'
import AcceptanceRecord from './acceptanceRecord.vue'
import BaseInfo from './base-info/index.vue'
import ChangeRecord from './changeRecord.vue'
import SideBar from './components/sideBar.vue'
import OutsourcedPerson from './outsourcedPerson.vue'
import PurchaseContract from './purchaseContract.vue'
import RelatedProject from './relatedProject.vue'
import WorkHourDetail from './workHourDetail.vue'

const tabs = [
  '基本信息',
  '变更记录',
  '验收记录',
  '外包人员',
  '工时明细',
  '采购合同',
  '相关项目',
]

const route = useRoute()

const projectCode = computed(() => {
  return route.query.projectCode
})

const activeTab = ref(tabs[0])
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex: auto;
  overflow-y: auto;

  .content {
    display: flex;
    flex: auto;
    flex-direction: column;
    overflow-x: hidden;
    margin-left: 10px;
    border-radius: 10px;
    background-color: #fff;
  }
}
</style>
