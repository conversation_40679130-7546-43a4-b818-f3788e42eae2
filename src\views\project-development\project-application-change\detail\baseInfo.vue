<template>
  <Title style="margin-top: 20px;">
    项目基本信息（变更前）
  </Title>
  <ElDescriptions
    border
    :column="4">
    <ElDescriptionsItem
      label="项目名称"
      :span="2">
      {{ form.prdProjectHistory.prdProjectName }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="项目编号"
      :span="1">
      {{ form.prdProjectHistory.prdProjectCode }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发负责人"
      :span="1">
      {{ form.prdProjectHistory.principal }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="申请日期">
      {{ form.prdProjectHistory.submitTime }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="业务主体">
      {{ form.prdProjectHistory.prdEntityDept }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分类">
      {{ form.prdProjectHistory.prdProjectCategory }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="研发方式">
      {{ form.prdProjectHistory.rdMethod }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分级">
      {{ form.prdProjectHistory.projectClassification }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="立项日期">
      {{ form.prdProjectHistory.projectInitiationDate }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分管领导">
      {{ form.prdProjectHistory.projectLeader }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发项目基本情况"
      :span="4">
      {{ form.prdProjectHistory.projectBasicInformation }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="附件"
      :span="4">
      <template v-if="form.prdProjectHistory.attachments">
        <div
          v-for="(item, index) in form.prdProjectHistory.attachments"
          :key="index"
          style="display: flex; align-items: center;">
          <ElIcon>
            <Document />
          </ElIcon>
          <span style="padding: 2px 0 0 2px;">
            {{ item.match(/[^/\\?#]+$/)[0] }}
          </span>
          <ElButton
            link
            type="primary"
            style="margin-left: 8px;"
            @click="handleDownload(item)">
            下载
          </ElButton>
        </div>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
  <Title style="margin-top: 20px;">
    项目基本信息（变更后）
  </Title>
  <ElDescriptions
    border
    :column="4">
    <ElDescriptionsItem
      label="项目名称"
      :span="2">
      {{ form.projectChangeDraft.prdProjectName }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="项目编号"
      :span="1">
      {{ form.projectChangeDraft.prdProjectCode }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发负责人"
      :span="1">
      {{ form.projectChangeDraft.principal }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="申请日期">
      {{ form.projectChangeDraft.submitTime }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="业务主体">
      {{ form.projectChangeDraft.prdEntityDept }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分类">
      {{ form.projectChangeDraft.prdProjectCategory }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="研发方式">
      {{ form.projectChangeDraft.rdMethod }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分级">
      {{ form.projectChangeDraft.projectClassification }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="立项日期">
      {{ form.projectChangeDraft.projectInitiationDate }}
    </ElDescriptionsItem>
    <ElDescriptionsItem label="项目分管领导">
      {{ form.projectChangeDraft.projectLeader }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="研发项目基本情况"
      :span="4">
      {{ form.projectChangeDraft.projectBasicInformation }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="附件"
      :span="4">
      <template v-if="form.projectChangeDraft.attachments">
        <div
          v-for="(item, index) in form.projectChangeDraft.attachments"
          :key="index"
          style="display: flex; align-items: center;">
          <ElIcon>
            <Document />
          </ElIcon>
          <span style="padding: 2px 0 0 2px;">
            {{ item.match(/[^/\\?#]+$/)[0] }}
          </span>
          <ElButton
            link
            type="primary"
            style="margin-left: 8px;"
            @click="handleDownload(item)">
            下载
          </ElButton>
        </div>
      </template>
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const form = defineModel()

// 自定义附件下载逻辑
function handleDownload(address) {
  const link = document.createElement('a')
  link.href = address
  // 设置下载文件名
  link.download = address.match(/[^/\\?#]+$/)[0]
  document.body.appendChild(link)
  link.click()
  // 清理 DOM
  document.body.removeChild(link)
}
</script>

<style lang="scss" scoped>

</style>
