<template>
  <Container show-back>
    <div class="wrapper">
      <ElForm
        ref="formRef"
        :rules="rules"
        :model="formData.baseInfo"
        label-position="top">
        <ProjectChangeDescription
          v-model="formData"
          :reset-form="resetForm" />
        <BaseInfo v-model="formData" />
        <BudgetInfo v-model="formData" />
        <OwnProductsAndServices v-model="formData" />
        <DevProjectRevenueForecast v-model="formData" />
      </ElForm>
    </div>
    <template #headerRight>
      <div style="display: flex;flex: auto; justify-content: flex-end;">
        <ElButton
          type="primary"
          plain
          @click="temporarilyStoreForm">
          暂存
        </ElButton>
        <ElButton
          type="primary"
          @click="submitForm">
          提交
        </ElButton>
      </div>
    </template>
  </Container>
</template>

<script setup>
import { getApplicationChangeDetailByBid, startChange } from '@/api/project-development/project-application-change.js'
import * as wFlowApis from '@/api/wflow-pro.js'
import Container from '@/components/Container/index.vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import BaseInfo from './baseInfo.vue'
import BudgetInfo from './budgetInfo.vue'
import DevProjectRevenueForecast from './devProjectRevenueForecast.vue'
import OwnProductsAndServices from './ownProductsAndServices.vue'
import ProjectChangeDescription from './projectChangeDescription.vue'

const props = defineProps({
  id: {
    default: '',
  },
})

const route = useRoute()

const router = useRouter()

const formRef = useTemplateRef('formRef')

const formData = ref({
  baseInfo: {
    projectChangeDraft: {
      budgetInfo: { // 项目预算信息
        outsourcingCost: [], // 外采成本
        jobLaborCost: [], // 人力成本
        manageCost: [], // 项目管理费
        budgetOverview: { // 预算概览
          manageCost: [],
        },
      },
      selfOwnedProductsServicesDetail: [], // 自有产品服务明细
      incomeDetails: [], // 研发项目收益预测
    },
  },
})

const rules = reactive({
  'changeType': [{ required: true, message: '请选择变更类型', trigger: 'change' }],
  'isImportantModification': [{ required: true, message: '请选择', trigger: 'change' }],
  'changeReason': [{ required: true, message: '请输入变更理由', trigger: 'change' }],
  'changeBeforeContent': [{ required: true, message: '请输入变更前内容', trigger: 'change' }],
  'changeAfterContent': [{ required: true, message: '请输入变更后内容', trigger: 'change' }],
  'changeImpactAnalysis': [{ required: true, message: '请输入变更影响分析', trigger: 'change' }],
  'projectChangeDraft.prdProjectName': [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  'projectChangeDraft.principal': [{ required: true, message: '请选择项目名称', trigger: 'change' }],
  'projectChangeDraft.prdEntityDept': [{ required: true, message: '请选择业务主体', trigger: 'change' }],
  'projectChangeDraft.prdProjectCategory': [{ required: true, message: '请选择项目分类', trigger: 'change' }],
  'projectChangeDraft.businessType': [{ required: true, message: '请选择业务类别', trigger: 'change' }],
  'projectChangeDraft.rdMethod': [{ required: true, message: '请选择研发方法', trigger: 'change' }],
  'projectChangeDraft.projectInitiationDate': [{ required: true, message: '请选择立项日期', trigger: 'change' }],
  'projectChangeDraft.projectLeader': [{ required: true, message: '请选择项目分管领导', trigger: 'change' }],
  'projectChangeDraft.projectBasicInformation': [{ required: true, message: '请输入项目研发基本情况', trigger: 'change' }],
  'projectChangeDraft.fileUrlList': [{ required: true, message: '请上传附件', trigger: 'change' }],
})

async function temporarilyStoreForm() {
  formData.value.baseInfo.projectChangeDraft.prdProjectCategory = Array.isArray(formData.value.baseInfo.projectChangeDraft.prdProjectCategory) ? formData.value.baseInfo.projectChangeDraft.prdProjectCategory.splice(-1)[0] : formData.value.baseInfo.projectChangeDraft.prdProjectCategory
  // 附件
  formData.value.baseInfo.projectChangeDraft.attachments = formData.value.baseInfo.projectChangeDraft.fileUrlList ? formData.value.baseInfo.projectChangeDraft.fileUrlList.map(item => item.url || item.response.data) : []
  formData.value.options = 0 // 0表示暂存
  const res = await startChange(formData.value)
  if (res.code === 200) {
    ElMessage.success('暂存成功')
    router.push('/project-development/project-application-change/list')
  }
}

function submitForm() {
  formRef.value.validate().then(async () => {
    formData.value.baseInfo.projectChangeDraft.prdProjectCategory = Array.isArray(formData.value.baseInfo.projectChangeDraft.prdProjectCategory) ? formData.value.baseInfo.projectChangeDraft.prdProjectCategory.splice(-1)[0] : formData.value.baseInfo.projectChangeDraft.prdProjectCategory
    // 附件
    formData.value.baseInfo.projectChangeDraft.attachments = formData.value.baseInfo.projectChangeDraft.fileUrlList ? formData.value.baseInfo.projectChangeDraft.fileUrlList.map(item => item.url || item.response.data) : []
    formData.value.options = 1 // 1表示提交
    const processDefId = await wFlowApis.getCurrentProcessId('development-project-application-change')
    formData.value.processDefId = processDefId
    const res = await startChange(formData.value)
    if (res.code === 200) {
      ElMessage.success('提交成功')
      router.push('/project-development/project-application-change/list')
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

function resetForm() {
  formRef.value.resetFields()
}

onMounted(async () => {
  if (route.query.type === 'edit' && route.query.id) {
    const res = await getApplicationChangeDetailByBid(route.query.id)
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.projectChangeDraft.fileUrlList = res.projectChangeDraft.attachments.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  } else if (props.id) {
    const res = await getApplicationChangeDetailByBid(route.query.id)
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.projectChangeDraft.fileUrlList = res.projectChangeDraft.attachments.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  }
})

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(formData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve(readonly(unref(formData)))
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.wrapper {
  flex: auto;
  overflow-y: auto;
  padding: 10px 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
