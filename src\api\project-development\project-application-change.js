import { get, post } from '@/utils/alova'

/**
 * 获取立项变更分页列表
 */
export function getProjectApplicationChangePageList(data, config) {
  return post('/project/prd/project/change/pageList', data, config)
}

/**
 * 根据编码获取变更详情
 */
export function getApplicationChangeDetailByCode(prdProjectCode, config) {
  return get(`/project/prd/project/change/startDetail/${prdProjectCode}`, null, config)
}

/**
 * 根据bid获取变更详情
 */
export function getApplicationChangeDetailByBid(changeBid, config) {
  return get(`/project/prd/project/change/queryDetail/${changeBid}`, null, config)
}

/**
 * 发起变更
 */
export function startChange(data, config = { transformRes: false }) {
  return post('/project/prd/project/change/start', data, config)
}

/**
 * 删除
 */
export function deleteChange(changeBid, config) {
  return post(`/project/prd/project/deleted/${changeBid}`, null, config)
}
