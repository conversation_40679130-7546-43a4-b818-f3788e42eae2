<template>
  <Title>项目结项说明</Title>
  <ElRow :gutter="30">
    <ElCol :span="24">
      <ElFormItem
        label="结项情况说明"
        prop="completionExplain">
        <ElInput
          v-model="formData.baseInfo.completionExplain"
          :rows="2"
          type="textarea"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        ref="uploadRef"
        label="附件"
        prop="completionExplainAttachment">
        <ElUpload
          v-model:file-list="formData.baseInfo.completionExplainAttachment"
          style="width: 100%; padding-top: 20px;"
          :action="uploadUrl"
          :on-preview="on_preview_or_downFile"
          auto-upload
          :on-success="onFileUploadSuccess"
          :on-error="onFileUploadFail"
          drag
          multiple>
          <i
            style="color: #1677FF; font-size: 28px;"
            class="iconfont icon-InboxOutlined" />
          <h3>拖拽或点击文件上传</h3>
          <div class="el-upload__tip">
            支持PPT、PPTX、PDF、DOC、DOCX、TXT、ZIP、JPG等通用格式
          </div>
        </ElUpload>
      </ElFormItem>
    </ElCol>
  </ElRow>
</template>

<script setup>
import Title from '@/components/Title/index.vue'
import { on_preview_or_downFile } from '@/utils/hooks.js'
import { ElMessage } from 'element-plus'

const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址

const formData = defineModel()

const uploadRef = useTemplateRef('uploadRef')

function onFileUploadSuccess() {
  uploadRef.value.clearValidate()
  ElMessage.success('上传附件成功')
}

function onFileUploadFail() {
  ElMessage.error('上传附件失败')
}
</script>

<style lang="scss" scoped>

</style>
