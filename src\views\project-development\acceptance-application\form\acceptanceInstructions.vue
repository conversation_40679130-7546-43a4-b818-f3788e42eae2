<template>
  <Title>项目验收说明</Title>
  <ElRow :gutter="30">
    <ElCol :span="7">
      <ElFormItem
        label="验收类型"
        prop="checkType">
        <ElRadioGroup v-model="formData.baseInfo.checkType">
          <ElRadio
            value="初验"
            size="large">
            初验
          </ElRadio>
          <ElRadio
            value="终验"
            size="large">
            终验
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElCol>
    <ElCol :span="7">
      <ElFormItem
        label="是否用印"
        prop="isUseSeal">
        <ElRadioGroup v-model="formData.baseInfo.isUseSeal">
          <ElRadio
            value="是"
            size="large">
            是
          </ElRadio>
          <ElRadio
            value="否"
            size="large">
            否
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElCol>
    <ElCol
      v-if="formData.baseInfo.isUseSeal === '是'"
      :span="10">
      <ElFormItem
        label="选择印章"
        prop="sealBids">
        <ElSelect
          v-model="formData.baseInfo.sealBids"
          placeholder="请选择"
          multiple>
          <ElOption
            v-for="item in sealList"
            :key="item.bid"
            :label="item.sealName"
            :value="item.bid" />
        </ElSelect>
      </ElFormItem>
    </ElCol>
    <ElCol :span="10">
      <ElFormItem label="验收采购合同名称">
        <ElSelect
          v-model="formData.baseInfo.contractName"
          placeholder="请选择"
          @visible-change="change => change && openCghtTableDialog()" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="7">
      <ElFormItem label="验收采购合同编号">
        <ElInput
          v-model="formData.baseInfo.contractCode"
          disabled
          placeholder="选择合同后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="7">
      <ElFormItem label="验收采购合同含税金额（元）">
        <ElInput
          v-model="formData.baseInfo.taxAmount"
          disabled
          placeholder="选择合同后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol>
      <ElTable
        border
        :data="formData.baseInfo.contractOtherSubjects">
        <ElTableColumn
          label="对方主体名称"
          prop="merchantName" />
        <ElTableColumn
          label="对方主体身份"
          prop="identity" />
        <ElTableColumn
          label="银行账户"
          prop="account" />
      </ElTable>
    </ElCol>
    <ElCol
      :span="24"
      style="margin-top: 10px;">
      <ElFormItem
        label="验收情况说明"
        prop="checkAcceptFactSheet">
        <ElInput
          v-model="formData.baseInfo.checkAcceptFactSheet"
          type="textarea"
          :rows="2"
          placeholder="请说明项目整体完成情况，包括研发过程是否规范、产品功能是否完整、非功能需求是否实现、项目资料是否完整、产品质量是否符合要求、产品研发费用执行情况、知识产权落实情况等" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem
        ref="uploadRef"
        label="附件"
        prop="fileUrlList">
        <ElUpload
          v-model:file-list="formData.baseInfo.fileUrlList"
          style="width: 100%; padding-top: 20px;"
          :action="uploadUrl"
          :on-preview="on_preview_or_downFile"
          auto-upload
          :on-success="onFileUploadSuccess"
          :on-error="onFileUploadFail"
          drag
          multiple>
          <i
            style="color: #1677FF; font-size: 28px;"
            class="iconfont icon-InboxOutlined" />
          <h3>拖拽或点击文件上传</h3>
          <div class="el-upload__tip">
            支持PPT、PPTX、PDF、DOC、DOCX、TXT、ZIP、JPG等通用格式
          </div>
        </ElUpload>
      </ElFormItem>
    </ElCol>
  </ElRow>
  <CghtTableDialog
    v-model="isShowCghtTableDialog"
    :project-code="formData.baseInfo.prdProjectCode"
    @associate="onAssociate" />
</template>

<script setup>
import { getPsmContractDetail, getSealList } from '@/api/project-development/acceptance-application.js'
import Title from '@/components/Title/index.vue'
import { on_preview_or_downFile } from '@/utils/hooks.js'
import { ElMessage } from 'element-plus'
import CghtTableDialog from './components/cghtTableDialog.vue'

const sealList = ref([])

const formData = defineModel()

const isShowCghtTableDialog = ref(false)

const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址

const uploadRef = useTemplateRef('uploadRef')

function onFileUploadSuccess() {
  uploadRef.value.clearValidate()
  ElMessage.success('上传附件成功')
}

function onFileUploadFail() {
  ElMessage.error('上传附件失败')
}

async function onAssociate(data) {
  formData.value.baseInfo.contractName = data.name
  formData.value.baseInfo.contractCode = data.code
  formData.value.baseInfo.taxAmount = data.amount
  const res = await getPsmContractDetail({ id: data.id })
  formData.value.baseInfo.contractOtherSubjects = res.psmContractOtherSubjectList.map(item => ({ id: item.id, account: item.account, identity: item.identity, merchantCode: item.merchantCode, merchantName: item.merchantName, nodeCode: item.nodeCode, remark: item.remark }))
}

async function getSealListReq() {
  const res = await getSealList({ pageNo: 1, pageSize: 1000 })
  sealList.value = res.rows
}

function openCghtTableDialog() {
  if (!formData.value.baseInfo.prdProjectCode)
    return ElMessage.warning('请先关联项目')
  isShowCghtTableDialog.value = true
}

onMounted(() => {
  getSealListReq()
})
</script>

<style lang="scss" scoped>

</style>
