<template>
  <Title>项目基本信息</Title>
  <ElRow :gutter="30">
    <ElCol :span="12">
      <ElFormItem prop="prdProjectName">
        <template #label>
          <span>项目名称</span>
          <ElButton
            v-if="!formData.baseInfo.prdProjectName"
            style="margin-left: 40px;"
            link
            type="primary"
            @click="isShowAssociationDialog = true">
            <ElIcon>
              <Link />
            </ElIcon>
            关联项目
          </ElButton>
          <ElButton
            v-else
            style="margin-left: 40px;"
            link
            type="primary"
            @click="resetSelect">
            <ElIcon>
              <Link />
            </ElIcon>
            清除重选
          </ElButton>
        </template>
        <ElInput
          v-model="formData.baseInfo.prdProjectName"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目编号">
        <ElInput
          v-model="formData.baseInfo.prdProjectCode"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="研发负责人">
        <ElInput
          v-model="formData.baseInfo.principal"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="业务主体">
        <ElInput
          v-model="formData.baseInfo.prdEntityDept"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目分类">
        <ElInput
          v-model="formData.baseInfo.prdProjectCategory"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="研发方式">
        <ElInput
          v-model="formData.baseInfo.rdMethod"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目分级">
        <ElInput
          v-model="formData.baseInfo.projectClassification"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="立项日期">
        <ElInput
          v-model="formData.baseInfo.projectInitiationDate"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="预算金额（万元）">
        <ElInput
          v-model="formData.baseInfo.budgetAmount"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目分管领导">
        <ElInput
          v-model="formData.baseInfo.projectLeader"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem label="研发项目基本情况">
        <ElInput
          v-model="formData.baseInfo.projectBasicInformation"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
  </ElRow>
  <YfxmDialog
    v-model="isShowAssociationDialog"
    @associate="onAssociate" />
</template>

<script setup>
import { getAcceptanceApplicationDetailByCode } from '@/api/project-development/acceptance-application.js'
import Title from '@/components/Title/index.vue'
import YfxmDialog from '@/views/project-development/project-application-change/form/components/yfxmDialog.vue'

const formData = defineModel()

const isShowAssociationDialog = ref(false)

async function onAssociate(data) {
  const res = await getAcceptanceApplicationDetailByCode(data.prdProjectCode)
  formData.value.baseInfo = res
}

function resetSelect() {
  // 重置时将与之关联的合同信息也一并清除
  formData.value.baseInfo = {
    ...formData.value.baseInfo,
    prdProjectName: '',
    prdProjectCode: '',
    principal: '',
    principalId: '',
    prdEntityDept: '',
    prdProjectCategory: '',
    rdMethod: '',
    projectClassification: '',
    projectInitiationDate: '',
    budgetAmount: '',
    projectLeader: '',
    projectLeaderId: '',
    projectBasicInformation: '',
    contractName: '',
    contractCode: '',
    taxAmount: '',
    contractOtherSubjects: [],
  }
}
</script>

<style lang="scss" scoped>

</style>
