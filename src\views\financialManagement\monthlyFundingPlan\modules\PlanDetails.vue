<template>
  <ElTabs
    v-model="activeName"
    style="width: 95%;"
    @tab-click="handleClick">
    <ElTabPane
      label="回款计划"
      name="hu">
      <div class="sub-title">
        合同回款计划明细
      </div>

      <ElTable
        :data="receiptPlan"
        border
        stripe
        style="width: 100%; margin-top: 10px">
        <ElTableColumn
          prop="contractName"
          fixed="left"
          label="合同名称"
          width="300" />
        <ElTableColumn
          prop="contractCode"
          fixed="left"
          label="合同编号"
          width="180" />
        <ElTableColumn
          prop="ourName"
          label="我方主体"
          width="240" />
        <ElTableColumn
          prop="merchantName"
          label="对方主体"
          width="240" />
        <ElTableColumn
          prop="markEntity"
          label="市场主体"
          width="240" />
        <ElTableColumn
          prop="deliverySubject"
          label="交付主体"
          width="240" />
        <ElTableColumn
          prop="contractTotalAmount"
          label="合同总金额"
          width="120">
          <template #default="{ row }">
            {{ formatNumber(row.contractTotalAmount) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="nodeName"
          label="节点名称"
          width="120" />
        <ElTableColumn
          prop="moneyNature"
          label="款项性质"
          width="120" />
        <ElTableColumn
          prop="settlement"
          label="结算条件"
          width="250" />
        <ElTableColumn
          prop="executionTime"
          label="预计执行时间"
          width="180" />
        <ElTableColumn
          prop="settlementMerchantName"
          label="结算客商名称"
          width="180" />
        <ElTableColumn
          prop="settlementAmount"
          label="结算金额"
          width="120"
          fixed="right">
          <template #default="{ row }">
            {{ formatNumber(row.settlementAmount) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="remark"
          label="备注"
          width="120"
          fixed="right" />
      </ElTable>
      <div class="pagination-container">
        <ElPagination
          v-model:current-page="receiptCurrentPage"
          v-model:page-size="receiptPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="receiptTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleReceiptSizeChange"
          @current-change="handleReceiptCurrentChange"
        />
      </div>
    </ElTabPane>
    <ElTabPane
      label="付款计划"
      name="fu">
      <div class="sub-title">
        合同付款计划明细
      </div>
      <ElTable
        :data="paymentPlan"
        border
        stripe
        style="width: 100%; margin-top: 10px">
        <ElTableColumn
          prop="contractName"
          label="合同名称"
          fixed="left"
          width="300" />
        <ElTableColumn
          prop="contractCode"
          label="合同编号"
          fixed="left"
          width="180" />
        <ElTableColumn
          prop="ourName"
          label="我方主体"
          width="240" />
        <ElTableColumn
          prop="merchantName"
          label="对方主体"
          width="240" />
        <ElTableColumn
          prop="markEntity"
          label="市场主体"
          width="240" />
        <ElTableColumn
          prop="deliverySubject"
          label="交付主体"
          width="240" />
        <ElTableColumn
          prop="contractTotalAmount"
          label="合同总金额"
          width="120">
          <template #default="{ row }">
            {{ formatNumber(row.contractTotalAmount) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="nodeName"
          label="节点名称"
          width="120" />
        <ElTableColumn
          prop="moneyNature"
          label="款项性质"
          width="120" />
        <ElTableColumn
          prop="settlement"
          label="结算条件"
          width="250" />
        <ElTableColumn
          prop="executionTime"
          label="预计执行时间"
          width="180" />
        <ElTableColumn
          prop="settlementMerchantName"
          label="结算客商名称"
          width="180" />
        <ElTableColumn
          prop="settlementAmount"
          label="结算金额"
          width="120"
          fixed="right">
          <template #default="{ row }">
            {{ formatNumber(row.settlementAmount) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="remark"
          label="备注"
          width="120"
          fixed="right" />
      </ElTable>
      <div class="pagination-container">
        <ElPagination
          v-model:current-page="paymentCurrentPage"
          v-model:page-size="paymentPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="paymentTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePaymentSizeChange"
          @current-change="handlePaymentCurrentChange"
        />
      </div>
    </ElTabPane>
  </ElTabs>
</template>

<script setup>
import { getPsmContractList } from '@/api/financialManagement/monthlyFundingPlan.js'
import { computed, ref, watch } from 'vue'

const props = defineProps({
  searchData: {
    type: Object,
    default: () => ({
      planProjectList: [],
      monthTableData: [],
    }),
  },
})
const activeName = ref('hu')
const receiptPlan = ref([])
const paymentPlan = ref([])

// 回款计划分页相关
const receiptCurrentPage = ref(1)
const receiptPageSize = ref(10)
const receiptTotal = ref(0)
const receiptAllData = ref([])

// 付款计划分页相关
const paymentCurrentPage = ref(1)
const paymentPageSize = ref(10)
const paymentTotal = ref(0)
const paymentAllData = ref([])

// 计算当前页的数据
function getCurrentPageData(data, currentPage, pageSize) {
  const start = (currentPage - 1) * pageSize
  const end = start + pageSize
  return data.slice(start, end)
}

// 回款计划分页处理
function handleReceiptSizeChange(val) {
  receiptPageSize.value = val
  receiptPlan.value = getCurrentPageData(receiptAllData.value, receiptCurrentPage.value, receiptPageSize.value)
}

function handleReceiptCurrentChange(val) {
  receiptCurrentPage.value = val
  receiptPlan.value = getCurrentPageData(receiptAllData.value, receiptCurrentPage.value, receiptPageSize.value)
}

// 付款计划分页处理
function handlePaymentSizeChange(val) {
  paymentPageSize.value = val
  paymentPlan.value = getCurrentPageData(paymentAllData.value, paymentCurrentPage.value, paymentPageSize.value)
}

function handlePaymentCurrentChange(val) {
  paymentCurrentPage.value = val
  paymentPlan.value = getCurrentPageData(paymentAllData.value, paymentCurrentPage.value, paymentPageSize.value)
}

const contractCodes = computed(() => {
  const codes = new Set()
  if (props.searchData.planProjectList && props.searchData.planProjectList.length > 0) {
    props.searchData.planProjectList.forEach((item) => {
      if (item.salesContractCode) {
        codes.add(item.salesContractCode)
      }
      if (item.purchaseContract?.purchaseContractCode) {
        codes.add(item.purchaseContract.purchaseContractCode)
      }
    })
  }
  return Array.from(codes)
})

const executionStartTime = computed(() => {
  return props.searchData.monthTableData ? `${props.searchData.monthTableData[0]}-01 00:00:00` : ''
})

const executionEndTime = computed(() => {
  if (!props.searchData.monthTableData || !props.searchData.monthTableData[2]) {
    return ''
  }

  // 获取最后一个月份（格式：YYYY-MM）
  const lastMonth = props.searchData.monthTableData[2]
  const [year, month] = lastMonth.split('-').map(Number)

  // 验证年份和月份是否有效
  if (Number.isNaN(year) || Number.isNaN(month) || month < 1 || month > 12) {
    console.error('Invalid year or month:', { year, month })
    return ''
  }

  // 获取下个月的第一天，然后减去1天，得到当前月的最后一天
  const lastDay = new Date(year, month, 0).getDate()

  // 格式化月份和日期，确保是两位数
  const formattedMonth = month.toString().padStart(2, '0')
  const formattedDay = lastDay.toString().padStart(2, '0')

  return `${year}-${formattedMonth}-${formattedDay} 23:59:59`
})

watch([contractCodes, executionStartTime, executionEndTime], ([newContractCodes, newStartTime, newEndTime]) => {
  if (newContractCodes.length > 0 && newStartTime && newEndTime) {
    getPsmContractList({
      contractCodes: newContractCodes,
      flowFunds: '收入',
      executionStartTime: newStartTime,
      executionEndTime: newEndTime,
    }).then((res) => {
      receiptAllData.value = res.data || []
      receiptTotal.value = receiptAllData.value.length
      receiptPlan.value = getCurrentPageData(receiptAllData.value, receiptCurrentPage.value, receiptPageSize.value)
    })
  } else {
    receiptAllData.value = []
    receiptTotal.value = 0
    receiptPlan.value = []
  }
})

watch([contractCodes, executionStartTime, executionEndTime], ([newContractCodes, newStartTime, newEndTime]) => {
  if (newContractCodes.length > 0 && newStartTime && newEndTime) {
    getPsmContractList({
      contractCodes: newContractCodes,
      flowFunds: '支出',
      executionStartTime: newStartTime,
      executionEndTime: newEndTime,
    }).then((res) => {
      paymentAllData.value = res.data || []
      paymentTotal.value = paymentAllData.value.length
      paymentPlan.value = getCurrentPageData(paymentAllData.value, paymentCurrentPage.value, paymentPageSize.value)
    })
  } else {
    paymentAllData.value = []
    paymentTotal.value = 0
    paymentPlan.value = []
  }
})

function handleClick(tab, event) {
  console.log(tab, event)
}

// 新增格式化数字的方法
function formatNumber(value) {
  return value || 0
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important; /* 设置最小高度 */

  .sub-title {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;

    .sub-title-span {
      display: inline-block;
      margin-left: 10px;
      font-weight: 400;
      font-size: 14px;
    }
  }

  .plan-item {
    width: 100%;

    .form-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 10px;
    margin-bottom: 40px;
    border: 1px dashed #1677ff;
    border-radius: 6px;
    color: #1677ff;
    font-size: 14px;
    cursor: pointer;
  }

  .form-item {
    width: 360px;
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 12px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}

.file-text-link {
  margin-bottom: 16px;
  color: #409eff;
  cursor: pointer;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 10px 0;
}
</style>

<style lang="scss">
.custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
