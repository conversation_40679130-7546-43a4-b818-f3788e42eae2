{
  "Vue3 Base Setup": {
    "scope": "vue",
    "prefix": "Vue3BaseSetup",
    "body": [
      "<template>",
      "  <div class=\"$1\">",
      "    <!-- TODO: $2 -->",
      "  </div>",
      "</template>",
      "",
      "<script setup>",
      "",
      "</script>",
      "",
      "<style lang=\"scss\" scoped>",
      "",
      "</style>"
    ],
    "description": "vue3 setup scss"
  },
  "Table Page Container": {
    "scope": "vue",
    "prefix": "TablePageContainer",
    "description": "生成 本项目一个封装后的布局 表格模板",
    "body": [
      "<template>",
      "  <Container>",
      "    <TableContainer title=\"${1:页面标题}\">",
      "      <template #search=\"{ searchBoxWidth }\">",
      "        <FilterCriteria :popover-width=\"searchBoxWidth\">",
      "          ${2:<!-- 筛选条件内容 -->}",
      "        </FilterCriteria>",
      "      </template>",
      "",
      "      <template #toolbar>",
      "        <ElButton",
      "          type=\"primary\"",
      "          @click=\"${3:handleCreate}\">",
      "          新建",
      "        </ElButton>",
      "      </template>",
      "",
      "      <template #default=\"{ contentHeight }\">",
      "        <ElTable",
      "          :max-height=\"contentHeight\"",
      "          :data=\"${4:tableData}\"",
      "          border",
      "          style=\"width: 100%\">",
      "          <ElTableColumn",
      "            prop=\"example\"",
      "            label=\"实例\"",
      "            width=\"180\"",
      "            align=\"center\" />",
      "          ${5:<!-- 更多表格列 -->}",
      "        </ElTable>",
      "      </template>",
      "",
      "      <template #footer>",
      "        <ElPagination",
      "          v-model:current-page=\"${6:pagination}.pageNum\"",
      "          v-model:page-size=\"${6:pagination}.pageSize\"",
      "          :page-sizes=\"[10, 20, 50, 100]\"",
      "          layout=\"total, prev, pager, next, sizes, jumper\"",
      "          :total=\"${6:pagination}.total\" />",
      "      </template>",
      "    </TableContainer>",
      "  </Container>",
      "</template>",
      "",
      "<script setup>",
      "import Container from '@/components/Container/index.vue'",
      "import TableContainer from '@/components/Container/table-container.vue'",
      "import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'",
      "import { usePagination } from '@/utils/hooks.js'",
      "",
      "const ${4:tableData} = ref([${7:/* 表格数据 */}])",
      "",
      "const { ${6:pagination} } = usePagination()",
      "",
      "function ${3:handleCreate}() {",
      "  ${8:// 新建逻辑}",
      "}",
      "</script>",
      "",
      "<style lang=\"scss\" scoped>",
      "${9:/* 样式代码 */}",
      "</style>"
    ]
  },
  "Destructure Proxy Instance": {
    "prefix": "getproxy",
    "body": "const { proxy } = getCurrentInstance()",
    "description": "从上下文解构 proxy 对象"
  },
  "Insert Modal Warning By Proxy": {
    "prefix": "ProxyModalwarn",
    "body": "proxy.\\$modal.msgWarning('${1:警告内容}')",
    "description": "插入模态警告提示"
  },
  "Insert Modal Success By Proxy": {
    "prefix": "ProxyModalsuccess",
    "body": "proxy.\\$modal.msgSuccess('${1:成功内容}')",
    "description": "插入模态成功提示"
  },
  "Insert Modal Error By Proxy": {
    "prefix": "ProxyModalerror",
    "body": "proxy.\\$modal.msgError('${1:错误内容}')",
    "description": "插入模态错误提示"
  },
  "Insert Modal Info By Proxy": {
    "prefix": "ProxyModalinfo",
    "body": "proxy.\\$modal.msg('${1:信息内容}')",
    "description": "插入模态信息提示"
  },
  "Insert Modal Confirm By Proxy": {
    "prefix": "ProxyModalconfirm",
    "body": [
      "proxy.\\$modal.confirm('${1:确认内容}').then(() => {",
      "  ${2:// 确认操作}",
      "}).catch(() => {",
      "  ${3:// 取消操作}",
      "})",
    ],
    "description": "插入模态确认提示"
  },
}