import { get, post } from '@/utils/request'

export default {
  /**
   * 投标申请
   */
  apply: {
    /**
     * 投标申请分页接口
     * @param {*} data
     * @returns
     */
    bidApplicationPage: (data) => {
      return post('/project/bid/application/bidApplicationPage', data)
    },
    /**
     * 根据bid获取投标申请详情
     * @param {*} bid
     * @returns
     */
    bidApplicationDetail: (bid) => {
      return get(`/project/bid/application/detail/${bid}`)
    },
    /**
     * 暂存或提交投标申请
     * @param {*} data
     * @returns
     */
    bidApplicationSaveOrUpdateByBid: (data) => {
      return post('/project/bid/application/saveOrUpdateByBid', data)
    },
    bidApplicationPageDeleteByBid: (bid) => {
      return post(`/project/bid/application/delete/${bid}`)
    },
    /**
     * 通过预立项id获取预立项项目详情
     */
    getPreProjectDetail: id => post(`/project/preinit/pre-est-project/details/${id}`),
    /**
     * 获取预立项审批通过且未被关联数据列表
     */
    getSuccessAndOnlyPreEstProject: data => post(`/project/bid/application/getSuccessAndOnlyPreEstProject`, data),
  },
  /**
   * 标后登记
   */
  log: {
    /**
     * 标后登记分页接口
     */
    bidRegistrationPage: data => post('/project/bid/registration/bidRegistrationPage', data),
    /**
     * 标后登记详情
     */
    bidRegistrationDetail: bid => get(`/project/bid/registration/detail/${bid}`),
    /**
     * 暂存或提交标后登记
     */
    bidRegistrationSaveOrUpdateByBid: data => post('/project/bid/registration/saveOrUpdateByBid', data),
    /**
     * 标后登记删除
     */
    bidRegistrationDelete: bid => post(`/project/bid/registration/delete/${bid}`),
    /**
     * 获取投标申请审批通过且未被关联数据列表
     * @param {*} data
     * @returns
     */
    getSuccessAndOnlyPreEstProject: data => post(`/project/bid/registration/getSuccessAndOnlyBidApplication`, data),
  },
}
