import { get, post, remove } from '@/utils/alova'
import { download } from '@/utils/request'
/**
 * 定标分页列表
 * @param {*} data
 * @returns
 */
export const getBidList = data => post('/purchaseManage/standardization/pageList', data)

/**
 * 获取详情
 * @param {*} data
 * @returns
 */
export const getBidDetail = data => get('/purchaseManage/standardization/get', data)
/**
 * 删除定标审批
 * @param {*} data
 * @returns
 */
export const rmBid = data => remove (`/purchaseManage/standardization/del/${data}`)
/**
 * 新增定标审批（提交审批）
 * @param {*} data
 * @returns
 */
export const addBid = data => post('/purchaseManage/standardization/submit/add', data)
/**
 * 新增定标审批（暂存）
 * @param {*} data
 * @returns
 */
export const saveBid = data => post('/purchaseManage/standardization/tempStore/add', data)

export function exportBidList(data, filename) {
  return download('/purchaseManage/standardization/export', data, filename, {
    headers: { 'Content-Type': 'application/json',
    },
  })
}
