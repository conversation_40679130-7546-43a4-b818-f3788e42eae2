<template>
  <Container>
    <TableContainer title="项目库">
      <template #search="{ searchBoxWidth }">
        <FilterCriteria
          :popover-width="searchBoxWidth"
          @search="refresh"
          @reset="onReset">
          <ElForm
            ref="formRef"
            :model="formData"
            label-width="130px"
            label-position="left">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem
                  prop="prdProjectStage"
                  label="审批状态">
                  <ElSelect
                    v-model="formData.prdProjectStage"
                    :teleported="false">
                    <ElOption
                      v-for="item in config.projectStage"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="6">
                <ElFormItem
                  prop="prdProjectName"
                  label="项目名称">
                  <ElInput v-model="formData.prdProjectName" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </FilterCriteria>
      </template>
      <template #toolbar>
        <div>
          <ElButton
            @click="exportHandle">
            导出
          </ElButton>
        </div>
      </template>

      <template #default="{ contentHeight }">
        <ElTable
          v-loading="loading"
          border
          :data="data"
          row-key="bid"
          :max-height="contentHeight"
          @selection-change="handleSelect"
          @row-dblclick="onRowDbClick">
          <ElTableColumn
            type="selection"
            reserve-selection
            width="55" />
          <ElTableColumn
            prop="prdProjectStage"
            label="项目阶段">
            <template #default="{ row }">
              <div class="status-row">
                <div :class="`status-icon status-icon-${config.projectStage.find(item => item.label === row.prdProjectStage).value}`" />
                {{ row.prdProjectStage }}
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="prdProjectName"
            label="项目名称" />
          <ElTableColumn
            prop="prdProjectCode"
            label="项目编号" />
          <ElTableColumn
            prop="principal"
            label="研发负责人" />
          <ElTableColumn
            prop="budgetAmount"
            label="项目总预算" />
          <ElTableColumn
            prop="prdEntityDept"
            label="业务主体" />
          <ElTableColumn
            label="立项日期"
            prop="projectInitiationDate" />
        </ElTable>
      </template>
      <template #footer>
        <ElPagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total,prev,pager,next,sizes,jumper"
          :total="Number(total)" />
      </template>
    </TableContainer>
  </Container>
</template>

<script setup>
import { getProjectLibraryPageList } from '@/api/project-development/project-library.js'
import Container from '@/components/Container/index.vue'
import TableContainer from '@/components/Container/table-container.vue'
import FilterCriteria from '@/components/DataTable/FilterCriteria.vue'
import { usePagination } from 'alova/client'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import * as config from './config.js'

const router = useRouter()
const formData = reactive({})
const formRef = ref()

const { proxy } = getCurrentInstance()

const { loading, data, pageSize, page, total, refresh } = usePagination((pageNum, pageSize) => getProjectLibraryPageList({
  params: formData,
  page: {
    pageNum,
    pageSize,
  },
}), {
  total: res => res.total,
  data: res => res.list,
  initialPage: 1, // 初始页码，默认为1
  initialPageSize: 10, // 初始每页数据条数，默认为10
})

function onRowDbClick(row) {
  router.push({ path: '/project-development/project-library/detail', query: { projectCode: row.prdProjectCode } })
}

function onReset() {
  formRef.value.resetFields()
  refresh()
}

const ids = ref([])
function handleSelect(selection) {
  ids.value = selection.map(item => item.bid)
}
async function exportHandle() {
  if (ids.value.length <= 0) {
    ElMessage.warning('请至少选择一条数据')
  } else {
    proxy.download('/project/prd/project/library/detail/change/export', { bids: ids.value, isAll: 0, params: {} }, `研发项目项目库_${new Date().getTime()}.xlsx`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
</script>

<style lang="scss" scoped>
.status-row {
  display: flex;
  align-items: center;

  .status-icon {
    width: 6px;
    height: 6px;
    margin-right: 6px;
    border-radius: 50%;
  }

  .status-icon-已结项 {
    background: #c1bfbf;
  }

  .status-icon-变更审批中 {
    background: #faad14;
  }

  .status-icon-验收审批中 {
    background: #1677ff;
  }

  .status-icon-进行中 {
    background: #52c41a;
  }

  .status-icon-结项审批中 {
    background: #ff7875;
  }

  .status-icon-5 {
    background: #722ed1;
  }

  .status-icon-6 {
    background: rgb(0 0 0 / 25%);
  }

  .status-icon-7 {
    background: #eb2f96;
  }
}
</style>
