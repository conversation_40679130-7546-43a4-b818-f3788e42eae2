import request from '@/utils/request'

const prefix = '/outsource'
/**
 * 离场/变更列表
 */
export function getDepartureList(query) {
  return request({
    url: `${prefix}/departureChange/list`,
    method: 'get',
    params: query,
  })
}
// 外包人员选择
export function getPersonList(query) {
  return request({
    url: `${prefix}/staff/approach/list`,
    method: 'get',
    params: query,
  })
}

// 保存离场/变更信息
export function saveDeparture(data) {
  return request({
    url: `${prefix}/departureChange/save`,
    method: 'post',
    data,
  })
}
// 查询离场/变更信息详情
export function getDepartureDetail(data) {
  return request({
    url: `${prefix}/departureChange/detail?bid=${data}`,
    method: 'get',
  })
}
// 撤销离场/变更信息
export function withdrawnDeparture(data) {
  return request({
    url: `${prefix}/departureChange/withdrawn?bid=${data}`,
    method: 'get',
  })
}
// 导出离场/变更信息
export function exportDeparture(query) {
  return request({
    url: `${prefix}/departureChange/export`,
    method: 'get',
    params: query,
  })
}
// 删除外包人员列表
export function deletDepartureList(data) {
  return request({
    url: `${prefix}/departureChange/del/${data}`,
    method: 'post',
  })
}
// 导出
export function exportList(query) {
  return request({
    url: `${prefix}/departureChange/export`,
    method: 'post',
    params: query,
  })
}
