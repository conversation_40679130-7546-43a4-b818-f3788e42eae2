import request from '@/utils/request'

// 获取发票申请信息分页列表
export function getInvoiceApplyPageList(params) {
  return request({
    url: `/finance/invoice-apply/pageList`,
    method: 'post',
    data: params,
  })
}

// 获取发票申请信息详情
export function getInvoiceApplyDetail(params) {
  return request({
    url: `/finance/invoice-apply/detail`,
    method: 'get',
    params,
  })
}

// 暂存、更新发票申请信息
export function createOrUpdateInvoice(data) {
  return request({
    url: `/finance/invoice-apply/createOrUpdate`,
    method: 'post',
    data,
  })
}

// 删除发票申请
export function deleteInvoiceApply(params) {
  return request({
    url: `/finance/invoice-apply/del`,
    method: 'get',
    params,
  })
}

// 提交发票申请
export function submitInvoiceApply(data) {
  return request({
    url: `/finance/invoice-apply/submit`,
    method: 'post',
    data,
  })
}

// 税务专员回传发票信息提交
export function submitInvoiceLedger(data) {
  return request({
    url: `/finance/invoice-apply/submit-with-ledger`,
    method: 'post',
    data,
  })
}
