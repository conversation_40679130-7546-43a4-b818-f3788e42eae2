<template>
  <Title style="margin-top: 20px;">
    预算执行监测
  </Title>
  <ElRow :gutter="30">
    <ElCol :span="12">
      <div class="card">
        <span>外采预算执行表</span>
        <PieChart :option="wcys" />
      </div>
    </ElCol>
    <ElCol :span="12">
      <div class="card">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span>人力预算执行表</span>
          <ElButtonGroup size="small">
            <ElButton type="primary">
              预算
            </ElButton>
            <ElButton>工时</ElButton>
          </ElButtonGroup>
        </div>
        <PieChart :option="rlys" />
      </div>
    </ElCol>
    <ElCol
      :span="12"
      style="margin-top: 20px;">
      <div class="card">
        <span>项目管理费执行表</span>
        <PieChart :option="xmglf" />
      </div>
    </ElCol>
  </ElRow>
</template>

<script setup>
import Title from '@/components/Title/index.vue'
import PieChart from './components/pieChart.vue'

const formData = defineModel()
console.log(formData)
// 外采预算
const wcys = {
  totalTitle: '外采预算金额',
  title: '已签约合同额',
  totalValue: 110308,
  value: 67800,
  color: '#446df7',
}

// 人力预算
const rlys = {
  totalTitle: '工时预算金额',
  title: '实际已用预算',
  totalValue: 130468,
  value: 43028,
  color: '#ebbe44',
}

// 项目管理费
const xmglf = {
  totalTitle: '项目管理费预算金额',
  title: '实际已用预算',
  totalValue: 70892,
  value: 32000,
  color: '#ebbe44',
}
</script>

<style lang="scss" scoped>
.card {
  padding: 15px 15px 10px;
  border: 1px solid rgb(0 0 0 / 6%);
  border-radius: 8px;
}
</style>
