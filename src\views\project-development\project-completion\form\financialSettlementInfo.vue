<template>
  <Title>财务决算信息</Title>
  <ElRow :gutter="30">
    <ElCol :span="6">
      <ElFormItem label="项目总成本预算（元）">
        <ElInput
          v-model="formData.baseInfo.budgetAmount"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="外采成本预算（元）">
        <ElInput
          v-model="formData.baseInfo.budgetOutsourcingCostAmount"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="人力成本预算（元）">
        <ElInput
          v-model="formData.baseInfo.budgetJobLaborCostAmount"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目管理费预算（元）">
        <ElInput
          v-model="formData.baseInfo.budgetProjectManagerCostAmount"
          disabled
          placeholder="关联后自动获取" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目总成本决算（元）">
        <ElInput
          v-model="formData.baseInfo.actualAmount"
          disabled
          placeholder="输入决算值后自动计算" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="外采成本决算（元）"
        prop="actualOutsourcingCostAmount">
        <ElInput
          v-model="formData.baseInfo.actualOutsourcingCostAmount"
          type="number"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="人力成本决算（元）"
        prop="actualJobLaborCostAmount">
        <ElInput
          v-model="formData.baseInfo.actualJobLaborCostAmount"
          type="number"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="项目管理费决算（元）"
        prop="actualProjectManagerCostAmount">
        <ElInput
          v-model="formData.baseInfo.actualProjectManagerCostAmount"
          type="number"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目总成本偏离值">
        <ElInput
          v-model="formData.baseInfo.amountVariance"
          disabled
          placeholder="输入决算值后自动计算">
          <template #append>
            %
          </template>
        </ElInput>
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="外采成本偏离值">
        <ElInput
          v-model="formData.baseInfo.outsourcingCostVariance"
          disabled
          placeholder="输入决算值后自动计算">
          <template #append>
            %
          </template>
        </ElInput>
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="人力成本偏离值">
        <ElInput
          v-model="formData.baseInfo.jobLaborCostVariance"
          disabled
          placeholder="输入决算值后自动计算">
          <template #append>
            %
          </template>
        </ElInput>
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目管理费偏离值">
        <ElInput
          v-model="formData.baseInfo.projectManagerCostVariance"
          disabled
          placeholder="输入决算值后自动计算">
          <template #append>
            %
          </template>
        </ElInput>
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem label="补充说明">
        <ElInput
          v-model="formData.baseInfo.supplementaryInstruction"
          :rows="2"
          type="textarea"
          placeholder="请输入" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="24">
      <ElFormItem label="财务决算相关附件">
        <ElUpload
          v-model:file-list="formData.baseInfo.financialActualAttachment"
          style="width: 100%; padding-top: 20px;"
          :action="uploadUrl"
          :on-preview="on_preview_or_downFile"
          auto-upload
          :on-success="onFileUploadSuccess"
          :on-error="onFileUploadFail"
          drag
          multiple>
          <i
            style="color: #1677FF; font-size: 28px;"
            class="iconfont icon-InboxOutlined" />
          <h3>拖拽或点击文件上传</h3>
          <div class="el-upload__tip">
            支持PPT、PPTX、PDF、DOC、DOCX、TXT、ZIP、JPG等通用格式
          </div>
        </ElUpload>
      </ElFormItem>
    </ElCol>
  </ElRow>
</template>

<script setup>
import Title from '@/components/Title/index.vue'
import { on_preview_or_downFile } from '@/utils/hooks.js'
import { add, subtract, toFixedAccurate } from '@/utils/math.js'
import { ElMessage } from 'element-plus'

const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址

const formData = defineModel()

function onFileUploadSuccess() {
  ElMessage.success('上传附件成功')
}

function onFileUploadFail() {
  ElMessage.error('上传附件失败')
}

watchEffect(() => {
  formData.value.baseInfo.actualAmount = add(add(formData.value.baseInfo.actualJobLaborCostAmount, formData.value.baseInfo.actualOutsourcingCostAmount), formData.value.baseInfo.actualProjectManagerCostAmount)
})

watchEffect(() => {
  if (formData.value.baseInfo.actualAmount) {
    formData.value.baseInfo.amountVariance = toFixedAccurate(subtract(formData.value.baseInfo.actualAmount, formData.value.baseInfo.budgetAmount) / formData.value.baseInfo.budgetAmount, 2) * 100 || 0
  }
})

watchEffect(() => {
  if (formData.value.baseInfo.actualOutsourcingCostAmount) {
    formData.value.baseInfo.outsourcingCostVariance = toFixedAccurate(subtract(formData.value.baseInfo.actualOutsourcingCostAmount, formData.value.baseInfo.budgetOutsourcingCostAmount) / formData.value.baseInfo.budgetOutsourcingCostAmount, 2) * 100 || 0
  }
})

watchEffect(() => {
  if (formData.value.baseInfo.actualJobLaborCostAmount) {
    formData.value.baseInfo.jobLaborCostVariance = toFixedAccurate(subtract(formData.value.baseInfo.actualJobLaborCostAmount, formData.value.baseInfo.budgetJobLaborCostAmount) / formData.value.baseInfo.budgetJobLaborCostAmount, 2) * 100 || 0
  }
})

watchEffect(() => {
  if (formData.value.baseInfo.actualProjectManagerCostAmount) {
    formData.value.baseInfo.projectManagerCostVariance = toFixedAccurate(subtract(formData.value.baseInfo.actualProjectManagerCostAmount, formData.value.baseInfo.budgetProjectManagerCostAmount) / formData.value.baseInfo.budgetProjectManagerCostAmount, 2) * 100 || 0
  }
})
</script>

<style lang="scss" scoped></style>
