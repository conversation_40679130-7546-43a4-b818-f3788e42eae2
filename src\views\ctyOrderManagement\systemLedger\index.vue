<template>
  <DefaultContainer v-loading="isLoading">
    <!-- 折叠面板组件，用于查询条件 -->
    <Collapse>
      <template #header />
      <!-- 查询表单 -->
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="150px"
        label-position="left">
        <ElRow :gutter="20">
          <ElCol :span="8">
            <ElFormItem
              prop="appName"
              label="应用系统名称(楚天云)">
              <ElInput
                v-model="formData.appName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="customerName"
              label="客户名称">
              <ElInput
                v-model="formData.customerName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              prop="gzAppName"
              label="应用系统名称(数产)">
              <ElInput
                v-model="formData.gzAppName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </Collapse>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
      <ElButton
        type="primary"
        @click="handle('add')">
        新建
      </ElButton>
    </div>
    <!-- 数据表格 -->
    <ElTable
      border
      :data="tableData"
      style="margin-top: 20px"
      @row-dblclick="onRowDbClick">
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        width="60"
        fixed="left"
        align="center" />
      <ElTableColumn
        prop="appName"
        label="应用系统名称(楚天云)"
        min-width="120" />
      <ElTableColumn
        prop="customerName"
        label="客户名称"
        min-width="120" />
      <ElTableColumn
        prop="gzAppName"
        label="应用系统名称(数产)"
        min-width="120" />
      <ElTableColumn
        prop="createTime"
        label="创建时间"
        min-width="140" />
      <ElTableColumn
        label="操作"
        width="110"
        fixed="right">
        <template #default="scope">
          <ElButton
            v-if="!scope.row.isItBound"
            type="primary"
            link
            @click="handle('edit', scope.row)">
            编辑
          </ElButton>
          <ElPopconfirm
            title="确定删除吗？"
            @confirm="handle('delete', scope.row)">
            <template #reference>
              <ElButton
                v-if="isCtCloudLeader && !scope.row.isItBound"
                v-hasPermi="['systemLedger:delete']"
                type="primary"
                link>
                删除
              </ElButton>
            </template>
          </ElPopconfirm>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onSearch" />
    </div>
  </DefaultContainer>
</template>

<script setup>
import { checkCtCloudLeader } from '@/api/ctyOrderManagement/public.js'
import { deleteCtCloudAppSystem, getCtCloudAppSystemPageList } from '@/api/ctyOrderManagement/systemLedger.js'
import Collapse from '@/components/Collapse/index.vue'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { usePagination } from '@/utils/hooks.js'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

/**
 * 分页相关
 * pagination: 分页参数对象
 * indexMethod: 表格序号计算方法
 */
const { pagination, indexMethod } = usePagination()

const router = useRouter()

/**
 * 表单数据与引用
 */
const formData = reactive({}) // 查询表单数据
const formRef = ref() // 表单引用，用于重置

/**
 * 表格数据与加载状态
 */
const tableData = ref([]) // 表格数据
const loadingIndex = ref(0) // 加载计数器，用于处理多个并发请求的loading状态

/**
 * 计算属性：是否显示加载状态
 * 当loadingIndex > 0时显示加载状态
 */
const isLoading = computed(() => {
  return loadingIndex.value !== 0
})
/**
 * 生命周期钩子：组件挂载完成后初始化
 */
onMounted(() => {
  init()
})

/**
 * 获取应用系统信息分页列表
 */
function getPage() {
  loadingIndex.value++ // 开始加载，计数器加1
  const searchData = {
    page: {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    },
    params: {},
  }
  searchData.params = {
    ...formData,
  }

  getCtCloudAppSystemPageList(searchData)
    .then((res) => {
      console.log(res, '---- getCtCloudAppSystemPageList')
      tableData.value = res.data.list
      pagination.total = res.data.total
      loadingIndex.value-- // 加载完成，计数器减1
    })
    .catch((err) => {
      console.log(err, '---- getCtCloudAppSystemPageList')
      loadingIndex.value-- // 加载出错，计数器减1
    })
}

/**
 * 初始化方法：组件挂载后加载应用系统列表数据
 */
const isCtCloudLeader = ref(false) // 是否楚天云负责人
function init() {
  checkCtCloudLeader({})
    .then((res) => {
      console.log(res, '---- checkCtCloudLeader')
      isCtCloudLeader.value = res.data
    })
    .catch((err) => {
      console.log(err, '---- checkCtCloudLeader')
    })
  getPage()
}

/**
 * 表格行双击事件处理
 * @param {object} row - 行数据
 */
function onRowDbClick(row) {
  router.push({
    path: '/ctyOrderManagement/systemLedger/details',
    query: {
      id: row.id,
    },
  })
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getPage()
}

/**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  getPage()
}

/**
 * 重置方法
 * 重置表单并查询第一页数据
 */
function onReset() {
  formRef.value.resetFields() // 重置表单字段
  pagination.pageNum = 1 // 重置到第一页
  onSearch() // 执行查询
}

/**
 * 操作方法
 * @param {string} type - 操作类型：add-新增应用系统，edit-编辑应用系统，delete-删除应用系统
 * @param {object} row - 行数据对象，包含应用系统信息
 */
function handle(type, row) {
  console.log(type, row, '---- handle')
  switch (type) {
    case 'add':
      console.log('新建应用系统')
      router.push({
        path: '/ctyOrderManagement/systemLedger/form',
        query: {
          type: 'add',
        },
      })
      break
    case 'edit':
      console.log('编辑应用系统')
      router.push({
        path: '/ctyOrderManagement/systemLedger/form',
        query: {
          id: row.id,
          type: 'edit',
        },
      })
      break
    case 'delete':
      console.log('删除应用系统')
      deleteCtCloudAppSystem({
        id: row.id,
      })
        .then((res) => {
          console.log(res, '---- deleteCtCloudAppSystem')
          if (res.code === 200) {
            ElMessage.success('删除成功')
            getPage()
          } else {
            ElMessage.warning(res.msg)
          }
        })
      break
    default:
      console.log('未知操作类型')
      break
  }
}
</script>

<style lang="scss" scoped>
/* 操作区域样式 */
.operation-area {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
