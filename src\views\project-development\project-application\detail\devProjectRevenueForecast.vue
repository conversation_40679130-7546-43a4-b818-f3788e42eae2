<template>
  <Title style="margin-top: 20px;">
    研发项目收益预测
  </Title>
  <ElTable
    border
    style="width: 100%;"
    :data="form.incomeDetails"
    :span-method="objectSpanMethod">
    <ElTableColumn
      label="收益类型"
      prop="incomeType"
      align="center" />
    <ElTableColumn
      label="周期"
      prop="period"
      align="center" />
    <ElTableColumn
      label="收入来源"
      prop="incomeSource"
      align="center" />
    <ElTableColumn
      label="收入描述"
      prop="incomeDescribe"
      align="center" />
    <ElTableColumn
      label="预计收入金额"
      prop="budgetRevenueAmount"
      align="center" />
    <ElTableColumn
      label="备注"
      prop="remark"
      align="center" />
  </ElTable>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const form = defineModel()

function objectSpanMethod({ column, rowIndex, columnIndex }) {
  // 只处理第一列（columnIndex === 0）
  if (columnIndex === 0) {
    // 获取当前行和上一行的第一列数据
    const currentRow = form.value.incomeDetails[rowIndex]
    const prevRow = form.value.incomeDetails[rowIndex - 1]

    // 如果不是第一行，并且当前行的第一列数据与上一行相同
    if (rowIndex > 0 && currentRow[column.property] === prevRow[column.property]) {
      // 隐藏当前单元格（通过设置 rowspan 为 0）
      return {
        rowspan: 0,
        colspan: 0,
      }
    } else {
      // 如果是第一行，或者当前行数据与上一行不同，则需要计算合并的行数
      let rowspan = 1

      // 向下查找相同数据的行数
      for (let i = rowIndex + 1; i < form.value.incomeDetails.length; i++) {
        if (form.value.incomeDetails[i][column.property] === currentRow[column.property]) {
          rowspan++
        } else {
          break
        }
      }

      // 返回合并的行数
      return {
        rowspan,
        colspan: 1,
      }
    }
  }
}
</script>
