<template>
  <DefaultContainer>
    <div
      v-if="!props.id"
      class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          框架采购协议详情
        </div>
      </div>
    </div>
    <div class="content">
      <!-- <div class="sub-title">
        明细信息
      </div> -->
      <ElDescriptions
        border
        :column="2"
        style="margin-bottom: 36px"
        label-width="200">
        <ElDescriptionsItem
          v-for="item in descriptionList1"
          :key="item.value"
          width="398"
          :span="item.span">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </DefaultContainer>
</template>

<script setup>
import { getFrameworkPurchaseProtocolDetail } from '@/api/ctyOrderManagement/frameworkAgreement.js'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { Back } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
})
const router = useRouter()
const descriptionList1 = [
  { label: '框架协议编号', value: 'protocolCode', span: 1 },
  { label: '协议名称', value: 'protocolName', span: 1 },
  { label: '协议起始日期', value: 'protocolStartTime', span: 1 },
  { label: '协议终止日期', value: 'protocolEndTime', span: 1 },
  { label: '申请日期', value: 'createTime', span: 2 },
  { label: '备注', value: 'remark', span: 2 },
]
const detail = ref({})
const id = ref()
onMounted(() => {
  if (props.id) {
    id.value = props.id
  } else {
    id.value = router.currentRoute.value.query.id
  }
  getFrameworkPurchaseProtocolDetail({ id: id.value }).then((res) => {
    if (res.code === 200) {
      detail.value = res.data
    }
  })
})
</script>

  <style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 8px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  position: relative;
  margin-top: 20px;

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .remark-attachment {
    &:hover {
      color: #409eff;
      cursor: pointer;
    }
  }
}
</style>
