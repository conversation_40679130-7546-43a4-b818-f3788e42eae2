<template>
  <div class="tabs">
    <div
      v-for="(item, index) in props.tabItem"
      :key="index"
      :class="`tab-item ${activeTab === item && 'tab-item-active'}`"
      @click="emit('change', item)">
      {{ item }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  tabItem: {
    type: Array,
    required: true,
  },
  activeTab: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['change'])
</script>

<style lang="scss" scoped>
.tabs {
  flex: none;
  overflow-y: auto;
  width: 150px;
  height: 100%;
  padding: 20px 9px;
  border-radius: 10px;
  background-color: #fff;

  .tab-item {
    display: flex;
    align-items: center;
    width: 132px;
    height: 34px;
    margin-bottom: 12px;
    padding: 0 10px;
    border-radius: 6px;
    color: rgb(0 0 0 / 45%);
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background: rgb(0 115 255 / 6%);
      color: #0073ff;
    }
  }

  .tab-item-active {
    position: relative;
    background: rgb(0 115 255 / 6%);
    color: #0073ff;

    &::before {
      content: '';
      position: absolute;
      left: -9px;
      width: 3px;
      height: 34px;
      border-radius: 0 4px 4px 0;
      background: #0073ff;
    }
  }
}
</style>
