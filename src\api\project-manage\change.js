import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 获取项目变更列表
 * @param id
 * @param {*} data
 * @returns
 */
export function getChangeRecordList(id, data) {
  return request({
    url: `${prefix}/change/list/${id}`,
    method: 'post',
    data,
  })
}

/**
 * 发起变更
 * @param id
 * @param {*} data
 * @returns
 */
export function createChange(data) {
  return request({
    url: `${prefix}/change/create`,
    method: 'post',
    data,
  })
}

/**
 * 发起变更详情
 * @param id
 * @param {*} data
 * @returns
 */
export function getCreateChangeDetail(id) {
  return request({
    url: `${prefix}/change/startDetail/${id}`,
    method: 'post',
  })
}

/**
 * 查询变更详情
 * @param id
 * @param {*} data
 * @returns
 */
export function getQueryChangeDetail(id) {
  return request({
    url: `${prefix}/change/queryDetail/${id}`,
    method: 'post',
  })
}

/**
 * 删除变更
 * @param id
 * @param {*} data
 * @returns
 */
export function removeChange(id) {
  return request({
    url: `${prefix}/change/queryDetail/${id}`,
    method: 'delete',
  })
}
