import request from '@/utils/request.js'

const prefix = '/ecology/company'
/**
 * 伙伴分类
 * @returns
 */
export function getCompanyClass() {
  return request({
    method: 'get',
    url: `${prefix}/class`,
  })
}
/**
 * 获取公司基本信息
 */
export function getCompanyInfo(companyBid) {
  return request({
    url: `/ecology/audit/company/info?companyBid=${companyBid}`,
    method: 'get',
  })
}

/**
 * 获取公司介绍附件
 */
export function getIntroduceAttach(companyBid) {
  return request({
    url: `/ecology/audit/company/introduce?companyBid=${companyBid}`,
    method: 'get',
  })
}/**
  * 获取公司产品及解决方案附件
  */
export function getSolutionAttach(companyBid) {
  return request({
    url: `/ecology/audit/company/solution?companyBid=${companyBid}`,
    method: 'get',
  })
}
/**
 * 获取公司标签
 */
export function getCompanyTag(companyBid) {
  return request({
    method: 'get',
    url: `ecology/audit/company/tag/info/tag?companyBid=${companyBid}`,
  })
}
/**
 * 获取评审结果
 * @param {*} auditRecordBid
 * @returns
 */
export function getAuditResult(auditRecordBid) {
  return request({
    method: 'get',
    url: `ecology/audit/result?auditRecordBid=${auditRecordBid}`,
  })
}
