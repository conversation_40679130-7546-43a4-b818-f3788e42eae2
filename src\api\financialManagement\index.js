import { get } from '@/utils/alova'
import request from '@/utils/request'

export function getDynamicHeader(moduleKey) {
  return request({
    url: `/common/dynamic/header/${moduleKey}`,
    method: 'get',
  })
}

// 表头全选
export function checkAllDynamicHeader(moduleKey) {
  return request({
    url: `/common/dynamic/header/show-all/${moduleKey}`,
    method: 'get',
  })
}

// 修改表头信息
export function updateDynamicHeader(data) {
  return request({
    url: `/common/dynamic/header/update`,
    method: 'post',
    data,
  })
}

// 项目属性控制器
export function getProjectPropertList() {
  return request({
    url: `/common/project/property/list`,
    method: 'get',
  })
}

/**
 * 按名称查找客商
 */
export function getMerchantByName(name) {
  return request({
    url: '/project/merchant/searchByName',
    method: 'get',
    params: { name },
  })
}

/**
 * 客商分页列表-供财务管理调用
 */
export function getMerchantPageList(params) {
  return request({
    url: '/project/merchant/pageList',
    method: 'get',
    params,
  })
}

/**
 * 获取销售合同列表
 */
export function getPsmContractList(params, config) {
  // return get('/purchaseManage/psmContract/list', params, config)
  return get('/purchase/psmContract/list', params, config)
  // return get('/common/contract/psmContract/list', params, config)
}

/**
 * 根据父级编码查询业务类型集合
 * parentCode string
 */
export function getBusinessTypeListWithParentCode(params) {
  return request({
    url: '/common/business/type/listWithParentCode',
    method: 'get',
    params,
  })
}

/**
 * 查询业务类型树状集合
 */
export function getBusinessTypeTreeList() {
  return request({
    url: '/common/business/type/treeList',
    method: 'get',
  })
}

/**
 * 产品服务类型
 */
export function getProductServiceTypeList() {
  return request({
    url: '/common/product/service/type/list',
    method: 'get',
  })
}

/**
 * 款项类别列表
 */
export function getPaymentCategoryList() {
  return request({
    url: '/common/payment/category/list',
    method: 'get',
  })
}

/**
 * 销项税列表
 */
export function getOutputTaxList() {
  return request({
    url: '/common/output/tax/list',
    method: 'get',
  })
}
