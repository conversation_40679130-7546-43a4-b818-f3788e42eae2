import request from '@/utils/request'

// 获取确收分页列表
export function getConfirmationPage(params) {
  return request({
    url: `/finance/confirm-income/pageList`,
    method: 'post',
    data: params,
  })
}

// 获取确收信息详情
export function getConfirmIncomeDetail(params) {
  return request({
    url: `/finance/confirm-income/detail`,
    method: 'get',
    params,
  })
}

// 暂存、更新确收信息
export function createOrUpdateConfirmIncome(data) {
  return request({
    url: `/finance/confirm-income/createOrUpdate`,
    method: 'post',
    data,
  })
}

// 删除确收
export function deleteConfirmIncome(params) {
  return request({
    url: `/finance/confirm-income/del`,
    method: 'get',
    params,
  })
}

// 提交确收
export function submitConfirmIncome(data) {
  return request({
    url: `/finance/confirm-income/submit`,
    method: 'post',
    data,
  })
}

// 根据项目编码获取累计确收进度
export function getCumulativeProgressByProjectCode(params) {
  return request({
    url: `/finance/confirm-income/getCumulativeProgressByProjectCode`,
    method: 'get',
    params,
  })
}

// 根据合同编号，查询累计确认收入/成本占比
export function getCumulativeProgressByContractCode(params) {
  return request({
    url: `/finance/confirm-income/sumConfirmRatioBycontractNumber`,
    method: 'get',
    params,
  })
}

// 审核中提交确收申请明细
export function submitWithApprove(data) {
  return request({
    url: `/finance/confirm-income/submit-with-approve`,
    method: 'post',
    data,
  })
}
