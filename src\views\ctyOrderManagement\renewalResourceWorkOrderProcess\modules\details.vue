<template>
  <DefaultContainer>
    <div
      v-if="!props.id"
      class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          续费资源工单详情
        </div>
      </div>
    </div>
    <div class="content">
      <div class="sub-title">
        楚天云报价信息
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="240">
        <ElDescriptionsItem
          v-for="item in descriptionList1"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
        <ElDescriptionsItem
          :span="3"
          width="398">
          <template #label>
            <div>续费订单附件</div>
          </template>
          <p
            v-for="file in fileList"
            :key="file.url"
            class="file-text"
            @click="downloadFile(file.url, file.name)">
            {{ file.name }}
          </p>
        </ElDescriptionsItem>
      </ElDescriptions>
      <div class="sub-title">
        项目经理核对
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="240">
        <ElDescriptionsItem
          v-for="item in descriptionList2"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
      </ElDescriptions>
      <div class="sub-title">
        市场经理核对
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="240">
        <ElDescriptionsItem
          v-for="item in descriptionList3"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
      </ElDescriptions>
      <div class="sub-title">
        楚天云服务处理信息
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="240">
        <ElDescriptionsItem
          v-for="item in descriptionList4"
          :key="item.value"
          :span="item.span"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </div>
  </DefaultContainer>
</template>

<script setup>
import { getRenewalResourceWorkOrderProcessDetail } from '@/api/ctyOrderManagement/renewalResourceWorkOrderProcess.js'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { Back } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
})
const router = useRouter()
const fileList = ref([])
const descriptionList1 = [
  { label: '年度', value: 'year', span: 1 },
  { label: '订单部门', value: 'deptName', span: 1 },
  { label: '经办人', value: 'handledByName', span: 1 },
  { label: '应用系统名称', value: 'ctAppName', span: 3 },
  { label: '客户名称', value: 'customerName', span: 1 },
  { label: '报价类型', value: 'quotationType', span: 2 },
  { label: '续订开始日期', value: 'continueStartTime', span: 1 },
  { label: '续订结束日期', value: 'continueEndTime', span: 1 },
  { label: '使用周期(天)', value: 'useCycle', span: 1 },
  { label: '打折前原总价(元)', value: 'originalPrice', span: 1 },
  { label: '打折后总报价(元)', value: 'discountedPrice', span: 1 },
  { label: '折扣金额(元)', value: 'discount', span: 1 },
  { label: '综合折扣比例', value: 'discountRatio', span: 1 },
  { label: '报价人', value: 'bidderName', span: 1 },
  { label: '报价日期', value: 'quotationDate', span: 1 },
  { label: '项目名称', value: 'originalProjectName', span: 3 },
  { label: '项目编号', value: 'originalProjectCode', span: 1 },
  { label: '数产项目经理', value: 'originalProjectManagerName', span: 2 },
  { label: '备注', value: 'remark', span: 3 },
]

const descriptionList2 = [
  { label: '项目阶段', value: 'projectPhase', span: 3 },
  { label: '项目名称', value: 'projectName', span: 3 },
  { label: '项目编号', value: 'projectCode', span: 1 },
  { label: '项目所属部门', value: 'projectDeptName', span: 1 },
  { label: '数产项目经理', value: 'projectManagerName', span: 1 },
  { label: '数产市场经理', value: 'marketManagerName', span: 1 },
  { label: '项目分管领导', value: 'projectLeaderName', span: 2 },
  { label: '备注', value: 'projectManagerRemark', span: 3 },
]
const descriptionList3 = [
  { label: '客户意见', value: 'customerComment', span: 3 },
  { label: '新签云服务销售合同', value: 'cloudServiceSalesContract', span: 3 },
  { label: '市场经理核对备注', value: 'marketManagerRemark', span: 3 },
]

const descriptionList4 = [
  { label: '续费订单号', value: 'formNo', span: 3 },
  { label: '续费单状态', value: 'continueOrderStatus', span: 3 },
  { label: '楚天云服务处理备注', value: 'ctRemark', span: 3 },
]

const detail = ref({})
const id = ref()
onMounted(() => {
  if (props.id) {
    id.value = props.id
  } else {
    id.value = router.currentRoute.value.query.id
  }
  getRenewalResourceWorkOrderProcessDetail({ id: id.value }).then((res) => {
    if (res.code === 200) {
      detail.value = res.data
      if (detail.value.attachment) {
        try {
          let parsedArr = JSON.parse(detail.value.attachment)
          parsedArr = parsedArr.map(item => ({
            name: item.match(/[^/\\?#]+$/)[0],
            url: item,
          }))
          fileList.value = parsedArr
        } catch (error) {
          console.log(error)
        }
      }
    }
  })
})

function downloadFile(url, filename) {
  const link = document.createElement('a')
  link.href = url
  link.download = filename // 设置下载的文件名
  document.body.appendChild(link)
  link.click() // 触发点击事件
  document.body.removeChild(link) // 移除元素
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(detail))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

    <style lang="scss" scoped>
  .header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 8px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  position: relative;
  margin-top: 20px;

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .remark-attachment {
    &:hover {
      color: #409eff;
      cursor: pointer;
    }
  }
}

.file-text {
  cursor: pointer;

  &:hover {
    color: #409eff;
    cursor: pointer;
  }
}
</style>
