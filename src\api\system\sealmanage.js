import request from '@/utils/request'
/**
 * 印章分页列表
 */
export function getSealManagePage(query) {
  return request({
    url: '/system/seal/list',
    method: 'get',
    params: query,
  })
}

/**
 * 获取所有的印章列表，用于下拉选择
 * @returns
 */
export function getAllSeal() {
  return request({
    url: '/system/seal/getAll',
    method: 'get',
  })
}

// 添加印章
export function addSeal(data) {
  return request({
    url: '/system/seal',
    method: 'put',
    data,
  })
}
// 修改印章
export function updateSeal(data) {
  return request({
    url: '/system/seal',
    method: 'post',
    data,
  })
}
/**
 * 删除印章
 */
export function delSeal(data) {
  return request({
    url: `/system/seal/delete`,
    method: 'post',
    data,
  })
}
/**
 * 获取部门
 */
export function getDeptTree(query) {
  return request({
    url: '/system/user/deptTree',
    method: 'get',
    params: query,
  })
}
/**
 * 印章详情
 */
export function getSeal(bid) {
  return request({
    url: `/system/seal/${bid}`,
    method: 'get',
  })
}
