<template>
  <DefaultContainer>
    <Title> 文档模板 </Title>
    <Collapse>
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="120px"
        label-position="left">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElFormItem
              label="模板名称"
              prop="projectDocName">
              <ElInput v-model="formData.projectDocName " />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </Collapse>
    <ElRow>
      <ElButton
        type="primary"
        @click="handleAdd">
        新增
      </ElButton>
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
    </ElRow>
    <ElTable
      ref="tableRef"
      border
      :data="tableData"
      height="480"
      style="width: 100%; margin-top: 20px">
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        align="center"
        width="100" />
      <ElTableColumn
        prop="projectDocName"
        label="文档模板名称" />
      <ElTableColumn
        prop="updateTime"
        label="更新时间" />
      <ElTableColumn
        prop="updateBy"
        label="更新人" />
      <ElTableColumn
        prop="createTime"
        label="创建时间" />
      <ElTableColumn
        label="操作"
        width="180">
        <template #default="{ row, $index }">
          <ElButton
            type="primary"
            link
            @click="handleview(row)">
            查看
          </ElButton>
          <ElButton
            type="primary"
            link
            @click="handleUpdate(row, $index)">
            编辑
          </ElButton>
          <ElButton
            type="danger"
            link
            @click="handleDelete(row)">
            删除
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
    <div style="display: flex; justify-content: flex-end; margin-top: 10px">
      <ElPagination
        :current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onSearch" />
    </div>
    <!-- 新增修改对话框 -->
    <ElDialog
      v-model="open"
      :title="title"
      width="80%"
      append-to-body>
      <div style="overflow:auto;padding:20px">
        <ElForm
          :model="form"
          :rules="rules"
          label-width="120px">
          <ElFormItem
            label="文档模板名称"
            prop="projectDocName">
            <ElInput
              v-if="isEdit"
              v-model="form.projectDocName"
              placeholder="请输入文档模板名称"
              style="width: 250px" />
            <span v-else>{{ form.projectDocName }}</span>
          </ElFormItem>
          <ElFormItem
            label="工程类型"
            prop="engineeringType">
            <ElSelect
              v-if="isEdit"
              v-model="form.engineeringType"
              placeholder="选择关联模块"
              style="width: 250px">
              <ElOption
                v-for="item in engineeringType_select_options"
                :key="item.value"
                :label="item.value"
                :value="item.value" />
            </ElSelect>
            <span v-else>{{ form.engineeringType }}</span>
          </ElFormItem>
        </ElForm>
        <p style="font-weight:bold;font-size:18px">
          模板内容
        </p>
        <div style="display:flex;justify-content: right;">
          <ElButton
            v-if="isEdit"
            type="primary"
            @click="addcheckList">
            新增
          </ElButton>
        </div>
        <ElTable
          ref="tabledetailRef"
          border
          :data="checklistchild"
          style="width: 100%; margin-top: 20px"
          height="400">
          <ElTableColumn
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
            width="100" />
          <ElTableColumn
            prop="projectPhase"
            label="项目阶段">
            <template #default="scope">
              <ElInput
                v-if="isEdit"
                v-model="scope.row.projectPhase"
                placeholder="请输入" />
              <span v-else>{{ scope.row.projectPhase }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="classification"
            label="一级分类">
            <template #default="scope">
              <ElInput
                v-if="isEdit"
                v-model="scope.row.classification"
                placeholder="请输入" />
              <span v-else>{{ scope.row.classification }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="docName"
            label="文档名称">
            <template #default="scope">
              <ElInput
                v-if="isEdit"
                v-model="scope.row.docName"
                placeholder="请输入" />
              <span v-else>{{ scope.row.docName }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="remark"
            label="备注">
            <template #default="scope">
              <ElInput
                v-if="isEdit"
                v-model="scope.row.remark"
                placeholder="请输入" />
              <span v-else>{{ scope.row.remark }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="electronicDocuments"
            label="电子文件">
            <template #default="scope">
              <ElInput
                v-if="isEdit"
                v-model="scope.row.electronicDocuments"
                placeholder="请输入" />
              <span v-else>{{ scope.row.electronicDocuments }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="paperOriginal"
            label="纸质文件">
            <template #default="scope">
              <ElInput
                v-if="isEdit"
                v-model="scope.row.paperOriginal"
                placeholder="请输入" />
              <span v-else>{{ scope.row.paperOriginal }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            prop="submitDept"
            label="资料提交部门">
            <template #default="scope">
              <ElInput
                v-if="isEdit"
                v-model="scope.row.submitDept"
                placeholder="请输入" />
              <span v-else>{{ scope.row.submitDept }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isEdit"
            label="操作"
            width="100">
            <template #default="scope">
              <ElButton
                type="primary"
                link
                @click="handleDelete_check(scope.$index, scope.row)">
                删除
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
      <template
        v-if="isEdit"
        #footer>
        <div class="dialog-footer">
          <ElButton
            v-show="checklistsaveBut"
            type="primary"
            @click="submitForm">
            保存
          </ElButton>
          <ElButton @click="cancel">
            取消
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </DefaultContainer>
</template>

<script setup>
import * as apis from '@/api/system/docs.js'
import Collapse from '@/components/Collapse'
import DefaultContainer from '@/components/DefaultContainer'
import Title from '@/components/Title'
import { usePagination } from '@/utils/hooks.js'
import { ElForm, ElFormItem } from 'element-plus'
import { ref } from 'vue'

const { proxy } = getCurrentInstance()
const open = ref(false)
const title = ref('')
const tableRef = ref('tableRef')
const tabledetailRef = ref('tabledetailRef')// 详情表格ref
const { pagination, indexMethod } = usePagination()
const formRef = useTemplateRef('formRef')
const formData = reactive({
  associatedModule: '', // 模板名称
})
const tableData = ref([])
const data = reactive({
  form: {},
  rules: {
    projectDocName: [
      { required: true, message: '模板名称不能为空', trigger: 'blur' },
    ],
  },
})
const { form, rules } = toRefs(data)

// 新增修改的表格内容变量
const checklistsaveBut = ref(true)
const checklistchild = ref([])
const checkupdateBid = ref('')

const isEdit = computed(() => {
  return title.value !== '模板查看'
})

const engineeringType_select_options = [
  {
    lable: '软件服务类',
    value: '软件服务类',
  },
  {
    lable: '硬件销售类',
    value: '硬件销售类',
  },
  {
    lable: '系统集成类',
    value: '系统集成类',
  },
  {
    lable: '工程实施类',
    value: '工程实施类',
  },
  {
    lable: '云服务类',
    value: '云服务类',
  },
  {
    lable: '运营类',
    value: '运营类',
  },
  {
    lable: '其他类',
    value: '其他类',
  },
]

/** 查询 */
function onSearch(pageNum) {
  if (pageNum && typeof pageNum === 'number' && pageNum !== pagination.pageNum) {
    pagination.pageNum = pageNum
  }
  getDocsListPage({
    ...pagination,
    ...formData,
  })
}
// 表格内容接口调用
async function getDocsListPage(data) {
  const res = await apis.getDocsList(data)
  const { total, list } = res.data
  tableData.value = list
  pagination.total = Number(total)
}

/** 重置 */
function onReset() {
  formRef.value.resetFields()
  onSearch()
}

// 分页
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  pagination.pageNum = 1
  onSearch()
}

// 查看
function handleview(row) {
  reset()
  chakan(row.bid)
  checklistsaveBut.value = false
  open.value = true
  title.value = '模板查看'
}

// 查看和编辑的渲染
function chakan(bid) {
  apis.getDocsDetail(bid).then((res) => {
    // 设置表单基础信息
    form.value.projectDocName = res.data.projectDoc.projectDocName
    form.value.engineeringType = res.data.projectDoc.engineeringType

    // 设置模板内容表格数据
    checklistchild.value = res.data.projectDocTemplate.map(item => ({
      projectPhase: item.projectPhase || '',
      classification: item.classification || '',
      docName: item.docName || '',
      remark: item.remark || '',
      electronicDocuments: item.electronicDocuments || '',
      paperOriginal: item.paperOriginal || '',
      submitDept: item.submitDept || '',
    }))
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除').then(() => {
    return apis.removeDocs([row.bid])
  }).then(() => {
    onSearch()
    proxy.$modal.msgSuccess('删除成功')
  }).catch(() => {})
}

// 弹窗内表格内容

/** 新增模板 */
function handleAdd() {
  open.value = true
  title.value = '模板创建'
  checklistsaveBut.value = true
  reset()
}

/** 修改模板 */
function handleUpdate(row) {
  reset()
  chakan(row.bid)
  checkupdateBid.value = row.bid
  open.value = true
  title.value = '模板修改'
  form.value = row
  checklistsaveBut.value = true
}

/** 重置新增的表单以及其他数据  */
function reset() {
  checklistchild.value = []
  form.value.projectDocName = ''
  form.value.engineeringType = ''
}
/** 提交按钮 */
function submitForm() {
  if (title.value === '模板创建') {
    const data = {
      projectDocName: form.value.projectDocName,
      engineeringType: form.value.engineeringType,
      sysBaseDocTemplateCreateDTOS: checklistchild.value,
    }
    // 创建
    proxy.$modal.confirm('请确认是否保存').then(() => {
      return apis.saveDocs(data)
    }).then(() => {
      onSearch()
      proxy.$modal.msgSuccess('保存成功')
      open.value = false
    }).catch(() => {})
  } else {
    // 修改
    proxy.$modal.confirm('请确认是否修改').then(() => {
      return apis.updateDocs(checkupdateBid.value, {
        updateDTO: checklistchild.value,
      })
    }).then(() => {
      onSearch()
      proxy.$modal.msgSuccess('修改成功')
      open.value = false
    }).catch(() => {})
  }
}
/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

// 新增修改
// 新增表格行按钮
function addcheckList() {
  checklistchild.value.push({
    projectPhase: '',
    classification: '',
    docName: '',
    remark: '',
    electronicDocuments: '',
    paperOriginal: '',
    submitDept: '',
  })
}

// 删除表格行按钮
function handleDelete_check(index) {
  checklistchild.value.splice(index, 1)
}

// 默认加载
onMounted(() => {
  onSearch()
})
</script>
