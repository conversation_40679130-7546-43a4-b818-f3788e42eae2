<template>
  <div
    v-loading="loading"
    class="manager_target">
    <div class="manager_target_chart">
      <VChart
        :option="option"
        autoresize />
    </div>
  </div>

  <ElDialog
    v-model="dialogVisible"
    title="市场经理销售目标"
    width="1140px">
    <div
      ref="search"
      class="mb-[24px]">
      <div class="flex justify-between">
        <ElPopover
          placement="bottom-start"
          trigger="click"
          :width="searchWidth"
          :show-arrow="false"
          :teleported="false">
          <template #reference>
            <ElButton>
              <i
                class="iconfont icon-sift"
                :style="{
                  marginRight: '8px',
                }" />
              所有筛选
              <ElIcon
                :style="{
                  marginLeft: '10px',
                }">
                <ArrowDown />
              </ElIcon>
            </ElButton>
          </template>
          <ElForm
            ref="searchFormRef"
            :model="dialogFormData">
            <ElRow :gutter="20">
              <ElCol :span="6">
                <ElFormItem
                  label="市场经理"
                  prop="marketManager">
                  <ElInput v-model="dialogFormData.marketManager" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
          <div>
            <ElButton
              type="primary"
              :loading="loading"
              @click="handleSearch">
              查询
            </ElButton>
            <ElButton @click="handleReset">
              重置
            </ElButton>
          </div>
        </ElPopover>
        <ElButton
          type="primary"
          @click="handleTargetFill">
          <template #icon>
            <i
              class="iconfont icon-mbtb"
              :style="{
                marginRight: '8px',
              }" />
          </template>
          目标填报
        </ElButton>
      </div>
    </div>

    <ElTable
      v-loading="loading"
      :data="dialogTableData"
      :border="true">
      <ElTableColumn
        label="序号"
        width="80"
        type="index"
        :index="indexMethod"
        fixed="left" />
      <ElTableColumn
        label="市场经理"
        width="120"
        prop="marketManagerName" />
      <ElTableColumn
        label="所属部门"
        width="120"
        prop="deptName" />
      <ElTableColumn
        label="年度目标销售额（万元）"
        width="170"
        prop="annualTargetSales">
        <template #default="{ row }">
          {{ row?.annualTargetSales.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="年度目标回款额（万元）"
        width="170"
        prop="annualTargetRepayment">
        <template #default="{ row }">
          {{ row?.annualTargetRepayment.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="年度目标收入额（万元）"
        width="170"
        prop="annualTargetIncome">
        <template #default="{ row }">
          {{ row.annualTargetIncome.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="第一季度累计完成销售额（万元）"
        width="220">
        <template #default="{ row }">
          {{ row?.firstQuarter?.actualSales.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="第二季度累计完成销售额（万元）"
        width="220">
        <template #default="{ row }">
          {{ row?.secondQuarter?.actualSales.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="第三季度累计完成销售额（万元）"
        width="220">
        <template #default="{ row }">
          {{ row?.thirdQuarter?.actualSales.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="第四季度累计完成销售额（万元）"
        width="220">
        <template #default="{ row }">
          {{ row?.fourthQuarter?.actualSales.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="年度累计完成额（万元）"
        width="170"
        prop="annualCompleteAmount">
        <template #default="{ row }">
          {{ row?.annualCompleteAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="年度目标完成率"
        width="170"
        prop="annualTargetCompleteRate">
        <template #default="{ row }">
          {{ row.annualTargetCompleteRate }}%
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="当前季度计划分解销售额（万元）"
        width="220"
        prop="expectedSalesCurrentQuarter">
        <template #default="{ row }">
          {{ row?.expectedSalesCurrentQuarter?.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="当前季度实际完成销售额（万元）"
        width="220"
        prop="actualSalesCurrentQuarter">
        <template #default="{ row }">
          {{ row?.actualSalesCurrentQuarter?.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="本月计划分解销售额（万元）"
        width="220"
        prop="expectedSalesCurrentMonth">
        <template #default="{ row }">
          {{ row?.expectedSalesCurrentMonth?.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="本月实际完成销售额（万元）"
        width="220"
        prop="actualSalesCurrentMonth">
        <template #default="{ row }">
          {{ row?.actualSalesCurrentMonth?.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) || '--' }}
        </template>
      </ElTableColumn>
    </ElTable>

    <div class="flex justify-end py-[16px]">
      <ElPagination
        v-model:current-page="dialogFormData.pageNum"
        v-model:page-size="dialogFormData.pageSize"
        :total="dialogFormData.total"
        layout="total, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </ElDialog>

  <ElDrawer
    v-model="drawerVisible"
    size="410px"
    body-class="border-t border-[#000]/[0.08]"
    title="目标填报">
    <ElForm
      ref="drawerFormRef"
      :model="drawerFormData"
      :rules="drawerFormRules"
      label-position="top">
      <ElDivider>年度目标</ElDivider>
      <ElFormItem
        label="年度目标销售额（万元）"
        prop="annualTargetSales">
        <ElInput
          v-model="drawerFormData.annualTargetSales"
          placeholder="请输入年度目标销售额" />
      </ElFormItem>
      <ElFormItem
        label="年度目标回款额（万元）"
        prop="annualTargetRepayment">
        <ElInput
          v-model="drawerFormData.annualTargetRepayment"
          placeholder="请输入年度目标回款额" />
      </ElFormItem>
      <ElFormItem
        label="年度目标收入额（万元）"
        prop="annualTargetIncome">
        <ElInput
          v-model="drawerFormData.annualTargetIncome"
          placeholder="请输入年度目标收入额" />
      </ElFormItem>
      <ElDivider>季度目标</ElDivider>
      <ElFormItem
        label="季度选择"
        prop="quarter">
        <ElSelect
          v-model="drawerFormData.quarter"
          @change="() => {
            drawerFormData.month = monthOptions[0].value
          }">
          <ElOption
            v-for="item in quarterOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem
        label="季度计划分解销售额（万元）"
        prop="quarterExpectedSales">
        <ElInput
          v-model="drawerFormData[drawerFormData.quarter].expectedSales"
          placeholder="请输入季度目标销售额" />
      </ElFormItem>
      <ElFormItem
        label="季度实际完成销售额（万元）"
        prop="quarterActualSales">
        <ElInput
          v-model="drawerFormData[drawerFormData.quarter].actualSales"
          placeholder="请输入季度实际完成销售额" />
      </ElFormItem>
      <ElDivider>月度目标</ElDivider>
      <ElFormItem
        label="月度选择"
        prop="month">
        <ElSelect v-model="drawerFormData.month">
          <ElOption
            v-for="item in monthOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem
        label="月度计划分解销售额（万元）"
        prop="monthExpectedSales">
        <ElInput
          v-model="drawerFormData[drawerFormData.month].expectedSales"
          placeholder="请输入月度计划分解销售额" />
      </ElFormItem>
      <ElFormItem
        label="月度实际完成销售额（万元）"
        prop="monthActualSales">
        <ElInput
          v-model="drawerFormData[drawerFormData.month].actualSales"
          placeholder="请输入月度实际完成销售额" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleTargetFillCancel">
        取消
      </ElButton>
      <ElButton
        type="primary"
        :loading="confirmLoading"
        @click="handleTargetFillConfirm">
        保存
      </ElButton>
    </template>
  </ElDrawer>
</template>

<script setup>
import { getManagerTargetData, getManagerTargetDetail, saveManagerTargetData } from '@/api/dashboard/client.js'
import { useElementSize } from '@vueuse/core'

const { year, analysisSubject, isAdmin, deptId } = defineProps({
  year: Number,
  analysisSubject: String,
  isAdmin: Boolean,
  deptId: [String, Number],
})

const { proxy } = getCurrentInstance()

const option = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.06)',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    icon: 'circle',
    textStyle: {
      width: 80,
      overflow: 'truncate',
    },
  },
  grid: {
    top: 32,
    right: 0,
    bottom: 50,
    left: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#C1C5CC',
      },
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '单位：万元',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
    },
  ],
  dataZoom: {
    type: 'slider',
    backgroundColor: '#FAFAFA',
    dataBackground: {
      lineStyle: {
        color: '#E4E4E4',
      },
      areaStyle: {
        color: '#F0F0F0',
        opacity: 1,
      },
    },
    selectedDataBackground: {
      lineStyle: {
        color: '#77C6F2',
      },
      areaStyle: {
        color: '#C3E1F2',
        opacity: 1,
      },
    },
    fillColor: '#C3E1F2',
    borderColor: '#EBEBEB',
    borderRadius: 2,
    moveHandleStyle: {
      opacity: 0,
    },
    brushStyle: {
      color: '#E1F1FA',
    },
  },
  series: [
    {
      name: '年度目标销售额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#165DFF',
      },
      barGap: 0,
      barWidth: 24,
    },
    {
      name: '第一季度累计完成销售额',
      stack: '年度累计完成销售额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#14C9C9',
      },
      barWidth: 24,
    },
    {
      name: '第二季度累计完成销售额',
      stack: '年度累计完成销售额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#F7BA1E',
      },
    },
    {
      name: '第三季度累计完成销售额',
      stack: '年度累计完成销售额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#722ED1',
      },
    },
    {
      name: '第四季度累计完成销售额',
      stack: '年度累计完成销售额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#3491FA',
      },
    },
    {
      name: '年度实际完成率',
      type: 'line',
      yAxisIndex: 1,
      data: [],
      itemStyle: {
        color: '#D91AD9',
      },
    },
  ],
})

const { width: searchWidth } = useElementSize(useTemplateRef('search'))
const searchFormRef = useTemplateRef('searchFormRef')

const dialogVisible = ref(false)
const dialogFormData = reactive({
  marketManager: '',
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const dialogTableData = ref([])
function indexMethod(index) {
  return (dialogFormData.pageNum - 1) * dialogFormData.pageSize + index + 1
}

function handleSearch() {
  init(true)
}

function handleReset() {
  searchFormRef.value.resetFields()
}

function handleSizeChange() {
  init(true)
}

function handleCurrentChange() {
  init(true)
}

const loading = ref(false)
function init(isSearch = false) {
  loading.value = true
  getManagerTargetData({
    analysisYear: year,
    analysisEntityName: isAdmin ? analysisSubject?.[analysisSubject.length - 1] : '',
    analysisEntityId: isAdmin ? undefined : deptId,
    marketManagerName: isSearch ? dialogFormData.marketManager : undefined,
    pageNum: isSearch ? dialogFormData.pageNum : 1,
    pageSize: isSearch ? dialogFormData.pageSize : 9999,
  }).then((res) => {
    if (isSearch) {
      dialogTableData.value = res.data.list
      dialogFormData.total = res.data.total
      dialogFormData.pageNum = res.data.page
    } else {
      option.value.xAxis.data = res.data.list.map(item => item.marketManagerName)
      option.value.series[0].data = res.data.list.map(item => item.annualTargetSales)
      option.value.series[1].data = res.data.list.map(item => item?.firstQuarter?.actualSales || 0)
      option.value.series[2].data = res.data.list.map(item => item?.secondQuarter?.actualSales || 0)
      option.value.series[3].data = res.data.list.map(item => item?.thirdQuarter?.actualSales || 0)
      option.value.series[4].data = res.data.list.map(item => item?.fourthQuarter?.actualSales || 0)
      option.value.series[5].data = res.data.list.map(item => item.annualTargetCompleteRate)
    }
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  init()
})

watch(() => [year, analysisSubject, deptId], () => {
  init(false)
})

const drawerVisible = ref(false)
const quarterOptions = ref([
  {
    label: '第一季度',
    value: 'firstQuarter',
    children: [
      { label: '一月', value: 'januaryQuarter' },
      { label: '二月', value: 'februaryQuarter' },
      { label: '三月', value: 'marchQuarter' },
    ],
  },
  {
    label: '第二季度',
    value: 'secondQuarter',
    children: [
      { label: '四月', value: 'aprilQuarter' },
      { label: '五月', value: 'mayQuarter' },
      { label: '六月', value: 'juneQuarter' },
    ],
  },
  {
    label: '第三季度',
    value: 'thirdQuarter',
    children: [
      { label: '七月', value: 'julyQuarter' },
      { label: '八月', value: 'augestQuarter' },
      { label: '九月', value: 'septemberQuarter' },
    ],
  },
  {
    label: '第四季度',
    value: 'fourthQuarter',
    children: [
      { label: '十月', value: 'octoberQuarter' },
      { label: '十一月', value: 'novemberQuarter' },
      { label: '十二月', value: 'decemberQuarter' },
    ],
  },
])
const drawerFormData = reactive({
  id: undefined, // 未填写时为空，填写后为后端返回的id
  annualTargetSales: 0, // 年度目标销售额
  annualTargetRepayment: 0, // 年度目标回款额
  annualTargetIncome: 0, // 年度目标收入额
  quarter: '', // 季度
  firstQuarter: {
    expectedSales: 0, // 计划分解销售额
    actualSales: 0, // 实际完成销售额
  }, // 第一季度
  secondQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 第二季度
  thirdQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 第三季度
  fourthQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 第四季度
  month: '', // 月度
  januaryQuarter: {
    expectedSales: 0, // 计划分解销售额
    actualSales: 0, // 实际完成销售额
  }, // 一月
  februaryQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 二月
  marchQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 三月
  aprilQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 四月
  mayQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 五月
  juneQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 六月
  julyQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 七月
  augestQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 八月
  septemberQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 九月
  octoberQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 十月
  novemberQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 十一月
  decemberQuarter: {
    expectedSales: 0,
    actualSales: 0,
  }, // 十二月
})
const monthOptions = computed(() => {
  return quarterOptions.value.find(item => item.value === drawerFormData.quarter).children
})

const drawerFormRules = {
  annualTargetSales: [
    { required: true, message: '请输入年度目标销售额', trigger: ['blur', 'change'] },
    { pattern: /^(?:0|[1-9]\d*)(?:\.\d{1,2})?$/, message: '请输入正确的金额', trigger: ['blur', 'change'] },
  ],
  quarterExpectedSales: [
    {
      validator: (rule, value, callback) => {
        const pattern = /^(?:0|[1-9]\d*)(?:\.\d{1,2})?$/

        // 校验季度计划分解销售额格式
        quarterOptions.value.forEach((item) => {
          const quarterExpectedSales = drawerFormData[item.value].expectedSales
          if (quarterExpectedSales && !pattern.test(quarterExpectedSales)) {
            callback(new Error(`${item.label}的计划分解销售额格式不正确`))
          }
        })

        // 校验季度计划分解销售额之和不能大于年度目标销售额
        const annualTargetSales = Number(drawerFormData.annualTargetSales || '') || 0
        const sum = quarterOptions.value.reduce((prev, curr) => {
          const quarterExpectedSales = Number(drawerFormData[curr.value].expectedSales || '') || 0
          return prev + quarterExpectedSales
        }, 0)

        if (sum > annualTargetSales) {
          callback(new Error('季度计划分解销售额之和不能大于年度目标销售额'))
        }

        callback()
      },
      trigger: ['blur', 'change'],
    },
  ],
  quarterActualSales: [
    {
      validator: (rule, value, callback) => {
        const pattern = /^(?:0|[1-9]\d*)(?:\.\d{1,2})?$/
        // 校验季度实际完成销售额格式
        quarterOptions.value.forEach((item) => {
          const quarterActualSales = drawerFormData[item.value].actualSales
          if (quarterActualSales && !pattern.test(quarterActualSales)) {
            callback(new Error(`${item.label}的实际完成销售额格式不正确`))
          }
        })

        callback()
      },
      trigger: ['blur', 'change'],
    },
  ],
  monthExpectedSales: [
    {
      validator: (rule, value, callback) => {
        const pattern = /^(?:0|[1-9]\d*)(?:\.\d{1,2})?$/
        // 校验月度计划分解销售额格式
        quarterOptions.value.forEach((item) => {
          item.children.forEach((monthItem) => {
            const monthExpectedSales = drawerFormData[monthItem.value].expectedSales
            if (monthExpectedSales && !pattern.test(monthExpectedSales)) {
              callback(new Error(`${item.label}${monthItem.label}的计划分解销售额格式不正确`))
            }
          })
        })
        // 校验月度计划分解销售额之和不能大于季度计划分解销售额
        quarterOptions.value.forEach((item) => {
          const quarterExpectedSales = Number(drawerFormData[item.value].expectedSales || '') || 0
          const sum = item.children.reduce((prev, curr) => {
            const monthExpectedSales = Number(drawerFormData[curr.value].expectedSales || '') || 0
            return prev + monthExpectedSales
          }, 0)

          if (sum > quarterExpectedSales) {
            callback(new Error(`${item.label}的月度计划分解销售额之和不能大于季度计划分解销售额`))
          }
        })

        callback()
      },
      trigger: ['blur', 'change'],
    },
  ],
  monthActualSales: [
    {
      validator: (rule, value, callback) => {
        const pattern = /^(?:0|[1-9]\d*)(?:\.\d{1,2})?$/

        quarterOptions.value.forEach((item) => {
          item.children.forEach((monthItem) => {
            const monthActualSales = drawerFormData[monthItem.value].actualSales
            if (monthActualSales && !pattern.test(monthActualSales)) {
              callback(new Error(`${item.label}${monthItem.label}的实际完成销售额格式不正确`))
            }
          })
        })

        callback()
      },
      trigger: ['blur', 'change'],
    },
  ],
}

const drawerFormRef = useTemplateRef('drawerFormRef')
// 监听年度目标销售额变化，重新校验季度计划分解销售额
watch(
  () => drawerFormData.annualTargetSales,
  () => {
    drawerFormRef.value?.validateField('quarterExpectedSales')
  },
)
// 监听季度计划分解销售额变化，重新校验月度计划分解销售额
watch(
  () => drawerFormData.quarter && drawerFormData[drawerFormData.quarter].expectedSales,
  () => {
    drawerFormRef.value?.validateField('monthExpectedSales')
  },
)

function handleTargetFill() {
  drawerVisible.value = true
  const monthNow = new Date().getMonth() + 1
  const quarterNow = Math.ceil(monthNow / 3)

  drawerFormData.quarter = quarterOptions.value[quarterNow - 1].value
  drawerFormData.month = quarterOptions
    .value[quarterNow - 1]
    .children[monthNow % 3 === 0 ? 2 : monthNow % 3 - 1]
    .value

  getManagerTargetDetail().then((res) => {
    for (const key in drawerFormData) {
      if (Object.prototype.hasOwnProperty.call(drawerFormData, key)) {
        if (key === 'quarter' || key === 'month') {
          continue
        } else if (key === 'id') {
          drawerFormData[key] = res.data?.id || undefined
        } else if (['firstQuarter', 'secondQuarter', 'thirdQuarter', 'fourthQuarter'].includes(key)) {
          drawerFormData[key].expectedSales = res.data?.[key]?.expectedSales || 0
          drawerFormData[key].actualSales = res.data?.[key]?.actualSales || 0
        } else if (['januaryQuarter', 'februaryQuarter', 'marchQuarter', 'aprilQuarter', 'mayQuarter', 'juneQuarter', 'julyQuarter', 'augestQuarter', 'septemberQuarter', 'octoberQuarter', 'novemberQuarter', 'decemberQuarter'].includes(key)) {
          drawerFormData[key].expectedSales = res.data?.[key]?.expectedSales || 0
          drawerFormData[key].actualSales = res.data?.[key]?.actualSales || 0
        } else {
          drawerFormData[key] = res.data?.[key] || 0
        }
      }
    }
  })
}

function handleTargetFillCancel() {
  drawerVisible.value = false
  drawerFormRef.value.resetFields()
}

const confirmLoading = ref(false)
function handleTargetFillConfirm() {
  drawerFormRef.value.validate((valid, fields) => {
    if (valid) {
      confirmLoading.value = true
      const submitData = {
        ...drawerFormData,
      }
      delete submitData.quarter
      delete submitData.month

      // 空字符串处理
      quarterOptions.value.forEach((item) => {
        submitData[item.value].expectedSales = submitData[item.value].expectedSales || 0
        submitData[item.value].actualSales = submitData[item.value].actualSales || 0
        item.children.forEach((monthItem) => {
          submitData[monthItem.value].expectedSales = submitData[monthItem.value].expectedSales || 0
          submitData[monthItem.value].actualSales = submitData[monthItem.value].actualSales || 0
        })
      })

      saveManagerTargetData({
        ...submitData,
      }).then(() => {
        proxy.$modal.msgSuccess('保存成功')
        handleTargetFillCancel()
      }).finally(() => {
        confirmLoading.value = false
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}

defineExpose({
  showDialog() {
    dialogVisible.value = true
    init(true)
  },
})
</script>

<style lang="scss" scoped>
.manager_target {
  &_chart {
    width: 100%;
    height: 381px;
    margin-top: 12px;
  }
}
</style>
