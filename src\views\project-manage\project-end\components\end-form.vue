<template>
  <div class="main_container">
    <ElForm
      label-position="top"
      :model="formData"
      :rules="rules">
      <Title is-hidden>
        项目终止/挂起审批提交
      </Title>
      <ElRow
        :gutter="20"
        class="mt-16">
        <ElCol :span="4">
          <ElFormItem
            label="项目名称"
            prop="projectBasicInfo.projectName">
            <ElInput
              v-model="formData.projectBasicInfo.projectName"
              :disabled="formType === 'view'"
              placeholder="请输入项目名称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="2">
          <ElFormItem label="项目选择">
            <ElButton
              :disabled="formType === 'view'"
              :icon="Link"
              type="primary"
              @click="project_dialog_options.onOpen">
              关联项目
            </ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="项目编号"
            prop="projectBasicInfo.projectCode">
            <ElInput
              v-model="formData.projectBasicInfo.projectCode"
              disabled
              placeholder="项目编号自动生成" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="项目经理"
            prop="projectBasicInfo.projectManager">
            <ElInput
              v-model="formData.projectBasicInfo.projectManager"
              disabled
              placeholder="自动关联项目经理" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="市场经理"
            prop="projectBasicInfo.marketingManager">
            <ElInput
              v-model="formData.projectBasicInfo.marketingManager"
              disabled
              placeholder="自动关联市场经理" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="分管领导"
            prop="projectBasicInfo.projectLeader">
            <ElInput
              v-model="formData.projectBasicInfo.projectLeader"
              disabled
              placeholder="自动关联分管领导" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="申请人"
            prop="applicationInfo.applicant">
            <ElInput
              v-model="formData.applicationInfo.applicant"
              disabled
              placeholder="当前登录用户" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="申请日期"
            prop="applicationInfo.applyDate">
            <ElDatePicker
              v-model="formData.applicationInfo.applyDate"
              disabled
              type="date"
              placeholder="选择申请日期"
              style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem
            label="申请内容"
            prop="applicationInfo.applicationContent">
            <ElSelect
              v-model="formData.applicationInfo.applicationContent"
              :disabled="formType === 'view'"
              placeholder="请选择">
              <ElOption
                label="项目终止"
                value="项目终止" />
              <ElOption
                label="项目临时挂起"
                value="项目临时挂起" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow
        :gutter="20"
        class="mt-16">
        <ElCol :span="12">
          <div class="process-tips">
            <h4>项目终止提示：</h4>
            <ol style=" margin-top: 8px; margin-bottom: 8px;color: red">
              <li>
                1.联系项目建设方，通知取消销售合同，结算合同费用，并达成一致；
              </li>
              <li>
                2.若已签署采购合同，通知供应商取消采购合同，结算合同费用，并达成一致；
              </li>
              <li>
                3.提起本流程，领导批复后，到合同管理系统中取消销售合同和采购合同；
              </li>
              <li>
                4.本流程到部门部长：审核终止真实性;
                <ul style="margin-left: 24px">
                  <li>部门部长：审核终止真实性</li>
                  <li>经营管理部：取消项目预算和里程碑</li>
                  <li>市场经理：协助取消销售合同</li>
                  <li>采购部：协助取消采购合同</li>
                  <li>财务部：协助合同决算及费用核销</li>
                </ul>
              </li>
            </ol>
          </div>
        </ElCol>
        <ElCol :span="12">
          <h4>项目终止提示:</h4>
          <ol style="color: red;margin-top: 8px;margin-bottom: 8px;">
            <li>1.项目发生变更，双方未达成一致，可发起挂起。</li>
            <li>2.项目资金不能及时支付，可发起挂起。</li>
            <li>3.项目建设方/业主方通知我方暂停项目，可发起挂起。</li>
            <li>4.相关供应商出现履约的异常情况，可发起挂起。</li>
          </ol>
        </ElCol>
      </ElRow>
      <ElRow>
        <ElCol :span="24">
          <ElFormItem
            label="终止/挂起日期"
            prop="applicationInfo.eolDate">
            <ElDatePicker
              v-model="formData.applicationInfo.eolDate"
              :disabled="formType === 'view'"
              style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            label="终止/挂起影响"
            prop="applicationInfo.effect">
            <ElInput
              v-model="formData.applicationInfo.effect"
              :disabled="formType === 'view'"
              :rows="4"
              type="textarea"
              placeholder="请输入终止/挂起影响" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            label="终止/挂起原因"
            prop="applicationInfo.reason">
            <ElInput
              v-model="formData.applicationInfo.reason"
              :disabled="formType === 'view'"
              :rows="4"
              type="textarea"
              placeholder="请输入终止/挂起原因" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            label="终止/挂起风险"
            prop="applicationInfo.risk">
            <ElInput
              v-model="formData.applicationInfo.risk"
              :disabled="formType === 'view'"
              :rows="4"
              type="textarea"
              placeholder="请输入可能存在的风险" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            label="后续工作计划"
            prop="applicationInfo.nextSteps">
            <ElInput
              v-model="formData.applicationInfo.nextSteps"
              :disabled="formType === 'view'"
              :rows="4"
              type="textarea"
              placeholder="请输入后续工作计划" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            label="附件"
            prop="attachments">
            <ElUpload
              v-model:file-list="formData.attachments"
              :disabled="formType === 'view'"
              :action="uploadUrl"
              multiple>
              <ElButton type="primary">
                点击上传
              </ElButton>
            </ElUpload>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ProjectDialog
        v-model="project_dialog_options.visible"
        type="项目终止挂起"
        @checked="project_dialog_options.checked" />
    </ElForm>
  </div>
</template>

<script setup>
import {
  createProjectEol,
  getProjectEolDetail,
  submitProjectEol,
  updateProjectEol,
} from '@/api/project-manage/projectEnd.js'
import { getCurrentProcessId } from '@/api/wflow-pro.js'
import ProjectDialog from '@/components/dialog/project-dialog.vue'
import Title from '@/components/Title/index.vue'
import useUserStore from '@/store/modules/user.js'
import { uploadUrl } from '@wflow-pro/api/request'
// import { on_preview_or_downFile } from '@/utils/hooks.js'
import { dayjs, ElMessage } from 'element-plus'
import { klona } from 'klona'

const { id } = defineProps({
  id: {
    type: String,
    default: '',
  },
  formType: {
    type: String,
    default: 'view',
  },
})
const route = useRoute()
const userStore = useUserStore()
// const formRef = useTemplateRef('formRef')

const formData = reactive({
  projectBasicInfo: {
    projectName: '',
    projectCode: '',
    projectManager: '',
    projectManagerId: '',
    marketingManager: '',
    marketingManagerId: '',
    projectLeader: '',
    projectLeaderId: '',
    deliverySubject: '',
    projectStatus: '',
    acceptanceDate: '',
  },
  applicationInfo: {
    applicant: userStore.nickName,
    applyDate: dayjs().format('YYYY-MM-DD'),
    applicationContent: '',
    eolDate: '',
    reason: '',
    risk: '',
    nextSteps: '',
  },
  attachments: [],
})
const processDefId = ref('')
onMounted(async () => {
  const bid = route.query?.businessId
  const pid = await getCurrentProcessId('project-finish')
  processDefId.value = pid
  if (id || bid) {
    try {
      const res = await getProjectEolDetail(id || bid)
      const data = res.data
      formData.applicationInfo.applicant = data.applicant
      formData.applicationInfo.applicantDept = data.applicantDept
      formData.applicationInfo.applyDate = data.createTime
      formData.applicationInfo.eolDate = data.eolDate
      formData.applicationInfo.effect = data.effect
      formData.applicationInfo.reason = data.reason
      formData.applicationInfo.risk = data.risk
      formData.applicationInfo.nextSteps = data.nextSteps
      formData.applicationInfo.applicationContent = data.applicationContent
      // ===== projectBasicInfo =====
      formData.projectBasicInfo.projectName = data.projectName
      formData.projectBasicInfo.projectCode = data.projectCode
      formData.projectBasicInfo.projectManager = data.projectManager
      formData.projectBasicInfo.projectManagerId = data.projectManagerId
      formData.projectBasicInfo.marketingManager = data.marketingManager
      formData.projectBasicInfo.marketingManagerId = data.marketingManagerId
      formData.projectBasicInfo.projectLeader = data.projectLeader
      formData.projectBasicInfo.projectLeaderId = data.projectLeaderId
      formData.projectBasicInfo.deliveryEntity = data.deliveryEntity
      formData.projectBasicInfo.deliveryEntityId = data.deliveryEntityId
      formData.projectBasicInfo.projectStatus = data.projectStatus
      formData.projectBasicInfo.acceptanceDate = data.acceptanceDate

      // ===== 附件处理 =====
      if (data.appendix) {
        formData.attachments = data.appendix.split(',').map((url) => {
          const name = url.match(/([^/]+)(?=\.[^.]+$|$)/)
            ? url.match(/([^/]+)(?=\.[^.]+$|$)/)[0]
            : ''
          return { url, name }
        })
      }
    } catch (error) {
      ElMessage.error('获取终止数据失败')
      console.error('Error fetching project detail:', error)
    }
  }
})

function setValues() {
  return {
    // applicationInfo
    applicant: formData.applicationInfo.applicant,
    applicantDept: formData.applicationInfo.applicantDept,
    applyDate: formData.applicationInfo.applyDate,
    eolDate: formData.applicationInfo.eolDate,
    effect: formData.applicationInfo.effect,
    reason: formData.applicationInfo.reason,
    risk: formData.applicationInfo.risk,
    nextSteps: formData.applicationInfo.nextSteps,

    // projectBasicInfo
    projectName: formData.projectBasicInfo.projectName,
    projectCode: formData.projectBasicInfo.projectCode,
    projectManager: formData.projectBasicInfo.projectManager,
    projectManagerId: formData.projectBasicInfo.projectManagerId,
    marketingManager: formData.projectBasicInfo.marketingManager,
    marketingManagerId: formData.projectBasicInfo.marketingManagerId,
    projectLeader: formData.projectBasicInfo.projectLeader,
    projectLeaderId: formData.projectBasicInfo.projectLeaderId,
    deliveryEntity: formData.projectBasicInfo.deliveryEntity,
    deliveryEntityId: formData.projectBasicInfo.deliveryEntityId,
    // TODO:字段暂定
    projectStatus: formData.projectBasicInfo.projectStatus,
    acceptanceDate: formData.projectBasicInfo.acceptanceDate,
    // applicationInfo
    applicationContent: formData.applicationInfo.applicationContent,
    appendix:
      formData.attachments.length > 0
        ? formData.attachments
            .map(item => item.url || item?.response?.data)
            .filter(Boolean)
            .join(',')
        : '',
  }
}

async function saveDraftData() {
  const data = setValues()
  data.processDefId = processDefId.value
  data.reviewStatus = 0
  console.log(data)
  if (id) {
    const response = await updateProjectEol(id, data)
    console.log(response)
  } else {
    // 保存草稿
    const response = await createProjectEol(data)
    console.log(response)
  }
}

async function saveData() {
  const data = setValues()
  data.reviewStatus = 1
  data.processDefId = processDefId.value
  console.log(data)
  if (id) {
    const response = await updateProjectEol(id, data)
    console.log(response)
  } else {
    // 提交
    const response = await submitProjectEol(data)
    console.log(response)
  }
}

defineExpose({
  onSave: saveData,
  onSaveDraft: saveDraftData,
  // 获取表单数据
  getFormData: () => {
    return readonly(formData)
  },
  // 审批流程
  saveFormData: async () => {
    try {
      // TODO:需要在此处调用保存接口，保存审批过程中特定人员修改的数据
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})

const project_dialog_options = reactive({
  visible: false,
  project_info: {
    projectManagerName: '',
    projectManagerId: '',
  },
  onOpen: () => {
    project_dialog_options.visible = true
  },
  checked: (data) => {
    console.log(data)
    /**
     * 需要反填的字段
     * 项目名称 projectName
     * 项目编号 projectCode
     * 项目经理 projectManagerName-projectManagerId
     * 市场经理 marketManagerName-marketManagerId
     * 项目分管领导 leadershipName-leadershipId
     * 项目归属部门 deliverySubject
     * 项目状态
     * 验收日期
     */
    project_dialog_options.pre_project_info = klona(data)
    formData.projectBasicInfo.projectName = data.bolProjectName
    formData.projectBasicInfo.projectCode = data.projectCode
    formData.projectBasicInfo.projectManager = data.projectManager
    formData.projectBasicInfo.projectManagerId = data.projectManagerId
    formData.projectBasicInfo.marketingManager = data.marketingManager
    formData.projectBasicInfo.marketingManagerId = data.marketingManagerId
    formData.projectBasicInfo.projectLeader = data.projectLeader
    formData.projectBasicInfo.projectLeaderId = data.projectLeaderId
    formData.projectBasicInfo.deliverySubject = data.deliveryEntity
    formData.projectBasicInfo.deliverySubjectId = data.deliveryEntityId
    // TODO:字段暂定
    formData.projectBasicInfo.projectStatus = data.status
    formData.projectBasicInfo.acceptanceDate = data.acceptanceDate
  },
})

const rules = {
  'applicationInfo.eolDate': [
    { required: true, message: '请选择日期', trigger: 'change' },
  ],
  'applicationInfo.reason': [
    { required: true, message: '请输入原因', trigger: 'blur' },
  ],
}
</script>

<style lang="scss" scoped>
.main_container {
  flex: 1;
  overflow: auto;
  min-width: 1500px;
  padding: 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
