<template>
  <div class="goal_management">
    <div class="flex items-center mb-[12px]">
      <div class="flex-1 pl-[24px]">
        <p class="text-[#000]/[.45] text-[14px] mb-[4px]">
          数量
        </p>
        <p class="text-[#000]/[.88] text-[24px] font-[600]">
          {{ data.count || '-' }}
        </p>
      </div>
      <ElDivider
        style="height: 51px;margin: 0;"
        direction="vertical" />
      <div class="flex-1  pl-[24px]">
        <p class="text-[#000]/[.45] text-[14px] mb-[4px]">
          分值
        </p>
        <p class="text-[#000]/[.88] text-[24px] font-[600]">
          {{ data.score || '-' }}
        </p>
      </div>
      <ElDivider
        style="height: 51px;margin: 0;"
        direction="vertical" />
      <div class="flex-1 pl-[24px]">
        <p class="text-[#000]/[.45] text-[14px] mb-[4px]">
          当前得分
        </p>
        <p class="text-[#000]/[.88] text-[24px] font-[600]">
          {{ data.currentScore || '-' }}
        </p>
      </div>
      <ElDivider
        style="height: 51px;margin: 0;"
        direction="vertical" />
      <div class="flex-1 pl-[24px]">
        <p class="text-[#000]/[.45] text-[14px] mb-[4px]">
          预计得分
        </p>
        <p class="text-[#000]/[.88] text-[24px] font-[600]">
          {{ data.expectedScore || '-' }}
        </p>
      </div>
    </div>
    <div class="w-full h-[272px]">
      <VChart
        :option="option"
        autoresize />
    </div>
  </div>
  <ElDialog
    v-model="dialogVisible"
    width="60%"
    title="目标管理">
    <ElTable
      :data="tableData"
      :border="true">
      <ElTableColumn
        label="序号"
        type="index"
        width="55"
        :index="indexMethod"
        fixed="left" />
      <ElTableColumn
        label="经营主体"
        prop="businessSubject" />
      <ElTableColumn
        label="指标类型"
        prop="indicatorType" />
      <ElTableColumn
        label="所属部门"
        prop="department" />
      <ElTableColumn
        label="数量（项）"
        prop="count" />
      <ElTableColumn
        label="分值"
        prop="score" />
      <ElTableColumn
        label="当前得分"
        prop="currentScore" />
      <ElTableColumn
        label="预计得分"
        prop="expectedScore" />
      <ElTableColumn
        label="完成情况"
        prop="completion">
        <template #default="{ row }">
          <ElTag
            v-if="row.completion === '完成'"
            type="success">
            完成
          </ElTag>
          <ElTag
            v-else-if="row.completion === '未完成'"
            type="danger">
            未完成
          </ElTag>
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="年份"
        prop="year" />
    </ElTable>
    <div class="flex justify-end py-[16px]">
      <ElPagination
        v-model:current-page="paginationData.pageNum"
        v-model:page-size="paginationData.pageSize"
        :total="paginationData.total"
        layout="total, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </ElDialog>
</template>

<script setup>
const data = reactive({
  count: 10,
  score: 100,
  currentScore: 80,
  expectedScore: 90,
})

const option = ref({
  color: ['#165DFF', '#14C9C9', '#F7BA1E', '#722ED1'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.04)',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    icon: 'circle',
  },
  grid: {
    top: 32,
    right: 70,
    bottom: 0,
    left: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#E5E6EB',
      },
    },
  },
  yAxis: {
    type: 'category',
    data: ['约束指标', '鼓励指标', '个性指标', '核心指标'],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#C1C5CC',
      },
    },
  },
  series: [
    {
      name: '数量',
      type: 'bar',
      stack: 'goal',
      barWidth: 20,
      data: [320, 302, 301, 334, 390, 330, 320],
    },
    {
      name: '分值',
      type: 'bar',
      stack: 'goal',
      barWidth: 20,
      data: [120, 132, 101, 134, 90, 230, 210],
    },
    {
      name: '当前得分',
      type: 'bar',
      stack: 'goal',
      barWidth: 20,
      data: [220, 182, 191, 234, 290, 330, 310],
    },
    {
      name: '预计得分',
      type: 'bar',
      stack: 'goal',
      barWidth: 20,
      data: [150, 212, 201, 154, 190, 330, 410],
    },
  ],
})

const dialogVisible = ref(false)
function showDialog() {
  dialogVisible.value = true
}

const tableData = ref([
  { businessSubject: '主体1', indicatorType: '类型1', department: '部门1', count: 10, score: 100, currentScore: 80, expectedScore: 90, completion: '完成', year: '2025' },
  { businessSubject: '主体2', indicatorType: '类型2', department: '部门2', count: 20, score: 200, currentScore: 160, expectedScore: 180, completion: '完成', year: '2025' },
  { businessSubject: '主体3', indicatorType: '类型3', department: '部门3', count: 30, score: 300, currentScore: 240, expectedScore: 270, completion: '完成', year: '2025' },
  { businessSubject: '主体4', indicatorType: '类型4', department: '部门4', count: 40, score: 400, currentScore: 320, expectedScore: 360, completion: '完成', year: '2025' },
  { businessSubject: '主体5', indicatorType: '类型5', department: '部门5', count: 50, score: 500, currentScore: 400, expectedScore: 450, completion: '完成', year: '2025' },
  { businessSubject: '主体6', indicatorType: '类型6', department: '部门6', count: 60, score: 600, currentScore: 480, expectedScore: 540, completion: '完成', year: '2025' },
  { businessSubject: '主体7', indicatorType: '类型7', department: '部门7', count: 70, score: 700, currentScore: 560, expectedScore: 630, completion: '完成', year: '2025' },
  { businessSubject: '主体8', indicatorType: '类型8', department: '部门8', count: 80, score: 800, currentScore: 640, expectedScore: 720, completion: '完成', year: '2025' },
  { businessSubject: '主体9', indicatorType: '类型9', department: '部门9', count: 90, score: 900, currentScore: 720, expectedScore: 810, completion: '完成', year: '2025' },
  { businessSubject: '主体10', indicatorType: '类型10', department: '部门10', count: 100, score: 1000, currentScore: 800, expectedScore: 900, completion: '完成', year: '2025' },
])
const paginationData = reactive({
  pageSize: 10,
  pageNum: 1,
  total: 0,
})

function indexMethod(index) {
  return (paginationData.pageNum - 1) * paginationData.pageSize + index + 1
}

defineExpose({
  showDialog,
})
</script>

<style lang="scss" scoped>
.goal_management {
  padding-bottom: 18px;
}
</style>
