import request from '@/utils/request'

// 查询合同池列表
export function list(query) {
  return request({
    url: '/purchase/odsContract/list',
    method: 'get',
    params: query,
  })
}

// 查询合同池详细
export function getDetail(query) {
  return request({
    url: '/purchase/odsContract/detail',
    method: 'get',
    params: query,
  })
}
// 保存共享用户
export function saveShare(query) {
  return request({
    url: '/purchase/odsContract/share/save',
    method: 'post',
    data: query,
  })
}

// 查询共享用户信息
export function getShareDetail(query) {
  return request({
    url: '/purchase/odsContract/share/list',
    method: 'get',
    params: query,
  })
}
