import { get, post, put, remove } from '@/utils/alova.js'

/**
 * 下载中心-制度文件
 * @param {*} data
 * @returns
 */
export function getInstitutionDocument(data) {
  return get(`/system/workbench/downloadCenter/institutionDocument`, data)
}

/**
 * 下载中心，模板下载
 * @param {*} data
 * @returns
 */
export function getTemplateFile(data) {
  return get(`/system/workbench/downloadCenter/template`, data)
}

/**
 * 获取下载中心统计数据
 * @param {*} data
 * @returns
 */
export function getDownloadCenterCount(data) {
  return get(`/system/download/lastCount`, data)
}

/**
 * 通知公告
 * @param {*} type
 * @param {*} data
 * @returns
 */
export function getNoticeListByType(type, data) {
  return get(`/system/workbench/notice/${type}`, data)
}

/**
 * 通知公告近一个月新增的数量统计
 * @param {*} type
 * @param {*} data
 * @returns
 */
export function getNoticeCount(type, data) {
  return get(`/system/workbench/noticeCount`, data)
}

/**
 * 获取顶部数据统计信息
 * @returns
 */
export function getHomeHeaderData() {
  return get(`/system/workbench/topTotalCount`)
}

/**
 * 获取消息列表
 * @param {*} type
 * @returns
 */
export function getMessage() {
  return get(`/wflow/notify`)
}

/**
 * 获取职能入口列表
 * @param {*} data
 * @returns
 */
export function getAllCompetency(data) {
  return get(`/system/workbench/allCompetency`, data)
}

/**
 * 获取流程入口快捷菜单
 * @param {*} data
 * @returns
 */
export function getEnterList(config) {
  return get(`/system/process/entry/listByUser`, {}, config)
}

/**
 * 新增流程入口
 * @param {*} data
 * @returns
 */
export function addEnter(data) {
  return put(`/system/process/entry/add`, data)
}

/**
 * 删除流程入口快捷菜单
 * @param {*} bid
 * @param {*} data
 * @returns
 */
export function deleteEnter(bid, data) {
  return remove(`/system/process/entry/delete/${bid}`, data)
}

/**
 * 修改流程入口
 * @param {*} data
 * @returns
 */
export function updateEnterr(data) {
  return post(`/system/process/entry/update`, data)
}

/**
 * 获取流程入口快捷菜单树
 * @returns
 */
export function getEnterMenuTree() {
  return get(`/sso/getRouters`)
}

/**
 * 获取流程中心各类待办数量
 * @returns
 */
export function getProcessCenterTodoCount() {
  return get(`/wflow/process/todoTask/instance/count`)
}

/**
 * 流程中心待办列表
 * @returns
 */
export function getProcessCenterTodoList(data) {
  return post(`/wflow/process/todoTask/todoList`, data)
}

/**
 * 获取流程中心待阅列表数据
 * @param {*} data
 * @returns
 */
export function getProcessCenterCcMeList(data) {
  return post(`/wflow/process/todoTask/ccMeList`, data)
}

/**
 * 获取流程中心已办列表数据（自己的流程办完了）
 * @param {*} data
 * @returns
 */
export function getProcessCenterIdoList(data) {
  return post(`/wflow/process/todoTask/idoList`, data)
}

/**
 * 获取流程中心办结列表数据，整体办完
 * @param {*} data
 * @returns
 */
export function getProcessCenterFinishedList(data) {
  return post(`/wflow/process/todoTask/finishedList`, data)
}

/**
 * 获取流程中心我发起的列表数据
 * @param {*} data
 * @returns
 */
export function getProcessCenterMySubmitList(data) {
  return post(`/wflow/process/todoTask/mySubmittedList`, data)
}
