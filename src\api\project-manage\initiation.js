import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 暂存或者更新项目立项
 * @param {*} data
 * @returns
 */
export function create_update_initiation(data) {
  return request({
    url: `${prefix}/establishment/createOrUpdate`,
    method: 'post',
    data,
  })
}

/**
 * 删除项目立项
 * @param {*} data
 * @returns
 */
export function delete_initiation(id) {
  return request({
    url: `${prefix}/establishment/del/${id}`,
    method: 'post',
  })
}

/**
 * 查询项目立项详情
 * @param {*} data
 * @returns
 */
export function search_initiation(id) {
  return request({
    url: `${prefix}/establishment/details/${id}`,
    method: 'get',
  })
}
/**
 * 获取项目立项列表
 * @param {*} data
 * @returns
 */
export function search_initiation_list(data) {
  return request({
    url: `${prefix}/establishment/pageList`,
    method: 'post',
    data,
  })
}

/**
 * 提交项目立项信息
 * @param {*} data
 * @returns
 */
export function submit_initiation(data) {
  return request({
    url: `${prefix}/establishment/submit`,
    method: 'post',
    data,
  })
}

/**
 * 获取可以选择的合同列表，用于选择项目后获取与该项目有关的合同
 * @param {*} data
 * @returns
 */
export function getContractByInitiation(data) {
  return request({
    url: `/purchase/psmContract/list`,
    method: 'get',
    params: data,
  })
}

/**
 * 客商补录列表
 */
export function getCustomerMerchantPage() {
  return request({
    url: '/project/merchant/page',
    method: 'post',
    data: {
      pageNum: 1,
      pageSize: 1000,
      fillStatus: 3,
    },
  })
}
/**
 * 查询项目详情
 */
export function getProjectDetail(projectCode) {
  return request({
    url: `/project/business/library/get/byCode`,
    method: 'get',
    params: {
      projectCode,
    },
  })
}
/**
 * 通过交付主体的Code 查询对应的部门Id
 * @param {*} data
 * @returns
 */
export function onSearchDeptId(code) {
  return request({
    url: `/system/dept/list?sysCode=${code}`,
    method: 'get',
  })
}
