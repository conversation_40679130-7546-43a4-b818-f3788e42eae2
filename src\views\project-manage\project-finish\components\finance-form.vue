<template>
  <ElDivider>财务决算</ElDivider>
  <ElRow :gutter="20">
    <ElCol :span="6">
      <ElFormItem
        label="收入(元)"
        prop="financialSettlement.income">
        <ElInput
          v-model="formData.financialSettlement.income"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="采购成本(元)">
        <ElInput
          v-model="formData.financialSettlement.costProcurement"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="人力成本(元)">
        <ElInput
          v-model="formData.financialSettlement.costLabor"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目管理费(元)">
        <ElInput
          v-model="formData.financialSettlement.costManagement"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="售前阶段费用(元)">
        <ElInput
          v-model="formData.financialSettlement.costPresale"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="交付阶段费用(元)">
        <ElInput
          v-model="formData.financialSettlement.costDelivery"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="回款金额(元)">
        <ElInput
          v-model="formData.financialSettlement.paymentReceived"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="付款金额(元)">
        <ElInput
          v-model="formData.financialSettlement.paymentPaid"
          :disabled="formType !== 'process'" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目毛利(元)">
        <ElInput
          disabled
          :value="
            formData.financialSettlement.income - formData.financialSettlement.costProcurement
          "
          :model-value="formData.financialSettlement.grossProfit" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem label="项目毛利率(%)">
        <ElInput
          disabled
          :value="`${((
            (formData.financialSettlement.income || 0)
            - (formData.financialSettlement.costProcurement || 0)
          ) / (formData.financialSettlement.income || 1) * 100).toFixed(2)}%
          `"
          :model-value="formData.financialSettlement.profitMargin" />
      </ElFormItem>
    </ElCol>
  </ElRow>
</template>

<script setup>
const { formType } = defineProps({
  formType: {
    type: String,
    default: '',
  },
})
const formData = defineModel('formData', {
  type: Object,
  default: () => ({}),
})
</script>

<style lang="scss" scoped>

</style>
