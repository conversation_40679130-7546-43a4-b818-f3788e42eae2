import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 获取计划里程碑列表
 * @param {*} data
 * @returns
 */
export function getPlanMilestonesList(data) {
  return request({
    url: `${prefix}/milestone/plan/pageList`,
    method: 'post',
    data,
  })
}

/**
 * 新增或更新里程碑
 * @param {*} data
 * @returns
 */
export function createOrUpdateMilestone(data) {
  return request({
    url: `${prefix}/milestone/createOrUpdate`,
    method: 'post',
    data,
  })
}

/**
 * 获取可以选择的项目列表，用于创建里程碑时选择项目
 * @param {*} data
 * @returns
 */
export function getProjectListForMilestone(data) {
  return request({
    url: `${prefix}/projectLibrary/pageList`,
    method: 'post',
    data,
  })
}

/**
 * 获取可以选择的合同列表，用于创建里程碑时选择合同
 * @param {*} data
 * @returns
 */
export function getContractByProjectForMilestone(data) {
  return request({
    url: `/purchase/psmContract/list`,
    method: 'get',
    params: data,
  })
}

/**
 * 获取里程碑详情
 * @param {*} id
 * @param {*} data
 * @returns
 */
export function getMilestoneDetail(id, data) {
  return request({
    url: `${prefix}/milestone/plan/details/${id}`,
    method: 'get',
    params: data,
  })
}
