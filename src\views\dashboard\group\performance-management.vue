<template>
  <div class="performance_management">
    <ElRadioGroup
      v-model="type"
      size="small">
      <ElRadioButton
        label="周"
        value="week" />
      <ElRadioButton
        label="月"
        value="month" />
      <ElRadioButton
        label="年"
        value="year" />
    </ElRadioGroup>
    <div class="flex overflow-auto mt-[16px]">
      <div class="chart">
        <p class="text-[16px] text-[#000]/[.88] mb-[12px] font-[600]">
          出勤天数
        </p>
        <div class="flex-1">
          <VChart
            :option="option1"
            autoresize />
        </div>
      </div>
      <div class="chart">
        <p class="text-[16px] text-[#000]/[.88] mb-[12px] font-[600]">
          工时
        </p>
        <div class="flex-1">
          <VChart
            :option="option2"
            autoresize />
        </div>
      </div>
      <div class="chart">
        <p class="text-[16px] text-[#000]/[.88] mb-[12px] font-[600]">
          年度产出
        </p>
        <div class="flex-1">
          <VChart
            :option="option3"
            autoresize />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const type = ref('week')

const option1 = ref({
  color: ['#165DFF', '#14C9C9'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0, 0, 0, 0.04)',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    icon: 'circle',
  },
  grid: {
    top: 32,
    right: 45,
    bottom: 0,
    left: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#E5E6EB',
      },
    },
  },
  yAxis: {
    type: 'category',
    data: ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#C1C5CC',
      },
    },
    inverse: true,
  },
  dataZoom: {
    type: 'inside',
    yAxisIndex: 0,
    orient: 'vertical',
    startValue: 0,
    endValue: 4,
    zoomOnMouseWheel: false,
    moveOnMouseWheel: true,
  },
  series: [
    {
      name: '出勤',
      type: 'bar',
      stack: 'goal',
      barWidth: 22,
      data: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    },
    {
      name: '差旅',
      type: 'bar',
      stack: 'goal',
      barWidth: 20,
      data: [8, 7, 6, 5, 4, 3, 2, 1],
    },
  ],
})

const option2 = ref({
  color: ['#F7BA1E', '#FF7D00'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.04)',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    icon: 'circle',
  },
  grid: {
    top: 32,
    right: 45,
    bottom: 0,
    left: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#E5E6EB',
      },
    },
  },
  yAxis: {
    type: 'category',
    data: ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#C1C5CC',
      },
    },
    inverse: true,
  },
  dataZoom: {
    type: 'inside',
    yAxisIndex: 0,
    orient: 'vertical',
    startValue: 0,
    endValue: 4,
    zoomOnMouseWheel: false,
    moveOnMouseWheel: true,
  },
  series: [
    {
      name: '业务工时',
      type: 'bar',
      stack: 'goal',
      barWidth: 22,
      data: [1, 2, 3, 4, 5, 6, 7, 8],
    },
    {
      name: '非业务工时',
      type: 'bar',
      stack: 'goal',
      barWidth: 20,
      data: [8, 7, 6, 5, 4, 3, 2, 1],
    },
  ],
})

const option3 = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.04)',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    icon: 'circle',
  },
  grid: {
    top: 32,
    right: 45,
    bottom: 0,
    left: 0,
    containLabel: true,
  },
  yAxis: [
    {
      type: 'category',
      data: ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'],
      axisPointer: {
        type: 'shadow',
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#C1C5CC',
        },
      },
      inverse: true,
    },
  ],
  xAxis: [
    {
      type: 'value',
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
  ],
  dataZoom: {
    type: 'inside',
    yAxisIndex: 0,
    orient: 'vertical',
    startValue: 0,
    endValue: 4,
    zoomOnMouseWheel: false,
    moveOnMouseWheel: true,
  },
  series: [
    {
      name: '产出合同',
      color: '#9FDB1D',
      type: 'bar',
      barWidth: 11,
      data: [1, 2, 3, 4, 5, 6, 7, 8],
    },
    {
      name: '产出收入',
      color: '#3491FA',
      type: 'bar',
      xAxisIndex: 1,
      barWidth: 11,
      data: [8, 7, 6, 5, 4, 3, 2, 1],
    },
  ],
})
</script>

<style lang="scss" scoped>
.performance_management {
  padding-bottom: 18px;

  .chart {
    display: flex;
    flex: none;
    flex-direction: column;
    width: 535px;
    height: 330px;
    margin-right: 12px;
    padding: 12px;
    border-radius: 10px;
    background: rgb(0 0 0 / 2%);

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
