<template>
  <ElDialog
    v-model="model"
    @open="onOpen">
    <template #header>
      <Title is-hidden>
        研发项目收益预测明细
      </Title>
    </template>
    <ElForm
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="140"
      label-position="top">
      <ElRow :gutter="20">
        <ElCol :span="8">
          <ElFormItem
            prop="name"
            label="收益类型">
            <ElInput
              v-model="form.name"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem
            prop="category"
            label="周期">
            <ElInput
              v-model="form.name"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem
            prop="categoryName"
            label="收入来源">
            <ElInput
              v-model="form.name"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="16">
          <ElFormItem
            prop="standardPrice"
            label="收入描述">
            <ElInput
              v-model="form.name"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem
            prop="quantity"
            label="预计收入金额">
            <ElInput
              v-model="form.name"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            prop="discount"
            label="备注">
            <ElInput
              v-model="form.name"
              :rows="2"
              type="textarea"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton @click="onCancel">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="onConfirm">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import Title from '@/components/Title'
import { isEmpty } from '@/utils/hooks'
import regexp from '@/utils/regexp'
import { ElButton, ElCol, ElDialog, ElForm, ElFormItem, ElInput, ElRow } from 'element-plus'

const { info } = defineProps({
  info: {
    type: [Object, null],
    default: null,
  },
})
const emit = defineEmits(['confirm', 'cancel'])

const rules = reactive({
  quantity: [
    { required: true, message: '请输入数量', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的数量', trigger: ['blur', 'change'] },
  ],
  standardPrice: [
    { required: true, message: '请输入标准价格', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的价格', trigger: ['blur', 'change'] },
  ],
  category: [
    { required: true, message: '请选择产品分类', trigger: 'change' },
  ],
  discountPrice: [
    { required: true, message: '请输入折扣价格', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的折扣价格', trigger: ['blur', 'change'] },
  ],
  discount: [
    { required: true, message: '请输入折扣', trigger: ['blur', 'change'] },
  ],
})
const formRef = useTemplateRef('formRef')
const form = ref({
  name: '',
  category: '',
  categoryName: '',
  standardPrice: '0.00',
  quantity: 0,
  discount: 0.00,
  discountPrice: 0,
  historicalDealPrice: '',
  selectProductReason: '',
})
const model = defineModel()
function onCancel() {
  formRef.value.resetFields()
  emit('cancel')
  model.value = false
}
function onConfirm() {
  formRef.value.validate().then(() => {
    emit('confirm', {
      formData: { ...form.value },
      dialogType: 'rjcp',
    })
    onCancel()
  })
}
function onOpen() {
  if (!isEmpty(info)) {
    form.value = { ...info }
  } else {
    delete form.value.uuid
    delete form.value.id
  }
}

watchEffect(() => {
  form.value.discountPrice = form.value.quantity * form.value.standardPrice * form.value.discount
})
</script>

<style lang="scss" scoped>
:deep(.custom_input_number) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
