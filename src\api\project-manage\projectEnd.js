import { get, post } from '@/utils/alova.js'
import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 暂存（创建）项目终止
 * @param {*} data
 * @returns
 */
export function createProjectEol(data) {
  return post(`${prefix}/eol/create`, data)
}
/**
 * 提交项目终止
 * @param {*} data
 * @returns
 */
export function submitProjectEol(data) {
  return post(`${prefix}/eol/create`, data)
}

/**
 * 修改项目终止
 * @param {*} id
 * @param {*} data
 * @returns
 */
export function updateProjectEol(id, data) {
  return post(`${prefix}/eol/update/${id}`, data)
}

/**
 * 获取项目终止列表
 * @param {*} data
 * @returns
 */
export function selectProjectEolList(data) {
  return request({
    url: `${prefix}/eol/pageList`,
    method: 'post',
    data,
  })
}

/**
 * 获取项目终止详情
 * @param {*} id
 * @returns
 */
export function getProjectEolDetail(id) {
  return request({
    url: `${prefix}/eol/details/${id}`,
    method: 'post',
  })
}

/**
 * 批量删除项目终止
 * @param {*} ids
 * @returns
 */
export function removeProjectEol(ids) {
  return request({
    url: `${prefix}/eol/removeBatch`,
    method: 'post',
    data:{idList:ids},
  })
}
