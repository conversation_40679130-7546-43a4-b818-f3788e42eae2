import request from '@/utils/request'

// 获取发票台账分页列表
export function getInvoiceLedgerPageList(params) {
  return request({
    url: `/finance/invoice-ledger/pageList`,
    method: 'post',
    data: params,
  })
}
// 发票冲红可选择销项票列表
export function getInvoiceCorrectList(params) {
  return request({
    url: `/finance/invoice-ledger/invoiceCorrectPageList`,
    method: 'post',
    data: params,
  })
}
// 根据合同编号 查询累计不含税金额
export function getSumAmountByContractNumber(params) {
  return request({
    url: `/finance/invoice-ledger/sumAmountByContractNumber`,
    method: 'get',
    params,
  })
}

// 根据项目编号 查询累计含税金额
export function getSumAmountIncludeTax(params) {
  return request({
    url: `/finance/invoice-ledger/sumAmountIncludeTax`,
    method: 'get',
    params,
  })
}

// 获取可确收的发票列表
export function getCanConfirmIncomeList(data) {
  return request({
    url: `/finance/invoice-ledger/canConfirmIncomeList`,
    method: 'post',
    data,
  })
}

// 暂存、更新虚拟发票
export function createOrUpdateVirtualInvoice(params) {
  return request({
    url: `/finance/invoice-ledger/createOrUpdate`,
    method: 'post',
    data: params,
  })
}

// 发票台账详情
export function getInvoiceLedgerDetail(params) {
  return request({
    url: `/finance/invoice-ledger/detail`,
    method: 'get',
    params,
  })
}

// 获取可冲销的虚拟发票列表
export function getCanOffsetList(data) {
  return request({
    url: `/finance/invoice-ledger/canOffsetList`,
    method: 'post',
    data,
  })
}
// 根据项目编号 查询 待冲销虚拟发票 相关信息
export function getSumOffsetInvoiceInfo(params) {
  return request({
    url: `/finance/invoice-ledger/sumOffsetInvoiceInfo`,
    method: 'get',
    params,
  })
}

// 发票冲红-可选择销项票列表
export function getInvoiceCorrectPageList(data) {
  return request({
    url: `/finance/invoice-ledger/invoiceCorrectPageList`,
    method: 'post',
    data,
  })
}
