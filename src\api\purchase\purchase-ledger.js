import { get, post, put } from '@/utils/alova'

/**
 * 采购台账列表
 */
export function getPurchaseLedgerPageList(data, config) {
  return post('/purchaseManage/ledger/pageList', data, config)
}

/**
 * 创建台账
 */
export function createPurchaseLedger(data, config) {
  return post('/purchaseManage/ledger/create', data, config)
}

/**
 * 编辑台账
 */
export function updatePurchaseLedger(data, config) {
  return put('/purchaseManage/ledger/updateByBid', data, config)
}

/**
 * 台账详情
 */
export function getPurchaseLedgerDetail(bid, params, config) {
  return get(`/purchaseManage/ledger/detail/${bid}`, params, config)
}

/**
 * 获取销售合同列表
 */
export function getPsmContractList(params, config) {
  return get('/purchase/psmContract/list', params, config)
}

/**
 * 获取销售合同详情（取对方主体列表）
 */
export function getPsmContractDetail(params, config) {
  return get('/purchase/psmContract/detail', params, config)
}

/**
 * 获取采购部门人员列表
 */
export function getlistByDeptCodeList(params, config) {
  return get('/system/user/listByDeptCode', params, config)
}

/**
 * 根据招采bid获取定标详情（查定标结果审批时间）
 */
export function getCalibrateDetail(params, config) {
  return get('/purchaseManage/standardization/get/procurementBid', params, config)
}

/**
 * 获取可以选择的项目列表，用于选择售前加交付项目
 */
export function getProjectListForPurchaseLedger(data, config) {
  return post('/project/projectLibrary/pageList', data, config)
}

/**
 * 我方主体名称
 */
export function getEntityList(params, config) {
  return get('/purchase/psmContract/odsCorporateEntity/list', params, config)
}
