<template>
  <Title style="margin-top: 20px;">
    研发项目收益预测
  </Title>
  <div style="float: right; margin-bottom: 10px">
    <ElButton
      type="primary"
      icon="plus"
      @click="onAddDialog()">
      新建
    </ElButton>
  </div>
  <ElTable
    border
    style="width: 100%;"
    :data="form.baseInfo.incomeDetails"
    :span-method="objectSpanMethod">
    <ElTableColumn
      label="收益类型"
      prop="incomeType"
      align="center" />
    <ElTableColumn
      label="周期"
      prop="period"
      align="center" />
    <ElTableColumn
      label="收入来源"
      prop="incomeSource"
      align="center" />
    <ElTableColumn
      label="收入描述"
      prop="incomeDescribe"
      align="center" />
    <ElTableColumn
      label="预计收入金额"
      prop="budgetRevenueAmount"
      align="center" />
    <ElTableColumn
      label="备注"
      prop="remark"
      align="center" />
    <ElTableColumn
      label="操作"
      prop="operate"
      align="center"
      fixed="right">
      <template #default="{ row }">
        <ElButton
          link
          type="danger"
          @click="onDeleteDialog(row)">
          删除
        </ElButton>
        <ElButton
          link
          type="warning"
          @click="onEditDialog(row)">
          编辑
        </ElButton>
      </template>
    </ElTableColumn>
  </ElTable>
  <YfxmSyycDialog
    v-model="showDialog"
    :info="rjcpDialogInfo"
    @confirm="onDialogConfirm"
    @cancel="resetDialogForm" />
</template>

<script setup>
import Title from '@/components/Title/index.vue'
import { generateUniqueKey } from '@/utils/hooks.js'
import YfxmSyycDialog from '@/views/project-development/project-application/form/components/yfxm-syyc-dialog.vue'
import { ElMessageBox } from 'element-plus'

const form = defineModel()

const showDialog = ref(false)

const rjcpDialogInfo = ref({})

function onDialogConfirm({ formData }) {
  // 修改
  if (formData?.id || formData?.uuid) {
    const index = form.value.baseInfo.incomeDetails.findIndex((item) => {
      if (item.uuid) {
        return item.uuid === formData.uuid
      }
      if (item.id) {
        return item.id === formData.id
      }
      return false
    })
    form.value.baseInfo.incomeDetails.splice(
      index,
      1,
      formData,
    )
    resetDialogForm()
  } else {
    // 新增时查找是否已经存在相同收益类型的数据，如果有则将其插入到最后一条相同数据的后面，否则直接插入到最后一条数据后面
    // 这里将相同收益类型的数据放在一起，目的是为了方便表格做合并列
    const index = form.value.baseInfo.incomeDetails.findLastIndex(item => item.incomeType === formData.incomeType)
    if (index !== -1) {
      form.value.baseInfo.incomeDetails.splice(index + 1, 0, {
        ...formData,
        uuid: generateUniqueKey(),
      })
    } else {
      form.value.baseInfo.incomeDetails.push({
        ...formData,
        uuid: generateUniqueKey(),
      })
    }
  }
}

function resetDialogForm() {
  rjcpDialogInfo.value = {}
}

function onAddDialog() {
  rjcpDialogInfo.value = {}
  showDialog.value = true
}

function onEditDialog(row) {
  rjcpDialogInfo.value = row
  showDialog.value = true
}

function onDeleteDialog(formData) {
  ElMessageBox({
    title: '警告',
    type: 'warning',
    message: '确定要删除该条记录吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        const index = form.value.baseInfo.incomeDetails.findIndex((item) => {
          if (item.uuid) {
            return item.uuid === formData.uuid
          }
          if (item.id) {
            return item.id === formData.id
          }
          return false
        })
        form.value.baseInfo.incomeDetails.splice(
          index,
          1,
        )
      }
      done()
    },
  })
}

function objectSpanMethod({ column, rowIndex, columnIndex }) {
  // 只处理第一列（columnIndex === 0）
  if (columnIndex === 0) {
    // 获取当前行和上一行的第一列数据
    const currentRow = form.value.baseInfo.incomeDetails[rowIndex]
    const prevRow = form.value.baseInfo.incomeDetails[rowIndex - 1]

    // 如果不是第一行，并且当前行的第一列数据与上一行相同
    if (rowIndex > 0 && currentRow[column.property] === prevRow[column.property]) {
      // 隐藏当前单元格（通过设置 rowspan 为 0）
      return {
        rowspan: 0,
        colspan: 0,
      }
    } else {
      // 如果是第一行，或者当前行数据与上一行不同，则需要计算合并的行数
      let rowspan = 1

      // 向下查找相同数据的行数
      for (let i = rowIndex + 1; i < form.value.baseInfo.incomeDetails.length; i++) {
        if (form.value.baseInfo.incomeDetails[i][column.property] === currentRow[column.property]) {
          rowspan++
        } else {
          break
        }
      }

      // 返回合并的行数
      return {
        rowspan,
        colspan: 1,
      }
    }
  }
}
</script>
