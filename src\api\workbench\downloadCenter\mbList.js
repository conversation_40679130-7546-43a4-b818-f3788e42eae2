import request from '@/utils/request'

// 列表数据
export function getList(data) {
  return request({
    url: '/system/template/list',
    method: 'get',
    params: data,
  })
}

// 详情
export function getDetails(data) {
  return request({
    url: '/system/template',
    method: 'post',
    data,
  })
}

// 创建模板下载
export function mbDownload(data) {
  return request({
    url: '/system/template',
    method: 'put',
    data,
  })
}

// 下载
export function downloadList(data) {
  return request({
    url: `/system/template/download/`,
    method: 'post',
    responseType: 'blob',
    data,
  })
}

// 模板下载已读未读
export function mbRead() {
  return request({
    url: '/system/template/readCondition',
    method: 'get',
  })
}

// 模板下载已读未读数量
export function readNum(data) {
  return request({
    url: '/system/template/signRead',
    method: 'post',
    data,
  })
}

// 置顶
export function pinned(data) {
  return request({
    url: '/system/template/top',
    method: 'post',
    data,
  })
}

// 模板下载详情
export function mbDetails(bid) {
  return request({
    url: `/system/template/${bid}`,
    method: 'get',
  })
}

// 删除模板文件
export function mbDelete(bid) {
  return request({
    url: `system/template/${bid}`,
    method: 'delete',
  })
}

// 新增模块使用的接口

// 新增模板
export function templateAdd(data) {
  return request ({
    url: '/system/template',
    method: 'put',
    data,
  })
}

// 修改文件夹和新增文件夹
export function templateeditAdd(data) {
  return request({
    url: '/system/template',
    method: 'post',
    data,
  })
}
