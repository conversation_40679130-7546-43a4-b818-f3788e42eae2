import { post } from '@/utils/alova'

/**
 * 获取研发项目分页列表
 */
export function getProjectLibraryPageList(data, config) {
  return post('/project/prd/project/library/pageList', data, config)
}

/**
 * 获取研发项目库基本详情数据
 */
export function getProjectLibraryBaseInfoDetail(prdProjectCode, config) {
  return post(`/project/prd/project/library/detail/baseInfo/${prdProjectCode}`, null, config)
}

/**
 * 获取研发项目库变更记录列表数据
 */
export function getProjectLibraryChangedRecordList(data, config) {
  return post('/project/prd/project/library/detail/change/list', data, config)
}

/**
 * 获取研发项目库验收记录列表数据
 */
export function getProjectLibraryAcceptanceRecordList(data, config) {
  return post('/project/prd/project/library/detail/checkAccept/list', data, config)
}

/**
 * 获取研发项目库外包人员列表数据
 */
export function getProjectLibraryOutsourcedPersonList(data, config) {
  return post('/project/prd/project/library/detail/outsource/list', data, config)
}

/**
 * 获取研发项目库工时明细列表数据
 */
export function getProjectLibraryWorkHourList(data, config) {
  return post('/project/prd/project/library/detail/workHour/list', data, config)
}

/**
 * 获取研发项目库采购合同列表数据
 */
export function getProjectLibraryPurchaseContractList(data, config) {
  return post('/project/prd/project/library/detail/contract/list', data, config)
}

/**
 * 获取交付项目库分页列表
 */
export function getProjectLibraryList(data, config) {
  return post('/project/projectLibrary/pageList', data, config)
}
