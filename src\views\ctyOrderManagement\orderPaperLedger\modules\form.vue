<template>
  <DefaultContainer v-loading="loading">
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          订单纸质台账
        </div>
      </div>
      <div
        v-if="!props.id"
        class="right">
        <ElButton @click="router.back()">
          取消
        </ElButton>
        <ElButton
          :loading="saveLoading"
          plain
          type="primary"
          @click="onSubmit(false)">
          暂存
        </ElButton>
        <ElButton
          :loading="saveLoading"
          type="primary"
          @click="onSubmit(true)">
          提交
        </ElButton>
      </div>
    </div>
    <div class="content">
      <ElForm
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="150px"
        :disabled="props.id !== undefined">
        <ElDivider />
        <ElFormItem
          class="form-item"
          label="年度"
          prop="year">
          <ElDatePicker
            v-model="form.year"
            value-format="YYYY"
            type="year"
            placeholder="请选择年度" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单部门"
          prop="deptId">
          <ElSelect
            v-model="form.deptName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef2.show())
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="经办人"
          prop="handledBy">
          <ElSelect
            v-model="form.handledByName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (orgPickerRef.show())
            " />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单编号"
          prop="orderNumber">
          <ElInput
            v-model="form.orderNumber"
            placeholder="请输入订单编号" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单名称"
          prop="orderName">
          <ElInput
            v-model="form.orderName"
            placeholder="请输入订单名称" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单性质"
          prop="orderNature">
          <ElSelect
            v-model="form.orderNature"
            clearable
            placeholder="请选择订单性质">
            <ElOption
              v-for="item in orderNatureList"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="应用系统名称"
          prop="ctAppId"
          style="width: 95%">
          <ElSelect
            v-model="form.ctAppName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectApplicationModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="客户名称"
          prop="customerCode">
          <ElInput
            v-model="form.customerName"
            disabled
            placeholder="选择应用系统后带入" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="项目阶段"
          prop="projectPhase">
          <ElSelect
            v-model="form.projectPhase"
            placeholder="请选择"
            @change="onChangeProjectPhase">
            <ElOption
              label="预立项阶段"
              value="预立项阶段" />
            <ElOption
              label="交付阶段"
              value="交付阶段" />
          </ElSelect>
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="项目名称"
          style="width: 95%"
          prop="projectName">
          <ElSelect
            v-model="form.projectName"
            placeholder="请选择"
            @visible-change="
              (change) => change && (modelIsShow.selectProjectModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="项目编号"
          prop="projectCode">
          <ElInput
            v-model="form.projectCode"
            disabled
            placeholder="选择项目后自动填入" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="销售合同名称"
          style="width: 95%"
          prop="saleContractName">
          <ElSelect
            v-model="form.saleContractName"
            placeholder="请选择"
            :disabled="!form.projectCode"
            @visible-change="
              (change) => change && (modelIsShow.contractModal = true)
            " />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="销售合同编号"
          prop="saleContractNumber">
          <ElInput
            v-model="form.saleContractNumber"
            disabled
            placeholder="选择销售合同后带入" />
        </ElFormItem>
        <br>
        <ElFormItem
          class="form-item"
          label="云服务实际开通日期"
          prop="serviceStartTime">
          <ElDatePicker
            v-model="form.serviceStartTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledStartDate"
            style="width: 100%"
            placeholder="请选择服务计划起始日期" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="云服务实际结算日期"
          prop="serviceEndTime">
          <ElDatePicker
            v-model="form.serviceEndTime"
            type="date"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledEndDate"
            style="width: 100%"
            placeholder="请选择服务计划结束日期" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="履约周期(天)"
          prop="servicePerformanceCycle">
          <ElInput
            v-model="form.servicePerformanceCycle"
            disabled
            placeholder="请选择" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="订单总金额(元)"
          prop="orderAmount">
          <ElInputNumber
            v-model="form.orderAmount"
            :precision="2"
            :min="0"
            controls-position="right"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="备注"
          prop="remark"
          style="width: 95%">
          <ElInput
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注" />
        </ElFormItem>
        <div
          class="attachment_wrapper"
          style="margin-bottom: 10px">
          <ElUpload
            ref="uploadRef"
            v-model:file-list="fileList"
            class="custom_upload"
            :action="uploadUrl"
            auto-upload
            :on-success="onFileUploadSuccess"
            :on-error="onFileUploadFail"
            multiple
            :on-preview="handlePreview"
            style="padding: 0 20px">
            <template #trigger>
              <ElButton
                link
                icon="link">
                <span style="color: red">* </span>上传用印订单
              </ElButton>
            </template>
            <ElButton
              type="primary"
              style="float: right"
              @click="selectFile()">
              上传
            </ElButton>
          </ElUpload>
        </div>
      </ElForm>
    </div>

    <SelectProjectModal
      v-model="modelIsShow.selectProjectModal"
      :project-phase="form.projectPhase"
      @select-item="onChangeProject" />
    <SelectContractModal
      v-model="modelIsShow.contractModal"
      :project-code="searchProjectCode"
      @select-item="onChangeContract" />
    <ApplicationSelectModal
      v-model="modelIsShow.selectApplicationModal"
      @select-item="onChangeApplication" />
    <OrgPicker
      ref="orgPickerRef"
      type="user"
      :multiple="false"
      title="选择经办人"
      @ok="handleSelected" />
    <OrgPicker
      ref="orgPickerRef2"
      type="dept"
      :multiple="false"
      title="选择订单部门"
      @ok="handleSelectedDepartment" />
  </DefaultContainer>
</template>

<script setup>
import {
  createOrUpdateOrderPaperLedger,
  getOrderPaperLedgerDetail,
  submitOrderPaperLedger,
} from '@/api/ctyOrderManagement/orderPaperLedger.js'
import { getCurrentProcessId } from '@/api/wflow-pro'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import useUserStore from '@/store/modules/user.js'
import { deepClone } from '@/utils'
import ApplicationSelectModal from '@/views/ctyOrderManagement/modules/ApplicationSelectModal.vue'
import SelectContractModal from '@/views/financialManagement/modules/SelectContractModal.vue'
import SelectProjectModal from '@/views/financialManagement/modules/SelectProjectModal.vue'
import { Back } from '@element-plus/icons-vue'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'
import { dayjs, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
  instanceId: {
    type: String,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  forms: {
    type: Object,
    default: () => ({}),
  },
})

const router = useRouter()
const formRef = ref(null)
const form = ref({
  projectPhase: '交付阶段',
})

const modelIsShow = reactive({
  selectProjectModal: false,
  contractModal: false,
  selectApplicationModal: false,
})

const searchProjectCode = computed(() => {
  if (form.value.projectCode) {
    return form.value.projectCode.split('-D')[0]
  }
  return ''
})

const uploadUrl = ref(`${import.meta.env.VITE_APP_BASE_API}/file/api/v1/uploads`) // 上传的服务器地址
const fileList = ref([])
const orgPickerRef = ref()
const orderNatureList = ref([
  { label: '新签', value: '新签' },
  { label: '续费', value: '续费' },
  { label: '撤销', value: '撤销' },
])
// 监听服务计划开始和结束时间的变化
watch(
  () => [form.value.serviceStartTime, form.value.serviceEndTime],
  ([start, end]) => {
    if (start && end) {
      const cycle = dayjs(end).diff(dayjs(start), 'day')
      form.value.servicePerformanceCycle = cycle || 0
    } else {
      form.value.servicePerformanceCycle = 0
    }
  },
)
const rules = ref({
  year: [
    { required: true, message: '请选择年度', trigger: 'change' },
  ],
  deptId: [
    { required: true, message: '请选择订单部门', trigger: 'change' },
  ],
  handledBy: [
    { required: true, message: '请选择经办人', trigger: 'change' },
  ],
  orderNumber: [
    { required: true, message: '请输入订单编号', trigger: 'change' },
  ],
  orderName: [
    { required: true, message: '请输入订单名称', trigger: 'change' },
  ],
  orderNature: [
    { required: true, message: '请选择订单性质', trigger: 'change' },
  ],
  ctAppId: [
    { required: true, message: '请选择应用系统名称', trigger: 'change' },
  ],
  customerCode: [
    { required: true, message: '请选择客户名称', trigger: 'change' },
  ],
  projectPhase: [
    { required: true, message: '请选择项目阶段', trigger: 'change' },
  ],
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' },
  ],
  saleContractName: [
    { required: true, message: '请选择销售合同名称', trigger: 'change' },
  ],
  serviceStartTime: [
    { required: true, message: '请选择云服务实际开通日期', trigger: 'change' },
  ],
  serviceEndTime: [
    { required: true, message: '请选择云服务实际结算日期', trigger: 'change' },
  ],
  servicePerformanceCycle: [
    { required: true, message: '请选择履约周期', trigger: 'change' },
  ],
  orderAmount: [
    { required: true, message: '请输入订单总金额', trigger: 'change' },
  ],
})

const id = ref('')
const loading = ref(false)
const saveLoading = ref(false)

onMounted(() => {
  if (props.id || router.currentRoute.value.query.id) {
    id.value = props.id || router.currentRoute.value.query.id
    getFormData()
  } else {
    form.value.handledByName = useUserStore().userInfo.nickName
    form.value.handledBy = useUserStore().userInfo.userId
  }
})

async function getFormData() {
  loading.value = true
  try {
    const res = await getOrderPaperLedgerDetail({
      id: id.value,
    })
    console.log(res)
    form.value = deepClone(res.data)
    if (form.value.attachment) {
      try {
        let parsedArr = JSON.parse(form.value.attachment)
        parsedArr = parsedArr.map(item => ({
          name: item.match(/[^/\\?#]+$/)[0],
          url: item,
        }))
        fileList.value = parsedArr
      } catch (error) {
        console.log(error)
      }
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

function onChangeProjectPhase(value) {
  console.log(value, '--- value')
  // 更改项目阶段 清空项目信息
  form.value.projectName = ''
  form.value.projectCode = ''

  // 销售合同清空
  form.value.saleContractName = ''
  form.value.saleContractNumber = ''
  if (value === '交付阶段') {
    rules.value.saleContractName = [
      { required: true, message: '请选择销售合同名称', trigger: 'change' },
    ]
  } else {
    rules.value.saleContractName = []
  }
}

function onChangeProject(row) {
  form.value.projectName = row.projectName || row.bolProjectName
  form.value.projectCode = row.projectCode

  form.value.saleContractName = ''
  form.value.saleContractNumber = ''
}

function onChangeContract(contract) {
  // 合同
  if (contract) {
    form.value.saleContractName = contract.name
    form.value.saleContractNumber = contract.code
  } else {
    form.value.saleContractName = ''
    form.value.saleContractNumber = ''
  }
}

function onChangeApplication(row) {
  console.log(row, '--- row')
  form.value.ctAppName = row.appName
  form.value.ctAppId = row.id
  form.value.customerName = row.customerName
  form.value.customerCode = row.customerCode
}

function disabledStartDate(time) {
  if (form.value.serviceEndTime) {
    const endTime = new Date(form.value.serviceEndTime).getTime()
    const currentTime = time.getTime()
    // 不能选择开始日期当天及之前的日期
    return currentTime >= endTime
  }
  return false
}

function disabledEndDate(time) {
  if (form.value.serviceStartTime) {
    const startTime = new Date(form.value.serviceStartTime).getTime()
    const currentTime = time.getTime()
    // 不能选择开始日期当天及之前的日期
    return currentTime <= startTime
  }
  return false
}

const uploadRef = useTemplateRef('uploadRef')
function selectFile() {
  uploadRef.value.$el.querySelector('input').click()
}

function handlePreview(file) {
  if (file?.url) {
    window.open(file.url)
  }
  if (file?.response?.data) {
    window.open(file.response.data)
  }
}

function onFileUploadSuccess() {
  ElMessage.success('附件上传成功')
}

function onFileUploadFail() {
  ElMessage.error('附件上传失败')
}

function handleSelected(val) {
  console.log(val, '--- val')
  if (val && val.length > 0) {
    form.value.handledByName = val[0].name
    form.value.handledBy = val[0].id
  } else {
    form.value.handledByName = ''
    form.value.handledBy = ''
  }
}

async function onSubmit(isSubmit) {
  let valid = true
  if (isSubmit) {
    if (fileList.value.length === 0) {
      ElMessage.error('请上传用印订单')
      return
    }
    valid = await formRef.value.validate()
  }
  if (valid) {
    const data = getData()

    saveLoading.value = true
    try {
      if (isSubmit) {
        data.processDefId = await getCurrentProcessId('cty-order-paper-ledger')
        console.log(data.processDefId, '--- data.processDefId')
        //  提交
        const res = await submitOrderPaperLedger(data)
        if (res.code === 200) {
          ElMessage.success('提交成功')
          router.back()
        } else {
          ElMessage.error('提交失败')
        }
      } else {
        // 暂存
        const res = await createOrUpdateOrderPaperLedger(data)
        if (res.code === 200) {
          ElMessage.success('暂存成功')
          router.back()
        } else {
          ElMessage.error('暂存失败')
        }
      }
    } finally {
      saveLoading.value = false
    }
  }
}

function getData() {
  const data = deepClone(form.value)
  if (fileList.value.length > 0) {
    const addressArr = fileList.value
      .map(item => item.url || item.response.data)
      .filter(address => address)
    if (addressArr.length > 0) {
      data.attachment = JSON.stringify(addressArr)
    } else {
      data.attachment = ''
    }
  } else {
    data.attachment = ''
  }
  console.log(data, '--- data')
  return data
}

const orgPickerRef2 = ref()
function handleSelectedDepartment(rows) {
  if (rows.length > 0) {
    form.value.deptName = rows[0].name
    form.value.deptId = rows[0].id
  } else {
    form.value.deptName = ''
    form.value.deptId = ''
  }
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(form))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      // const data = getData()
      // data.processDefId = props.instanceId
      // const res = await submitWithApprove(data)
      // if (res.code === 200) {
      //   ElMessage.success('提交成功')
      //   return Promise.resolve()
      // } else {
      //   ElMessage.warning('提交失败')
      //   return Promise.reject(new Error('提交失败'))
      // }
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
  .header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important;

  /* 设置最小高度 */

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .plan-item {
    width: 100%;

    .form-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 10px;
    margin-bottom: 40px;
    border: 1px dashed #1677ff;
    border-radius: 6px;
    color: #1677ff;
    font-size: 14px;
    cursor: pointer;
  }

  .form-item {
    width: 360px;
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 12px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}
</style>

  <style lang="scss">
  .custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
