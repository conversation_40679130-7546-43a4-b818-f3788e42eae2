<template>
  <Container show-back>
    <div class="wrapper">
      <ElForm
        ref="formRef"
        label-position="top"
        :model="formData.baseInfo"
        :rules="rules">
        <BaseInfo v-model="formData" />
        <BudgetInfo v-model="formData" />
        <OwnProductsAndServices v-model="formData" />
        <DevProjectRevenueForecast v-model="formData" />
      </ElForm>
    </div>
    <template #headerRight>
      <div style="display: flex;flex: auto; justify-content: flex-end;">
        <ElButton
          type="primary"
          plain
          @click="temporarilyStoreForm">
          暂存
        </ElButton>
        <ElButton
          type="primary"
          @click="submitForm">
          提交
        </ElButton>
      </div>
    </template>
  </Container>
</template>

<script setup>
import * as apis from '@/api/project-development/project-application.js'
import * as wFlowApis from '@/api/wflow-pro.js'
import Container from '@/components/Container/index.vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import BaseInfo from './baseInfo.vue'
import BudgetInfo from './budgetInfo.vue'
import DevProjectRevenueForecast from './devProjectRevenueForecast.vue'
import OwnProductsAndServices from './ownProductsAndServices.vue'

const props = defineProps({
  id: {
    default: '',
  },
})
const router = useRouter()
const route = useRoute()

const formRef = useTemplateRef('formRef')

const formData = ref({
  baseInfo: {
    budgetInfo: { // 项目预算信息
      outsourcingCost: [], // 外采成本
      jobLaborCost: [], // 人力成本
      manageCost: [], // 项目管理费
      budgetOverview: { // 预算概览
        manageCost: [],
      },
    },
    selfOwnedProductsServicesDetail: [], // 自有产品服务明细
    incomeDetails: [], // 研发项目收益预测
  },
})
const rules = reactive({
  prdProjectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  principal: [{ required: true, message: '请选择项目名称', trigger: 'change' }],
  prdEntityDept: [{ required: true, message: '请选择业务主体', trigger: 'change' }],
  prdProjectCategory: [{ required: true, message: '请选择项目分类', trigger: 'change' }],
  businessType: [{ required: true, message: '请选择业务类别', trigger: 'change' }],
  rdMethod: [{ required: true, message: '请选择研发方法', trigger: 'change' }],
  projectInitiationDate: [{ required: true, message: '请选择立项日期', trigger: 'change' }],
  projectLeader: [{ required: true, message: '请选择项目分管领导', trigger: 'change' }],
  projectBasicInformation: [{ required: true, message: '请输入项目研发基本情况', trigger: 'change' }],
  fileUrlList: [{ required: true, message: '请上传附件', trigger: 'change' }],
})

async function temporarilyStoreForm() {
  formData.value.baseInfo.prdProjectCategory = Array.isArray(formData.value.baseInfo.prdProjectCategory) ? formData.value.baseInfo.prdProjectCategory.splice(-1)[0] : formData.value.baseInfo.prdProjectCategory
  // 附件
  formData.value.baseInfo.attachments = formData.value.baseInfo.fileUrlList ? formData.value.baseInfo.fileUrlList.map(item => item.url || item.response.data) : []
  formData.value.options = 0 // 0表示暂存
  const res = await apis.projectTemporaryStorageOrApplicationOrChange(formData.value)
  if (res.code === 200) {
    ElMessage.success('暂存成功')
    router.push('/project-development/project-application/list')
  }
}

function submitForm() {
  formRef.value.validate().then(async () => {
    formData.value.baseInfo.prdProjectCategory = Array.isArray(formData.value.baseInfo.prdProjectCategory) ? formData.value.baseInfo.prdProjectCategory.splice(-1)[0] : formData.value.baseInfo.prdProjectCategory
    // 附件
    formData.value.baseInfo.attachments = formData.value.baseInfo.fileUrlList ? formData.value.baseInfo.fileUrlList.map(item => item.url || item.response.data) : []
    formData.value.options = 1 // 表示提交
    const processDefId = await wFlowApis.getCurrentProcessId('development-project-application')
    formData.value.processDefId = processDefId
    const res = await apis.projectTemporaryStorageOrApplicationOrChange(formData.value)
    if (res.code === 200) {
      ElMessage.success('提交成功')
      router.push('/project-development/project-application/list')
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

onMounted(async () => {
  if (route.query.type === 'edit' && route.query.id) {
    const res = await apis.getApplicationDetail(route.query.id)
    // 兼容后端数据，可能返回为null
    res.budgetInfo = {
      jobLaborCost: res.budgetInfo?.jobLaborCost || [],
      outsourcingCost: res.budgetInfo?.outsourcingCost || [],
      ...res.budgetInfo,
    }
    res.incomeDetails = res.incomeDetails || []
    res.selfOwnedProductsServicesDetail = res.selfOwnedProductsServicesDetail || []
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.fileUrlList = res.attachments.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  } else if (props.id) {
    const res = await apis.getApplicationDetail(props.id)
    // 兼容后端数据，可能返回为null
    res.budgetInfo = {
      jobLaborCost: res.budgetInfo?.jobLaborCost || [],
      outsourcingCost: res.budgetInfo?.outsourcingCost || [],
      ...res.budgetInfo,
    }
    res.incomeDetails = res.incomeDetails || []
    res.selfOwnedProductsServicesDetail = res.selfOwnedProductsServicesDetail || []
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.fileUrlList = res.attachments.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  }
})

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(formData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve(readonly(unref(formData)))
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.wrapper {
  flex: auto;
  overflow-y: auto;
  padding: 10px 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
