<template>
  <ElDialog
    v-model="model"
    @open="onOpen">
    <template #header>
      <Title is-hidden>
        研发项目收益预测明细
      </Title>
    </template>
    <ElForm
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="140"
      label-position="top">
      <ElRow :gutter="20">
        <ElCol :span="8">
          <ElFormItem
            prop="incomeType"
            label="收益类型">
            <ElInput
              v-model="form.incomeType"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem
            prop="period"
            label="周期">
            <ElInput
              v-model="form.period"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem
            prop="incomeSource"
            label="收入来源">
            <ElInput
              v-model="form.incomeSource"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="16">
          <ElFormItem
            prop="incomeDescribe"
            label="收入描述">
            <ElInput
              v-model="form.incomeDescribe"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem
            prop="budgetRevenueAmount"
            label="预计收入金额">
            <ElInput
              v-model="form.budgetRevenueAmount"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            prop="remark"
            label="备注">
            <ElInput
              v-model="form.remark"
              :rows="2"
              type="textarea"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton @click="onCancel">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="onConfirm">
        确认
      </ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import Title from '@/components/Title'
import { isEmpty } from '@/utils/hooks'
import regexp from '@/utils/regexp'
import { ElButton, ElCol, ElDialog, ElForm, ElFormItem, ElInput, ElRow } from 'element-plus'

const { info } = defineProps({
  info: {
    type: [Object, null],
    default: null,
  },
})
const emit = defineEmits(['confirm', 'cancel'])

const rules = reactive({
  incomeType: [{ required: true, message: '请输入收入类型', trigger: ['blur', 'change'] }],
  period: [{ required: true, message: '请输入周期', trigger: ['blur', 'change'] }],
  incomeSource: [{ required: true, message: '请输入收入来源', trigger: 'change' }],
  incomeDescribe: [{ required: true, message: '请输入收入描述', trigger: ['blur', 'change'] }],
  budgetRevenueAmount: [
    { required: true, message: '请输入预计收入金额', trigger: ['blur', 'change'] },
    { pattern: regexp.naturalNumber, message: '请输入正确的金额', trigger: ['blur', 'change'] },
  ],
})
const formRef = useTemplateRef('formRef')
const form = ref({
  incomeType: '',
  period: '',
  incomeSource: '',
  incomeDescribe: '',
  budgetRevenueAmount: '',
  remark: '',
})
const model = defineModel()
function onCancel() {
  formRef.value.resetFields()
  emit('cancel')
  model.value = false
}
function onConfirm() {
  formRef.value.validate().then(() => {
    emit('confirm', {
      formData: { ...form.value },
      dialogType: 'yfxm-syyc',
    })
    onCancel()
  })
}
function onOpen() {
  if (!isEmpty(info)) {
    form.value = { ...info }
  } else {
    delete form.value.uuid
    delete form.value.id
  }
}
</script>

<style lang="scss" scoped>
:deep(.custom_input_number) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
