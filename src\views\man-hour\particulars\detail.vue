<template>
  <Container show-back>
    <template #headerRight>
      <div>
        <ElPopover
          width="750"
          trigger="click"
          placement="bottom-end">
          <template #reference>
            <ElButton type="primary">
              审批进度
            </ElButton>
          </template>
          <ElDescriptions
            border
            :column="2">
            <ElDescriptionsItem
              v-show="workDetail.workHourType !== '交付项目'"
              label="部门负责人">
              {{ workDetail.deptLeader }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-show="workDetail.workHourType !== '交付项目'"
              label="部门负责人审批意见">
              <div class="status-row">
                <div
                  :class="`status-icon status-icon-${workDetail.deptLeaderApproveStatus}`" />
                <div>
                  {{
                    approveStatusProgress[workDetail.deptLeaderApproveStatus]
                  }}
                </div>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-show="workDetail.workHourType !== '交付项目'"
              label="部门负责人审批时间"
              :span="2">
              {{ workDetail.deptLeaderApproveTime }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-show="
                workDetail.deptLeaderApproveStatus === -1
                  && workDetail.workHourType !== '交付项目'
              "
              label="拒绝理由"
              :span="2">
              {{ workDetail.deptLeaderRefuseReason }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-show="workDetail.workHourType !== '通用项目'"
              label="项目经理">
              {{ workDetail.projectManager }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-show="workDetail.workHourType !== '通用项目'"
              label="项目经理审批意见">
              <div class="status-row">
                <div
                  :class="`status-icon status-icon-${workDetail.projectManagerApproveStatus}`" />
                <div>
                  {{
                    approveStatusProgress[
                      workDetail.projectManagerApproveStatus
                    ]
                  }}
                </div>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-show="workDetail.workHourType !== '通用项目'"
              label="项目经理审批时间"
              :span="2">
              {{ workDetail.projectManagerApproveTime }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-show="
                workDetail.projectManagerApproveStatus === -1
                  && workDetail.workHourType !== '通用项目'
              "
              label="拒绝理由"
              :span="2">
              {{ workDetail.projectManagerRefuseReason }}
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElPopover>
      </div>
    </template>
    <div
      v-loading="loading"
      class="content">
      <div class="informant-info">
        <ElDescriptions
          border
          :column="4">
          <template #title>
            <h3 style="font-weight: 600">
              填报人信息
            </h3>
          </template>
          <ElDescriptionsItem label="填报人">
            {{ workDetail.submitter }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="人员编号">
            {{ workDetail.createBy }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="填报部门">
            {{ workDetail.deptName }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="部门负责人">
            {{ workDetail.deptLeader }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>
      <div class="hour-info">
        <h3 style="margin-bottom: 20px; font-weight: 600">
          工时信息
        </h3>
        <div class="hour-info__list">
          <ElDescriptions
            border
            :column="4">
            <ElDescriptionsItem
              :span="2"
              label="审批状态">
              <div class="status-row">
                <div
                  :class="`status-icon status-icon-${workDetail.approveStatus + 3}`" />
                <div>
                  {{ approveStatus[workDetail.approveStatus] }}
                </div>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem
              :span="2"
              label="工时明细单号">
              {{ workDetail.timesheetDetailCode }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="工时类型">
              <ElTag
                v-if="!!workDetail.workHourType"
                :type="getWorkHourType(workDetail.workHourType)">
                {{ workDetail.workHourType }}
              </ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="工时日期">
              {{ workDetail.workDate }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="工时时长(天)">
              {{ workDetail.submitDays }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="工时属性">
              <ElTag :type="getWorkHourAttribute(workDetail.workAttribute)">
                {{ workPropertyOptions[workDetail.workAttribute]?.label }}
              </ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-if="workDetail.workHourType !== '通用项目'"
              :span="2"
              label="所属项目名称">
              {{ workDetail.projectName }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-if="workDetail.workHourType !== '通用项目'"
              :span="2"
              label="所属项目编号">
              {{ workDetail.projectCode }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              v-if="
                workDetail.workHourType !== '交付项目'
                  && workDetail.workHourType !== '产品研发'
                  && workDetail.workHourType !== '通用项目'
              "
              :span="4"
              label="跟进记录">
              <div
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                ">
                <ElButton
                  type="primary"
                  @click="drawerVisible = true">
                  查看跟进记录
                </ElButton>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem
              :span="4"
              label="查看工作内容">
              {{ workDetail.workContent }}
            </ElDescriptionsItem>
            <ElDescriptionsItem
              :span="4"
              label="备注">
              {{ workDetail.remark }}
            </ElDescriptionsItem>
          </ElDescriptions>
          <ElDrawer
            v-model="drawerVisible"
            size="45%">
            <template #header>
              <h3 style="color: #000; font-weight: 600">
                跟进记录
              </h3>
            </template>
            <RecordDrawer :record="workDetail.followRecord" />
          </ElDrawer>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup>
import * as api from '@/api/man-hour/approval.js'
import Container from '@/components/Container/index.vue'
import useUserStore from '@/store/modules/user.js'
import RecordDrawer from '@/views/man-hour/approval/components/RecordDrawer.vue'
import {
  workPropertyOptions,
  workTypeOptions,
} from '@/views/man-hour/approval/config.js'
import { approveStatus, approveStatusProgress } from './config.js'

const route = useRoute()

const workDetail = ref({})
const drawerVisible = ref(false)
const loading = ref(false)

function getWorkHourType(type) {
  return workTypeOptions.find(item => item.value === type)?.type || 'primary'
}

function getWorkHourAttribute(type) {
  return (
    workPropertyOptions.find(item => item.value === type)?.type || 'primary'
  )
}

async function getWorkDetail() {
  loading.value = true
  try {
    const res = await api.getWorkDetail(route.query.bid)
    res.data.myapproveStatus
      = useUserStore().id === +res.data.deptLeaderUserId
        ? res.data.deptLeaderApproveStatus
        : res.data.projectManagerApproveStatus
    workDetail.value = res.data
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getWorkDetail()
})
</script>

<style scoped lang="scss">
.content {
  position: relative;
  overflow: auto;
  height: 100%;
  padding: 18px 14px;
  border-radius: 10px;
  background: #fff;

  .informant-info {
    margin-bottom: 20px;
  }

  .hour-info {
    &__list {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
  }
}

.status-row {
  display: flex;
  align-items: center;

  .status-icon {
    width: 6px;
    height: 6px;
    margin-right: 6px;
    border-radius: 50%;
  }

  .status-icon-0 {
    background: rgb(0 0 0 / 25%);
  }

  .status-icon-1 {
    background: #52c41a;
  }

  .status-icon--1 {
    background: #ff7875;
  }

  .status-icon-3 {
    background: rgb(0 0 0 / 25%);
  }

  .status-icon-4 {
    background: #1677ff;
  }

  .status-icon-5 {
    background: #52c41a;
  }

  .status-icon-6 {
    background: #ff7875;
  }

  .status-icon-2 {
    background: rgb(251 189 67);
  }
}
</style>
