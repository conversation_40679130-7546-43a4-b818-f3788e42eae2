import request from '@/utils/request'

// 查询公告列表
export function listNotice(data) {
  return request({
    url: '/system/notice/list',
    method: 'post',
    data,
  })
}
// 查询公告已读未读数量
export function getSignRead(query) {
  return request({
    url: '/system/notice/signRead',
    method: 'get',
    params: query,
  })
}
// 置顶
export function changeTop(data) {
  return request({
    url: '/system/notice/top',
    method: 'post',
    data,
  })
}
// 新增公告
export function addNotice(data) {
  return request({
    url: '/system/notice',
    method: 'put',
    data,
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return request({
    url: `/system/notice/${noticeId}`,
    method: 'get',
  })
}
// export function getNotice(query) {
//   return request({
//     url: `/system/notice`,
//     method: 'get',
//     params: query,
//   })
// }

// 新增公告
// export function addNotice(data) {
//   return request({
//     url: '/system/notice',
//     method: 'post',
//     data,
//   })
// }

// 修改公告
// export function updateNotice(data) {
//   return request({
//     url: '/system/notice',
//     method: 'put',
//     data,
//   })
// }
export function updateNotice(data) {
  return request({
    url: '/system/notice',
    method: 'post',
    data,
  })
}

// 删除公告
// export function delNotice(noticeId) {
//   return request({
//     url: `/system/notice/${noticeId}`,
//     method: 'delete',
//   })
// }
export function delNotice(data) {
  return request({
    url: '/system/notice/deleted',
    method: 'post',
    data,
  })
}
// 通知公告标记已读未读

export function signRead(data) {
  return request({
    url: '/system/notice/signRead',
    method: 'post',
    data,
  })
}