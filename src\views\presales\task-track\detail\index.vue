<template>
  <Container>
    <div class="detail">
      <div class="title">
        <p class="title-text">
          {{ deptName }}
        </p>
      </div>
      <div class="content">
        <div class="content-search">
          <ElForm
            ref="formRef"
            :model="formData"
            label-width="74px"
            label-position="left"
            :inline="true">
            <ElFormItem
              label="市场经理"
              prop="marketManagerName"
              style="margin: 0;">
              <ElInput
                v-model="formData.marketManagerName"
                style="width: 286px;"
                placeholder="请输入市场经理" />
            </ElFormItem>
          </ElForm>
          <div class="content-search-btn">
            <ElButton
              type="primary"
              @click="handleSearch">
              查询
            </ElButton>
            <ElButton @click="handleReset">
              重置
            </ElButton>
          </div>
        </div>
        <div class="content-table">
          <ElTable
            height="100%"
            :data="tableData"
            :border="true">
            <template #empty>
              <ElEmpty
                :image="emptyImage"
                description="暂无数据" />
            </template>
            <ElTableColumn
              label="序号"
              type="index"
              width="80"
              fixed="left"
              :index="indexMethod" />
            <ElTableColumn
              label="市场经理"
              prop="marketManagerName"
              width="120" />
            <ElTableColumn
              label="本年度合同签订目标值"
              prop="currentTargetAmount"
              width="160">
              <template #default="{ row }">
                {{ row.currentTargetAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="本年度合同目标回款值"
              prop="currentTargetReceiveAmount"
              width="160">
              <template #default="{ row }">
                {{ row.currentTargetReceiveAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="本年度的收入目标值"
              prop="currentTargetIncome"
              width="160">
              <template #default="{ row }">
                {{ row.currentTargetIncome?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="本年度签订合同额"
              prop="currentSignAmount"
              width="160">
              <template #default="{ row }">
                {{ row.currentSignAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="本年度实际回款额"
              prop="currentActualReceiveAmount"
              width="160">
              <template #default="{ row }">
                {{ row.currentActualReceiveAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="本年度实际确收金额"
              prop="currentActualConfirmAmount"
              width="160">
              <template #default="{ row }">
                {{ row.currentActualConfirmAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="非本年度历史签订合同额"
              prop="historySignAmount"
              width="170">
              <template #default="{ row }">
                {{ row.historySignAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="非本年度历史开票金额"
              prop="historyInvoiceAmount"
              width="160">
              <template #default="{ row }">
                {{ row.historyInvoiceAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
            <ElTableColumn
              label="非本年度历史回款金额"
              prop="historyReceiveAmount"
              width="160">
              <template #default="{ row }">
                {{ row.historyReceiveAmount?.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) }}
              </template>
            </ElTableColumn>
          </ElTable>
        </div>
        <div class="content-pagination">
          <ElPagination
            v-model:current-page="paginationData.pageNum"
            v-model:page-size="paginationData.pageSize"
            :total="paginationData.total"
            layout="total, prev, pager, next, sizes, jumper"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange" />
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getTaskTrackDetail } from '@/api/presales/task-track.js'
import emptyImage from '@/assets/images/empty-image.png'
import Container from '@/components/Container/index.vue'

const route = useRoute()

const deptId = computed(() => {
  return route.query.id || ''
})
const deptName = computed(() => {
  return route.query.name || ''
})

const formRef = useTemplateRef('formRef')
const formData = reactive({
  marketManagerName: '',
})
const tableData = ref([])
const paginationData = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const loading = ref(false)
function getTableData() {
  loading.value = true
  getTaskTrackDetail({
    deptId: deptId.value,
    marketManagerName: formData.marketManagerName,
    pageNum: paginationData.pageNum,
    pageSize: paginationData.pageSize,
  }).then((res) => {
    tableData.value = res.data.list
    paginationData.pageNum = res.data.page
    paginationData.total = res.data.total
  }).finally(() => {
    loading.value = false
  })
}

function handleSearch() {
  paginationData.pageNum = 1
  getTableData()
}

function handleReset() {
  formRef.value.resetFields()
}

function indexMethod(index) {
  return (paginationData.pageNum - 1) * paginationData.pageSize + index + 1
}

function handleCurrentChange(pageNum) {
  paginationData.pageNum = pageNum
  getTableData()
}

function handleSizeChange(pageSize) {
  paginationData.pageSize = pageSize
  getTableData()
}

onMounted(() => {
  getTableData()
})
</script>

<style lang="scss" scoped>
.detail {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  border-radius: 10px;
  background-color: #fff;

  .title {
    display: flex;
    align-items: center;
    height: 58px;
    padding: 0 18px;
    border-bottom: 1px solid rgb(0 0 0 / 6%);

    &-text {
      color: rgb(0 0 0 / 88%);
      font-weight: 600;
      font-size: 20px;
    }
  }

  .content {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: auto;
    padding: 24px 18px;

    &-search {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      &-btn {
        margin-left: 16px;
      }
    }

    &-table {
      // flex: 1;
      overflow: hidden;
    }

    &-pagination {
      display: flex;
      justify-content: flex-end;
      padding: 16px 0;
    }
  }
}
</style>
