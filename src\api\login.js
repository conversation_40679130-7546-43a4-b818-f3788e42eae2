import request from '@/utils/request.js'

const prefix = '/sso'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid,
  }
  return request({
    url: `${prefix}/login`,
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: 'post',
    data,
  })
}

// 注册方法
export function register(data) {
  return request({
    url: `${prefix}/register`,
    headers: {
      isToken: false,
    },
    method: 'post',
    data,
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: `${prefix}/getInfo`,
    method: 'get',
  })
}

// 退出方法
export function logout() {
  return request({
    url: `${prefix}/logout`,
    method: 'post',
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: `${prefix}/captchaImage`,
    headers: {
      isToken: false,
    },
    method: 'get',
    timeout: 20000,
  })
}

// 三方登录
export function thirdLogin(data) {
  return request({
    url: `${prefix}/loginByThirdParty`,
    headers: {
      isToken: false,
      isRepeatSubmit: false,
    },
    method: 'post',
    data,
  })
}
