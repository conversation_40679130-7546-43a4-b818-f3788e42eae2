<template>
  <ElUpload
    ref="uploadRef"
    class="upload-demo"
    accept=".xlsx, .xls"
    :auto-upload="false"
    :limit="1"
    :on-change="handleFileChange"
    :on-exceed="handleExceed"
    :on-remove="handleFileRemove">
    <template #trigger>
      <ElButton type="default">
        <template #icon>
          <i class="iconfont icon-UploadOutlined" />
        </template>
        上传
      </ElButton>
    </template>
  </ElUpload>
</template>

<script setup>
import { ElMessage, genFileId } from 'element-plus'

const props = defineProps({
  type: String,
})
const emits = defineEmits(['handleChange', 'handleFileRemove', 'uploadFiles'])

const uploadRef = ref(null)

// 文件变化处理
function handleFileChange(file) {
  const isXls = file.raw.type === 'application/vnd.ms-excel'
  const isXlsx = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

  if (!isXls && !isXlsx) {
    ElMessage.warning('上传文件只能是 xls/xlsx 格式!')
    uploadRef.value.clearFiles()
    return false
  }
  emits('handleChange', props.type, file.raw)
  // TODO 解析文件数据
  emits('uploadFiles', props.type)
}
// 文件移除处理
function handleFileRemove() {
  uploadRef.value.clearFiles()
  emits('handleFileRemove', props.type)
}
// 文件替换处理
function handleExceed(files) {
  uploadRef.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  uploadRef.value.handleStart(file)
  emits('handleChange', props.type, file)
}
function handleReset() {
  uploadRef.value.clearFiles()
}
defineExpose({
  handleReset,
})
</script>

<style scoped lang="scss">
.upload-demo {
  width: 100%;
}
</style>
