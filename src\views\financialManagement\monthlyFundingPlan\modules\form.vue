<template>
  <DefaultContainer v-loading="loading">
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          {{ title }}
        </div>
      </div>
      <div
        v-if="!props.id"
        class="right">
        <ElButton @click="router.back()">
          取消
        </ElButton>
        <ElButton
          v-if="type === 'edit' || type === 'fill'"
          :loading="saveLoading"
          plain
          type="primary"
          @click="onSubmit(false)">
          保存
        </ElButton>
        <ElButton
          v-else-if="type === 'submit'"
          :loading="saveLoading"
          type="primary"
          @click="onSubmit(true)">
          提交
        </ElButton>
      </div>
    </div>
    <div class="content">
      <ElForm
        ref="formRef1"
        :inline="true"
        :model="form"
        :rules="rules1"
        label-position="left"
        :disabled="props.id"
        label-width="210px">
        <ElDivider />
        <div class="sub-title">
          基础信息
        </div>
        <ElFormItem
          class="form-item"
          label="填报月度"
          prop="fillMonth">
          <ElDatePicker
            v-model="form.fillMonth"
            placeholder="请选择填报月份"
            clearable
            value-format="YYYY-MM"
            disabled
            style="width: 100%" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="计划起始月度"
          prop="planStartMonth">
          <ElInput
            v-model="form.planStartMonth"
            placeholder="请选择计划起始月度"
            clearable
            disabled
            style="width: 100%" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="计划终止月度"
          prop="planEndMonth">
          <ElInput
            v-model="form.planEndMonth"
            placeholder="请选择计划终止月度"
            clearable
            disabled
            style="width: 100%" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="填报人"
          prop="applicationByName"
          v-if="type === 'submit'"
        >
          <ElInput
            v-model="form.applicationByName"
            placeholder="请选择填报人"
            clearable
            disabled
            style="width: 100%" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="填报部门"
          prop="fillDepartment"
          v-if="type === 'submit'">
          <ElInput
            v-model="form.fillDepartment"
            placeholder="请选择填报部门"
            clearable
            disabled
            style="width: 100%" />
        </ElFormItem>
        <ElDivider />
        <div class="sub-title">
          概览
        </div>
        <!-- 概览表格开始 -->
        <Overview :month="form.fillMonth" />
        <!-- 概览表格结束 -->

        <div class="sub-title">
          资金计划明细
        </div>
        <div class="table-header">
          <!-- 表头显示隐藏控制面板组件 -->

          <TableHeaderSetting
            v-model:checked-columns="checkedColumns"
            :columns="columns"
            :always-visible-columns="alwaysVisibleColumns" />
          <!-- 新增按钮 -->
          <ElButton
            type="primary"
            @click="modelIsShow.selectProjectModal = true">
            新增
          </ElButton>
        </div>
        <ElTable
          :data="form.planProjectList"
          :span-method="arraySpanMethod"
          border
          stripe
          style="width: 95%; margin-top: 10px">
          <!-- 只读/展示列加v-if -->
          <ElTableColumn
            v-if="isColumnVisible('warnMessage')"
            prop="warnMessage"
            label="预警消息"
            width="120"
            fixed="left">
            <template #default="{ row }">
              <span
                style="color: red">
                {{ row.warnMessage }}
              </span>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('deliveryEntity')"
            prop="deliveryEntity"
            label="交付主体"
            width="120"
            fixed="left" />
          <ElTableColumn
            v-if="isColumnVisible('marketEntity')"
            prop="marketEntity"
            label="市场主体"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('projectName')"
            prop="projectName"
            label="项目名称"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('projectCode')"
            prop="projectCode"
            label="项目编号"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('projectManager')"
            prop="projectManager"
            label="项目经理"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('marketingManager')"
            prop="marketingManager"
            label="市场经理"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('salesContractName')"
            prop="salesContractName"
            label="销售合同"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('customerName')"
            prop="customerName"
            label="客户"
            width="120" />
          <ElTableColumn
            prop="contractAmount"
            label="合同额"
            width="120" />
          <ElTableColumn
            prop="receivedCash"
            label="已回款现款"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('receivedUnexpiredTicket')"
            prop="receivedUnexpiredTicket"
            label="已收到未到期票证"
            width="140">
            <template #default="scope">
              <ElFormItem
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.receivedUnexpiredTicket`">
                <ElInputNumber
                  v-model="scope.row.receivedUnexpiredTicket"
                  placeholder="请输入已收到未到期票证"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('firstCashCollection')"
            prop="firstCashCollection"
            :label="`${monthTableData[0]}月回款现款`"
            width="120">
            <template #default="scope">
              <ElFormItem
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.firstCashCollection`">
                <ElInputNumber
                  v-model="scope.row.firstCashCollection"
                  :placeholder="`请输入${monthTableData[0]}月回款现款`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('firstCollectionTicket')"
            prop="firstCollectionTicket"
            :label="`${monthTableData[0]}月回款票证`"
            width="120">
            <template #default="scope">
              <ElFormItem
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.firstCollectionTicket`">
                <ElInputNumber
                  v-model="scope.row.firstCollectionTicket"
                  :placeholder="`请输入${monthTableData[0]}月回款票证`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('secondCashCollection')"
            prop="secondCashCollection"
            :label="`${monthTableData[1]}月回款现款`"
            width="120">
            <template #default="scope">
              <ElFormItem
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.secondCashCollection`">
                <ElInputNumber
                  v-model="scope.row.secondCashCollection"
                  :placeholder="`请输入${monthTableData[1]}月回款现款`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('secondCollectionTicket')"
            prop="secondCollectionTicket"
            :label="`${monthTableData[1]}月回款票证`"
            width="120">
            <template #default="scope">
              <ElFormItem
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.secondCollectionTicket`">
                <ElInputNumber
                  v-model="scope.row.secondCollectionTicket"
                  :placeholder="`请输入${monthTableData[1]}月回款票证`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('thirdCashCollection')"
            prop="thirdCashCollection"
            :label="`${monthTableData[2]}月回款现款`"
            width="120">
            <template #default="scope">
              <ElFormItem
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.thirdCashCollection`">
                <ElInputNumber
                  v-model="scope.row.thirdCashCollection"
                  :placeholder="`请输入${monthTableData[2]}月回款现款`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('thirdCollectionTicket')"
            prop="thirdCollectionTicket"
            :label="`${monthTableData[2]}月回款票证`"
            width="120">
            <template #default="scope">
              <ElFormItem
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.thirdCollectionTicket`">
                <ElInputNumber
                  v-model="scope.row.thirdCollectionTicket"
                  :placeholder="`请输入${monthTableData[2]}月回款票证`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <!-- 采购合同 start -->
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.purchaseContractName')"
            prop="purchaseContract.purchaseContractName"
            label="采购合同"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.customerName')"
            prop="purchaseContract.customerName"
            label="供应商"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.contractAmount')"
            prop="purchaseContract.contractAmount"
            label="合同额"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.paidInCash')"
            prop="purchaseContract.paidInCash"
            label="已付款现款"
            width="120" />
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.issuedNotExpiredTicket')"
            prop="purchaseContract.issuedNotExpiredTicket"
            label="已开具未到期票证"
            width="140">
            <template #default="scope">
              <ElFormItem
                v-if="scope.row.purchaseContract !== null"
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.purchaseContract.issuedNotExpiredTicket`">
                <ElInputNumber
                  v-model="scope.row.purchaseContract.issuedNotExpiredTicket"
                  placeholder="请输入已开具未到期票证"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.firstExpensesCash')"
            prop="purchaseContract.firstExpensesCash"
            :label="`${monthTableData[0]}月支出现款`"
            width="120">
            <template #default="scope">
              <ElFormItem
                v-if="scope.row.purchaseContract !== null"
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.purchaseContract.firstExpensesCash`">
                <ElInputNumber
                  v-model="scope.row.purchaseContract.firstExpensesCash"
                  :placeholder="`请输入${monthTableData[0]}月支出现款`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.firstExpensesTicket')"
            prop="purchaseContract.firstExpensesTicket"
            :label="`${monthTableData[0]}月支出票证`"
            width="120">
            <template #default="scope">
              <ElFormItem
                v-if="scope.row.purchaseContract !== null"
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.purchaseContract.firstExpensesTicket`">
                <ElInputNumber
                  v-model="scope.row.purchaseContract.firstExpensesTicket"
                  :placeholder="`请输入${monthTableData[0]}月支出票证`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.secondExpensesCash')"
            prop="purchaseContract.secondExpensesCash"
            :label="`${monthTableData[1]}月支出现款`"
            width="120">
            <template #default="scope">
              <ElFormItem
                v-if="scope.row.purchaseContract !== null"
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.purchaseContract.secondExpensesCash`">
                <ElInputNumber
                  v-model="scope.row.purchaseContract.secondExpensesCash"
                  :placeholder="`请输入${monthTableData[1]}月支出现款`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.secondExpensesTicket')"
            prop="purchaseContract.secondExpensesTicket"
            :label="`${monthTableData[1]}月支出票证`"
            width="120">
            <template #default="scope">
              <ElFormItem
                v-if="scope.row.purchaseContract !== null"
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.purchaseContract.secondExpensesTicket`">
                <ElInputNumber
                  v-model="scope.row.purchaseContract.secondExpensesTicket"
                  :placeholder="`请输入${monthTableData[1]}月支出票证`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.thirdExpensesCash')"
            prop="purchaseContract.thirdExpensesCash"
            :label="`${monthTableData[2]}月支出现款`"
            width="120">
            <template #default="scope">
              <ElFormItem
                v-if="scope.row.purchaseContract !== null"
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.purchaseContract.thirdExpensesCash`">
                <ElInputNumber
                  v-model="scope.row.purchaseContract.thirdExpensesCash"
                  :placeholder="`请输入${monthTableData[2]}月支出现款`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-if="isColumnVisible('purchaseContract.thirdExpensesTicket')"
            prop="purchaseContract.thirdExpensesTicket"
            :label="`${monthTableData[2]}月支出票证`"
            width="120">
            <template #default="scope">
              <ElFormItem
                v-if="scope.row.purchaseContract !== null"
                label=""
                label-width="0px"
                style="width: 100%"
                :prop="`planProjectList.${scope.$index}.purchaseContract.thirdExpensesTicket`">
                <ElInputNumber
                  v-model="scope.row.purchaseContract.thirdExpensesTicket"
                  :placeholder="`请输入${monthTableData[2]}月支出票证`"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%" />
              </ElFormItem>
            </template>
          </ElTableColumn>
        </ElTable>
        <!-- 采购合同 end -->
      </ElForm>
      <PlanDetails :searchData="{
        planProjectList: form.value?.planProjectList || [],
        monthTableData,
      }"  />
    </div>
    <SelectProjectModal
      v-model="modelIsShow.selectProjectModal"
      @select-item="onChangeProject" />
  </DefaultContainer>
</template>

<script setup>
import { getPsmContractList } from '@/api/financialManagement/index.js'
import { getFundPlanDetail, getFundPlanProjectList, historyPlanProjectFillData, historyPlanPurchaseContractData, savePlan, submitPlan } from '@/api/financialManagement/monthlyFundingPlan'
import { getCurrentProcessId } from '@/api/wflow-pro'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { deepClone } from '@/utils'
import { ElMessage } from 'element-plus'
import { computed, onMounted, readonly, ref, unref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import SelectProjectModal from '../../modules/SelectProjectModal.vue'
import Overview from './Overview.vue'
import PlanDetails from './PlanDetails.vue'
import TableHeaderSetting from './TableHeaderSetting.vue'

const props = defineProps({
  id: {
    type: String,
  },
  instanceId: {
    type: String,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  forms: {
    type: Object,
    default: () => ({}),
  },
})

const route = useRoute()
let type = route.query.type
const title = computed(() => {
  if (type === 'edit') {
    return '资金计划编辑'
  } else if (type === 'fill') {
    return '资金计划填报'
  } else if (type === 'submit') {
    return '资金计划提交'
  } else {
    return '资金计划审批'
  }
})

const form = ref({
  fillMonth: '',
  planStartMonth: '',
  planEndMonth: '',
  applicationByName: '',
  fillDepartment: '',
  planProjectList: [],
})
const formRef1 = ref(null)
const loading = ref(false)
const router = useRouter()
const saveLoading = ref(false)
const rules1 = ref({})
const modelIsShow = ref({
  selectProjectModal: false,
})

const id = props.id || route.query.id
const monthTableData = computed(() => {
  const start = form.value.fillMonth // 例如 '2025-11'
  if (!start)
    return []
  let [year, month] = start.split('-').map(Number)
  const arr = []
  for (let i = 1; i <= 3; i++) {
    month++
    if (month > 12) {
      year++
      month = 1
    }
    arr.push(`${year}-${String(month).padStart(2, '0')}`)
  }
  return arr
})

// 行合并的映射表，记录每个项目的合并信息
const spanMap = ref({})

// 表格行合并方法
function arraySpanMethod({ row, column, rowIndex }) {
  // 根据列的属性判断是否需要合并
  // 采购合同相关的列（以 purchaseContract. 开头的属性）不需要合并
  // 其他项目相关的列需要合并
  const shouldMerge = !column.property || !column.property.startsWith('purchaseContract.')

  if (shouldMerge) {
    const projectKey = row.projectCode || row.projectName || rowIndex // 使用项目编号或项目名称作为合并标识
    if (spanMap.value[projectKey]) {
      const spanInfo = spanMap.value[projectKey]
      if (rowIndex === spanInfo.startIndex) {
        return {
          rowspan: spanInfo.count,
          colspan: 1,
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        }
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

// 计算行合并信息
function calculateSpanMap(data) {
  const map = {}
  const countMap = {}

  data.forEach((item, index) => {
    const key = item.projectCode || item.projectName || index
    if (!countMap[key]) {
      countMap[key] = { count: 0, startIndex: index }
    }
    countMap[key].count++
  })

  Object.keys(countMap).forEach((key) => {
    map[key] = countMap[key]
  })

  spanMap.value = map
}

onMounted(() => {
  if ((route.query.type === 'edit' || route.query.type === 'fill') && id) {
    getFundPlanDetailData()
    type = 'edit'
  } else {
    type = 'add'
  }
})

async function getFundPlanDetailData() {
  loading.value = true
  const res = await getFundPlanDetail({ id })
  const res2 = await getFundPlanProjectList({ planId: id })
  if (res.code === 200) {
    form.value = res.data
  }
  if (res2.code === 200) {
    const list = res2.data
    form.value.planProjectList = getProjectList(list, false)
    // 计算行合并信息
    calculateSpanMap(form.value.planProjectList)
  }
  loading.value = false
}

function getProjectList(list, isSubmit) {
  const newList = []
  if (!isSubmit) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.purchaseContractList.length > 0) {
        for (let j = 0; j < item.purchaseContractList.length; j++) {
          const obj = {
            ...item,
          }
          delete obj.purchaseContractList
          const purchaseContract = item.purchaseContractList[j]
          obj.purchaseContract = purchaseContract
          newList.push(obj)
        }
      } else {
        const obj = {
          ...item,
        }
        delete obj.purchaseContractList
        obj.purchaseContract = null
        newList.push(obj)
      }
    }
    return newList
  } else {
    // 按照项目 projectCode 合并传入list 并将采购合同合并进 purchaseContractList
    const projectMap = new Map()
    for (const item of list) {
      if (!projectMap.has(item.projectCode)) {
        const { purchaseContract, ...rest } = item
        projectMap.set(item.projectCode, {
          ...rest,
          purchaseContractList: [],
        })
      }
      if (item.purchaseContract) {
        projectMap.get(item.projectCode).purchaseContractList.push(item.purchaseContract)
      }
    }
    return Array.from(projectMap.values())
  }
}

async function onChangeProject(item) {
  console.log(item, '--- item')
  for (let i = 0; i < form.value.planProjectList.length; i++) {
    if (form.value.planProjectList[i].projectCode === item.projectCode) {
      ElMessage.warning('项目已存在')
      return
    }
  }
  const obj = {
    projectCode: item.projectCode,
    projectName: item.projectName || item.bolProjectName,
    projectManager: item.projectManager,
    projectManagerId: item.projectManagerId,
    marketingManager: item.marketingManager,
    marketingManagerId: item.marketingManagerId,
    customerName: item.customer,
    customerId: item.customerId,
    deliveryEntity: item.deliveryEntity,
    contractAmount: item.contractAmountWithTax,
    purchaseContractList: [],
    marketEntity: item.marketEntity,
    salesContractCode: item.contractCode,
    salesContractName: item.contractName,
    warnMessage: '',
    fillMonth: form.value.fillMonth,
  }
  const historyPlanProjectFillData = await getHistoryPlanProjectFillData(item.projectCode)
  console.log(historyPlanProjectFillData, '--- historyPlanProjectFillData')
  obj.receivedCash = historyPlanProjectFillData.receivedCash
  obj.firstCashCollection = historyPlanProjectFillData.firstCashCollection
  obj.firstCollectionTicket = historyPlanProjectFillData.firstCollectionTicket
  obj.secondCashCollection = historyPlanProjectFillData.secondCashCollection
  obj.secondCollectionTicket = historyPlanProjectFillData.secondCollectionTicket
  const purchaseContractListRes = await getPsmContractList({
    projectCode: item.projectCode.split('-D')[0],
    purchaseContractType: 'purchase',
    pageNum: 1,
    pageSize: 1000,
  })
  console.log(purchaseContractListRes, '--- purchaseContractListRes')
  const purchaseContractList = purchaseContractListRes.records
  for (let i = 0; i < purchaseContractList.length; i++) {
    const purchaseContract = purchaseContractList[i]
    const historyPlanPurchaseContractData = await getHistoryPlanPurchaseContractData(purchaseContract.code)
    console.log(historyPlanPurchaseContractData, '--- historyPlanPurchaseContractData')
    const newPurchaseContract = {
      contractAmount: purchaseContract.amount,
      customerName: purchaseContract.merchantName,
      fillMonth: form.value.fillMonth,
      purchaseContractCode: purchaseContract.code,
      purchaseContractName: purchaseContract.name,
      paidInCash: historyPlanPurchaseContractData.paidInCash,
      firstCashCollection: historyPlanPurchaseContractData.firstCashCollection,
      firstCollectionTicket: historyPlanPurchaseContractData.firstCollectionTicket,
      secondCashCollection: historyPlanPurchaseContractData.secondCashCollection,
      secondCollectionTicket: historyPlanPurchaseContractData.secondCollectionTicket,
    }
    obj.purchaseContractList.push(newPurchaseContract)
  }
  const newPurchaseContractList = getProjectList(deepClone(form.value.planProjectList), true)
  newPurchaseContractList.push(obj)
  form.value.planProjectList = getProjectList(newPurchaseContractList, false)
}

async function getHistoryPlanProjectFillData(projectCode) {
  const res = await historyPlanProjectFillData({ planId: id, projectCode })
  if (res.code === 200) {
    return res.data
  }
  return []
}

async function getHistoryPlanPurchaseContractData(purchaseContractCode) {
  const res = await historyPlanPurchaseContractData({ planId: id, purchaseContractCode })
  if (res.code === 200) {
    return res.data
  }
  return []
}

async function onSubmit(isSubmit) {
  // 校验表单
  let valid = true
  // if (isSubmit) {
  const valid1 = await formRef1.value.validate()
  valid = valid1
  // }
  // if (!isSubmit) {
  //   valid = true
  // }
  saveLoading.value = true
  if (valid) {
    console.log('---- 校验通过')
    const data = deepClone(form.value)
    data.projectList = getProjectList(data.planProjectList, true)
    console.log(data.projectList, '--- data.projectList')
    if (isSubmit) {
      //  提交
      data.processDefId = await getCurrentProcessId('monthly-funding-plan')
      console.log(data.processDefId, '--- data.processDefId')
      const res = await submitPlan(data)
      saveLoading.value = false
      if (res.code === 200) {
        ElMessage.success('提交成功')
        router.back()
      } else {
        ElMessage.error('提交失败')
      }
    } else {
      // 暂存
      const res = await savePlan(data)
      saveLoading.value = false
      if (res.code === 200) {
        ElMessage.success('暂存成功')
        router.back()
      } else {
        ElMessage.error('暂存失败')
      }
    }
  }
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(form))
  },
  // 审批流程
  saveFormData: async () => {
    // try {
    //   if (props.forms[0] && props.forms[0]?.perm !== 'E') {
    //     return Promise.resolve()
    //   }

    //   // 校验表单
    //   if (form.value.confirmIncomeInvoiceApplyList.length === 0) {
    //     ElMessage.warning('请添加开票内容信息')
    //     return
    //   }
    //   const valid1 = await formRef1.value.validate()
    //   if (valid1) {
    //     const data = deepClone(form.value)
    //     data.processDefId = props.instanceId

    //     const res = await submitWithApprove(data)
    //     if (res.code === 200) {
    //       ElMessage.success('提交成功')
    //       return Promise.resolve()
    //     } else {
    //       ElMessage.error('提交失败')
    //       return Promise.reject(new Error('提交失败'))
    //     }
    //   }
    // } catch (error) {
    //   return Promise.reject(new Error(error))
    // }
  },
})

// 只读/展示列配置
const columns = [
  { key: 'warnMessage', label: '预警消息' },
  { key: 'deliveryEntity', label: '交付主体' },
  { key: 'marketEntity', label: '市场主体' },
  { key: 'projectName', label: '项目名称' },
  { key: 'projectCode', label: '项目编号' },
  { key: 'projectManager', label: '项目经理' },
  { key: 'marketingManager', label: '市场经理' },
  { key: 'salesContractName', label: '销售合同' },
  { key: 'customerName', label: '客户' },
  { key: 'contractAmount', label: '合同额' },
  { key: 'receivedCash', label: '已回款现款' },
  // ...如有其他只读列可补充
  { key: 'purchaseContract.purchaseContractName', label: '采购合同' },
  { key: 'purchaseContract.customerName', label: '供应商' },
  { key: 'purchaseContract.contractAmount', label: '采购合同合同额' },
  { key: 'purchaseContract.paidInCash', label: '已付款现款' },
]
// 只维护checkedColumns
const checkedColumns = ref(columns.map(col => col.key))
// 不可隐藏的输入列
const alwaysVisibleColumns = [
  'receivedUnexpiredTicket',
  'firstCashCollection',
  'firstCollectionTicket',
  'secondCashCollection',
  'secondCollectionTicket',
  'thirdCashCollection',
  'thirdCollectionTicket',
  'purchaseContract.issuedNotExpiredTicket',
  'purchaseContract.firstExpensesCash',
  'purchaseContract.firstExpensesTicket',
  'purchaseContract.secondExpensesCash',
  'purchaseContract.secondExpensesTicket',
  'purchaseContract.thirdExpensesCash',
  'purchaseContract.thirdExpensesTicket',
]
// 判断列是否显示
function isColumnVisible(key) {
  if (alwaysVisibleColumns.includes(key))
    return true
  return checkedColumns.value.includes(key)
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important; /* 设置最小高度 */

  .sub-title {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;

    .sub-title-span {
      display: inline-block;
      margin-left: 10px;
      font-weight: 400;
      font-size: 14px;
    }
  }

  .plan-item {
    width: 100%;

    .form-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 10px;
    margin-bottom: 40px;
    border: 1px dashed #1677ff;
    border-radius: 6px;
    color: #1677ff;
    font-size: 14px;
    cursor: pointer;
  }

  .form-item {
    width: 360px;
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 12px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}

.file-text-link {
  margin-bottom: 16px;
  color: #409eff;
  cursor: pointer;
}
</style>

<style lang="scss">
.custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
