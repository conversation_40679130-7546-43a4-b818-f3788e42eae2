<template>
  <ElDivider>申请信息</ElDivider>
  <ElRow :gutter="20">
    <ElCol :span="6">
      <ElFormItem
        prop="applicationInfo.applicant"
        label="申请人">
        <ElInput
          disabled
          :model-value="formData.applicationInfo.applicant" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        prop="applicationInfo.applicantDept"
        label="申请部门">
        <ElInput
          disabled
          :model-value="formData.applicationInfo.applicantDept" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        prop="applicationInfo.applyDate"
        label="申请时间">
        <ElDatePicker
          disabled
          style="width: 100%;"
          :model-value="formData.applicationInfo.applyDate" />
      </ElFormItem>
    </ElCol>
    <ElCol
      :span="6">
      <ElFormItem
        prop="applicationInfo.knotDate"
        label="结项日期">
        <ElDatePicker
          disabled
          style="width: 100%;"
          placeholder="审批通过后显示日期"
          :model-value="formData.applicationInfo.knotDate" />
      </ElFormItem>
    </ElCol>
  </ElRow>
</template>

<script setup>
import { ElDatePicker } from 'element-plus'

const formData = defineModel('formData', {
  type: Object,
  default: () => ({}),
})
</script>

<style lang="scss" scoped>

</style>
