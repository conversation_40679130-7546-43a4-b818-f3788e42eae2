<template>
  <Container>
    <template #headerRight>
      <div class="flex items-center text-[14px]">
        <p class="text-[#000]/[.45]">
          更新日期：{{ updateDate }}
        </p>
        <ElDivider direction="vertical" />
        <p class="text-[#000]/[.45]">
          单位：亿元
        </p>
        <ElDivider direction="vertical" />
        <div class="flex items-center">
          <p class="text-[#000]/[.88]">
            年度：
          </p>
          <ElSelect
            v-model="yearNow"
            size="small"
            style="width: 120px;">
            <ElOption
              v-for="item in yearOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </ElSelect>
        </div>
      </div>
    </template>
    <ElRow>
      <ChartPanel
        title="经营现状">
        <BusinessStatus />
      </ChartPanel>
    </ElRow>
    <ElRow
      :gutter="12">
      <ElCol
        style="margin-top: 12px;"
        :span="24"
        :md="12">
        <ChartPanel
          title="目标管理"
          :show-more="true"
          @click="() => {
            goalManagementRef?.showDialog()
          }">
          <GoalManagement ref="goalManagementRef" />
        </ChartPanel>
      </ElCol>
      <ElCol
        style="margin-top: 12px;"
        :span="24"
        :md="12">
        <ChartPanel title="重点商机">
          <KeyBusiness />
        </ChartPanel>
      </ElCol>
    </ElRow>
    <ElRow
      :gutter="12">
      <ElCol
        style="margin-top: 12px;"
        :span="24"
        :md="12">
        <ChartPanel title="重点任务">
          <KeyTask />
        </ChartPanel>
      </ElCol>
      <ElCol
        style="margin-top: 12px;"
        :span="24"
        :md="12">
        <ChartPanel title="重点交付项目">
          <KeyProject />
        </ChartPanel>
      </ElCol>
    </ElRow>
    <ElRow style="margin-top: 12px;">
      <ChartPanel title="人效管理">
        <PerformanceManagement />
      </ChartPanel>
    </ElRow>
  </Container>
</template>

<script setup>
import Container from '@/components/Container/index.vue'
import moment from 'moment'
import BusinessStatus from './business-status.vue'
import ChartPanel from './chart-panel.vue'
import GoalManagement from './goal-management.vue'
import KeyBusiness from './key-business.vue'
import KeyProject from './key-project.vue'
import KeyTask from './key-task.vue'
import PerformanceManagement from './performance-management.vue'

// 更新日期
const updateDate = ref(moment().format('YYYY-MM-DD'))
const yearNow = ref(new Date().getFullYear())
const yearOptions = computed(() => {
  // 最近五年
  return Array.from({ length: 5 }, (_, i) => {
    return {
      label: yearNow.value - i,
      value: yearNow.value - i,
    }
  })
})

const goalManagementRef = useTemplateRef('goalManagementRef')
</script>

<style lang="scss" scoped>

</style>
