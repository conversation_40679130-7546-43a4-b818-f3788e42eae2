import request from '@/utils/request'

export function list(params) {
  return request({
    url: '/outsource/demand/list',
    method: 'get',
    params,
  })
}
export function getDetail(params) {
  return request({
    url: `/outsource/demand/detail`,
    method: 'get',
    params,
  })
}
export function deleteItem(params) {
  return request({
    url: `/outsource/demand/delete`,
    method: 'get',
    params,
  })
}

export function createOrUpdate(data) {
  return request({
    url: '/outsource/demand/save',
    method: 'post',
    data,
  })
}

export function updateStatus(params) {
  return request({
    url: '/outsource/demand/updateStatus',
    method: 'get',
    params,
  })
}
export function withdrawn(params) {
  return request({
    url: `/outsource/demand/withdrawn`,
    method: 'get',
    params,
  })
}

export function getProjectBudget(params) {
  return request({
    url: '/outsource/demand/getProjectBudget',
    method: 'get',
    params,
  })
}

export function getProject(params) {
  return request({
    url: 'project/projectLibrary/pageList',
    method: 'POST',
    data: params,
  })
}
