<template>
  <ElDivider>项目基本信息</ElDivider>
  <ElRow :gutter="20">
    <ElCol :span="isEdit ? 4 : 6">
      <ElFormItem
        label="项目名称"
        prop="projectBasicInfo.projectName">
        <ElInput
          v-model="formData.projectBasicInfo.projectName"
          :disabled="formType === 'view'"
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol>
    <ElCol
      v-if="isEdit"
      :span="2">
      <ElFormItem label="请选择项目">
        <ElButton
          :disabled="formType === 'view'"
          :icon="Link"
          type="primary"
          @click="project_dialog_options.onOpen">
          关联项目
        </ElButton>
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="项目编号"
        prop="projectBasicInfo.projectCode">
        <ElInput
          v-model="formData.projectBasicInfo.projectCode"
          disabled
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="项目经理"
        prop="projectBasicInfo.projectManager">
        <ElInput
          v-model="formData.projectBasicInfo.projectManager"
          disabled
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="市场经理"
        prop="projectBasicInfo.marketingManager">
        <ElInput
          v-model="formData.projectBasicInfo.marketingManager"
          disabled
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="项目分管领导"
        prop="projectBasicInfo.projectLeader">
        <ElInput
          v-model="formData.projectBasicInfo.projectLeader"
          disabled
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="项目归属部门"
        prop="projectBasicInfo.deliveryEntity">
        <ElInput
          v-model="formData.projectBasicInfo.deliveryEntity"
          disabled
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol>
    <!-- <ElCol :span="6">
      <ElFormItem
        label="项目状态"
        prop="projectBasicInfo.projectStatus">
        <ElInput
          v-model="formData.projectBasicInfo.projectStatus"
          disabled
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol> -->
    <ElCol :span="6">
      <ElFormItem
        label="项目状态"
        prop="projectBasicInfo.projectStatus">
        <ElSelect
          v-model="formData.projectBasicInfo.projectStatus"
          disabled
          placeholder="请选择项目状态">
          <ElOption
            v-for="item in project_status"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="验收日期"
        prop="projectBasicInfo.acceptanceDate">
        <ElInput
          v-model="formData.projectBasicInfo.acceptanceDate"
          disabled
          placeholder="请选择项目" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="6">
      <ElFormItem
        label="结项类型"
        prop="projectBasicInfo.knotType">
        <ElSelect
          v-model="formData.projectBasicInfo.knotType"
          :disabled="formType === 'view'">
          <ElOption
            v-for="item in project_finish_type"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </ElSelect>
      </ElFormItem>
    </ElCol>
    <ElCol
      v-if="formData.projectBasicInfo.knotType === 2"
      :span="24">
      <ElFormItem
        label="异常结项原因"
        prop="projectBasicInfo.abnormalClosing">
        <ElInput
          v-model="formData.projectBasicInfo.abnormalClosing"
          :disabled="formType === 'view'"
          type="textarea"
          :rows="4"
          placeholder="请输入异常结项原因" />
      </ElFormItem>
    </ElCol>
  </ElRow>
  <ProjectDialog
    v-model="project_dialog_options.visible"
    type="预立项"
    @checked="project_dialog_options.checked" />
</template>

<script setup>
import ProjectDialog from '@/components/dialog/project-dialog.vue'
import { Link } from '@element-plus/icons-vue'
import { klona } from 'klona'

const { formType } = defineProps({
  formType: {
    type: String,
    default: '',
  },
})
// const { project_finish_type } = proxy.useDict('project_finish_type')

const project_finish_type = [
  {
    value: 1,
    label: '正常结项',
  },
  {
    value: 2,
    label: '异常结项',
  },
]

const project_status = [
  {
    value: 1,
    label: '进行中',
  },
  {
    value: 2,
    label: '阶段性验收',
  },
  {
    value: 3,
    label: '竣工验收',
  },
]

const formData = defineModel('formData', {
  type: Object,
  default: () => ({}),
})
const isEdit = inject('isEdit')
const project_dialog_options = reactive({
  visible: false,
  project_info: {
    projectManagerName: '',
    projectManagerId: '',
  },
  onOpen: () => {
    project_dialog_options.visible = true
  },
  checked: async (data) => {
    /**
     * 需要反填的字段
     * 项目名称 projectName
     * 项目编号 projectCode
     * 项目经理 projectManagerName-projectManagerId
     * 市场经理 marketManagerName-marketManagerId
     * 项目分管领导 leadershipName-leadershipId
     * 项目归属部门 deliverySubject
     * 项目状态
     * 验收日期
     */
    project_dialog_options.pre_project_info = klona(data)
    formData.value.projectBasicInfo.projectName = data.projectName
    formData.value.projectBasicInfo.projectCode = data.projectCode
    formData.value.projectBasicInfo.projectManager = data.outputProjectManagerName
    formData.value.projectBasicInfo.projectManagerId = data.outputProjectManagerId
    formData.value.projectBasicInfo.marketingManager = data.marketManagerName
    formData.value.projectBasicInfo.marketingManagerId = data.marketManagerId
    formData.value.projectBasicInfo.projectLeader = data.leadershipName
    formData.value.projectBasicInfo.projectLeaderId = data.leadershipId
    formData.value.projectBasicInfo.deliveryEntity = data.deliverySubject
    formData.value.projectBasicInfo.deliveryEntityId = data.deliverySubjectId
    // TODO:字段暂定
    formData.value.projectBasicInfo.projectStatus = data.status
    formData.value.projectBasicInfo.acceptanceDate = data.acceptanceDate
    formData.value.projectBasicInfo.knotType = data.knotType
    formData.value.projectBasicInfo.abnormalClosing = data.abnormalClosing
  },
})
</script>

<style lang="scss" scoped>

</style>
