<template>
  <Container show-back>
    <div class="wrapper">
      <ElForm label-position="top">
        <BaseInfo v-model="formData" />
        <AcceptanceInstructions v-model="formData" />
      </ElForm>
    </div>
  </Container>
</template>

<script setup>
import { getAcceptanceApplicationDetailByBid } from '@/api/project-development/acceptance-application.js'
import Container from '@/components/Container/index.vue'
import { useRoute } from 'vue-router'
import AcceptanceInstructions from './acceptanceInstructions.vue'
import BaseInfo from './baseInfo.vue'

const props = defineProps({
  id: {
    default: '',
  },
})

const route = useRoute()

const formData = ref({})

onMounted(async () => {
  if (route.query.id) {
    const res = await getAcceptanceApplicationDetailByBid(route.query.id)
    formData.value = res
  } else if (props.id) {
    const res = await getAcceptanceApplicationDetailByBid(props.id)
    formData.value = res
  }
})

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(formData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve(readonly(unref(formData)))
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.wrapper {
  flex: auto;
  overflow-y: auto;
  padding: 10px 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
