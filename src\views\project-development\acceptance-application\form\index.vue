<template>
  <Container show-back>
    <div class="wrapper">
      <ElForm
        ref="formRef"
        label-position="top"
        :model="formData.baseInfo"
        :rules="rules">
        <BaseInfo v-model="formData" />
        <AcceptanceInstructions v-model="formData" />
      </ElForm>
    </div>
    <template #headerRight>
      <div style="display: flex;flex: auto; justify-content: flex-end;">
        <ElButton
          type="primary"
          plain
          @click="temporarilyStoreForm">
          暂存
        </ElButton>
        <ElButton
          type="primary"
          @click="submitForm">
          提交
        </ElButton>
      </div>
    </template>
  </Container>
</template>

<script setup>
import { getAcceptanceApplicationDetailByBid, startAcceptanceApplication } from '@/api/project-development/acceptance-application.js'
import * as wFlowApis from '@/api/wflow-pro.js'
import Container from '@/components/Container/index.vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import AcceptanceInstructions from './acceptanceInstructions.vue'
import BaseInfo from './baseInfo.vue'

const props = defineProps({
  id: {
    default: '',
  },
})

const formRef = useTemplateRef('formRef')

const formData = ref({
  baseInfo: {
    contractOtherSubjects: [],
  },
})

const router = useRouter()
const route = useRoute()

const rules = reactive({
  prdProjectName: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  checkType: [{ required: true, message: '请选择验收类型', trigger: 'change' }],
  isUseSeal: [{ required: true, message: '请选择', trigger: 'change' }],
  checkAcceptFactSheet: [{ required: true, message: '请输入验收情况说明', trigger: 'change' }],
  sealBids: [{ required: true, message: '请选择印章', trigger: 'change' }],
  fileUrlList: [{ required: true, message: '请上传附件', trigger: 'change' }],
})

async function temporarilyStoreForm() {
  // 附件
  formData.value.baseInfo.attachments = formData.value.baseInfo.fileUrlList ? formData.value.baseInfo.fileUrlList.map(item => item.url || item.response.data) : []
  formData.value.options = 0 // 0表示暂存
  const res = await startAcceptanceApplication(formData.value)
  if (res.code === 200) {
    ElMessage.success('暂存成功')
    router.push('/project-development/acceptance-application/list')
  }
}

function submitForm() {
  formRef.value.validate().then(async () => {
    // 附件
    formData.value.baseInfo.attachments = formData.value.baseInfo.fileUrlList ? formData.value.baseInfo.fileUrlList.map(item => item.url || item.response.data) : []
    formData.value.options = 1 // 1表示提交
    const processDefId = await wFlowApis.getCurrentProcessId('acceptance-application')
    formData.value.processDefId = processDefId
    const res = await startAcceptanceApplication(formData.value)
    if (res.code === 200) {
      ElMessage.success('提交成功')
      router.push('/project-development/acceptance-application/list')
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

onMounted(async () => {
  if (route.query.type === 'edit' && route.query.id) {
    const res = await getAcceptanceApplicationDetailByBid(route.query.id)
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.fileUrlList = res.attachments.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  } else if (props.id) {
    const res = await getAcceptanceApplicationDetailByBid(props.id)
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.fileUrlList = res.attachments.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  }
})

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(formData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve(readonly(unref(formData)))
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.wrapper {
  flex: auto;
  overflow-y: auto;
  padding: 10px 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
