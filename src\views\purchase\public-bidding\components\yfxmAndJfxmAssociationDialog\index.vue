<template>
  <ElDialog
    v-model="model"
    title="关联项目"
    @open="onOpen">
    <ElTabs v-model="activeName">
      <ElTabPane
        label="关联研发项目"
        name="first">
        <YfxmTable
          ref="yfxmTable"
          @associate="onAssociate" />
      </ElTabPane>
      <ElTabPane
        label="关联交付项目"
        name="second">
        <JfxmTable
          ref="jfxmTable"
          @associate="onAssociate" />
      </ElTabPane>
    </ElTabs>
  </ElDialog>
</template>

<script setup>
import JfxmTable from './jfxmTable.vue'
import YfxmTable from './yfxmTable.vue'

const emit = defineEmits(['associate'])

const model = defineModel()

const activeName = ref('first')

const yfxmTable = useTemplateRef('yfxmTable')

const jfxmTable = useTemplateRef('jfxmTable')

function onAssociate(data, type) {
  emit('associate', data, type)
  model.value = false
}

function onOpen() {
  activeName.value = 'first'
  yfxmTable.value?.refresh()
  jfxmTable?.value?.refresh()
}
</script>
