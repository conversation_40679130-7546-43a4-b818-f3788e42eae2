import request from '@/utils/request'

// 暂存、更新续订资源单信息
export function createOrUpdateRenewalResourceWorkOrderProcess(data) {
  return request({
    url: '/cloudorder/ct-cloud-continue-order/createOrUpdate',
    method: 'post',
    data,
  })
}

// 删除续订资源单
export function deleteRenewalResourceWorkOrderProcess(params) {
  return request({
    url: '/cloudorder/ct-cloud-continue-order/del',
    method: 'get',
    params,
  })
}

// 获取详情
export function getRenewalResourceWorkOrderProcessDetail(params) {
  return request({
    url: '/cloudorder/ct-cloud-continue-order/detail',
    method: 'get',
    params,
  })
}

// 下载续订资源单
export function downloadRenewalResourceWorkOrderProcess(params) {
  return request({
    url: '/cloudorder/ct-cloud-continue-order/download',
    method: 'get',
    params,
  })
}

// 续订资源单分页列表
export function getRenewalResourceWorkOrderProcessPageList(data) {
  return request({
    url: '/cloudorder/ct-cloud-continue-order/pageList',
    method: 'post',
    data,
  })
}

// 提交续订资源单
export function submitRenewalResourceWorkOrderProcess(data) {
  return request({
    url: '/cloudorder/ct-cloud-continue-order/submit',
    method: 'post',
    data,
  })
}

// 审核中提交续订工单详情
export function submitRenewalResourceWorkOrderProcessWithApprove(data) {
  return request({
    url: '/cloudorder/ct-cloud-continue-order/submit-with-approve',
    method: 'post',
    data,
  })
}
