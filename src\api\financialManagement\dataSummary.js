import { get, post } from '@/utils/alova'

/**
 * 详情-确收记录
 */
export function getBaseConfirmIncome(params) {
  return get('/finance/financial-data-summary/detail-confirm-income', params)
}

/**
 * 详情-进项票
 */
export function getBaseInputLedger(params) {
  return get('/finance/financial-data-summary/detail-input-ledger', params)
}

/**
 * 详情-销项票
 */
export function getBaseOutputLedger(params) {
  return get('/finance/financial-data-summary/detail-output-ledger', params)
}

/**
 * 详情-付款记录
 */
export function getBasePayMoneyRecord(params) {
  return get('/finance/financial-data-summary/detail-pay-money-record', params)
}

/**
 * 详情-项目基本信息
 */
export function getBaseProjectDetail(params) {
  return get('/finance/financial-data-summary/base-project-detail', params)
}

/**
 * 详情-回款记录
 */
export function getBaseReceiveMoneyRecord(params) {
  return get('/finance/financial-data-summary/detail-receive-money-record', params)
}

/**
 * 根据项目分页列表，财务数据汇总
 */
export function getPageList(params, config) {
  return post('/finance/financial-data-summary/pageList', params, config)
}

/**
 * 详情-销售合同
 */
export function getDetailSaleContractList(params) {
  return get('/finance/financial-data-summary/detail-sale-contract-list', params)
}

/**
 * 详情-采购合同
 */
export function getDetailPurchaseContractList(params) {
  return get('/finance/financial-data-summary/detail-purchase-contract-list', params)
}
