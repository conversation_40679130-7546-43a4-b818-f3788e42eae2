<template>
  <Container>
    <div class="task-track">
      <div class="title">
        <p class="title-text">
          售前任务跟踪
        </p>
        <template v-if="isAdmin">
          <ElTag
            class="cursor-pointer"
            type="primary"
            :round="true"
            @click="() => {
              orgPickerRef?.show()
            }">
            <i class="iconfont icon-xzbm title-icon" />
            选择统计部门
          </ElTag>
          <OrgPicker
            ref="orgPickerRef"
            title="选择统计部门"
            type="dept"
            :multiple="true"
            :selected="selectedDeptList"
            @ok="handleSelectDept" />
        </template>
      </div>
      <div class="content">
        <div
          v-for="item in deptList"
          :key="item.deptId"
          class="item"
          @click="handleClickDept(item)">
          <img
            class="item-img"
            :src="deptIcon"
            alt="">
          <p class="item-name">
            {{ item.deptName }}
          </p>
        </div>
      </div>
    </div>
  </Container>
</template>

<script setup>
import { getDeptList, submitDeptList } from '@/api/presales/task-track.js'
import deptIcon from '@/assets/images/dept-icon.png'
import Container from '@/components/Container/index.vue'
import useUserStore from '@/store/modules/user.js'
import OrgPicker from '@wflow-pro/components/common/OrgPicker.vue'

const userStore = useUserStore()

const isAdmin = computed(() => {
  return userStore.roles.includes('admin') || userStore.roles.includes('operateManage')
})

const orgPickerRef = useTemplateRef('orgPickerRef')
const deptList = ref([])
const selectedDeptList = computed(() => {
  return deptList.value.map(item => ({
    id: item.deptId,
    name: item.deptName,
    type: 'dept',
  }))
})

async function init() {
  try {
    const res = await getDeptList()
    deptList.value = res.data
  } catch (error) {
    console.log(error)
  }
}
onMounted(() => {
  init()
})

function handleSelectDept(data) {
  deptList.value = data.map(item => ({
    deptId: item.id,
    deptName: item.name,
  }))

  submitDeptList({
    deptList: [...deptList.value],
  })
}

const router = useRouter()
function handleClickDept(item) {
  router.push({
    path: '/presales/task-track/detail',
    query: {
      id: item.deptId,
      name: item.deptName,
    },
  })
}
</script>

<style lang="scss" scoped>
.task-track {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  border-radius: 10px;
  background-color: #fff;

  .title {
    display: flex;
    flex: none;
    align-items: center;
    height: 58px;
    padding: 0 18px;
    border-bottom: 1px solid rgb(0 0 0 / 6%);

    &-text {
      margin-right: 12px;
      color: rgb(0 0 0 / 88%);
      font-weight: 600;
      font-size: 20px;
    }

    &-icon {
      margin-right: 8px;
      font-size: 14px;
    }
  }

  .content {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 28px;
    align-content: flex-start;
    overflow: auto;
    padding: 24px 18px;

    .item {
      display: flex;
      align-items: center;
      width: 386px;
      height: 84px;
      padding: 24px;
      border: 1px solid rgb(0 0 0 / 6%);
      border-radius: 10px;
      background-color: #fff;

      &:hover {
        background-color: #e6f4ff;
        cursor: pointer;
      }

      &-img {
        width: 36px;
        height: 36px;
        margin-right: 12px;
      }

      &-name {
        color: rgb(0 0 0 / 88%);
        font-size: 16px;
      }
    }
  }
}
</style>
