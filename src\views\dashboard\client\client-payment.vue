<template>
  <div
    v-loading="loading"
    class="client_payment">
    <div class="client_payment_chart">
      <VChart
        :option="option"
        autoresize />
    </div>
  </div>

  <ElDialog
    v-model="dialogVisible"
    title="客户历年回款"
    width="1140px">
    <div
      ref="search"
      class="mb-[24px]">
      <ElPopover
        placement="bottom-start"
        trigger="click"
        :width="searchWidth"
        :show-arrow="false"
        :teleported="false">
        <template #reference>
          <ElButton>
            <i
              class="iconfont icon-sift"
              :style="{
                marginRight: '8px',
              }" />
            所有筛选
            <ElIcon
              :style="{
                marginLeft: '10px',
              }">
              <ArrowDown />
            </ElIcon>
          </ElButton>
        </template>
        <ElForm
          ref="searchFormRef"
          :model="dialogFormData">
          <ElRow>
            <ElCol :span="6">
              <ElFormItem
                label="客户名称"
                prop="customerName">
                <ElInput v-model="dialogFormData.customerName" />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <div>
          <ElButton
            type="primary"
            :loading="loading"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">
            重置
          </ElButton>
        </div>
      </ElPopover>
    </div>

    <ElTable
      v-loading="loading"
      :data="dialogTableData"
      :border="true"
      @row-click="handleRowClick">
      <ElTableColumn
        label="序号"
        width="80"
        type="index" />
      <ElTableColumn
        label="客户名称"
        prop="customersName"
        show-overflow-tooltip />
      <ElTableColumn
        label="历史签订合同额（元）"
        prop="totalAmount">
        <template #default="{ row }">
          {{ row.totalAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="历史已开票金额（元）"
        prop="totalInvoiceAmount">
        <template #default="{ row }">
          {{ row.totalInvoiceAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="历史已回款金额（元）"
        prop="totalReceiveAmount">
        <template #default="{ row }">
          {{ row.totalReceiveAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="回款比例"
        prop="receiveAmountRatio">
        <template #default="{ row }">
          {{ row.receiveAmountRatio }}%
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="未回款金额（元）"
        prop="nonReceiveAmount">
        <template #default="{ row }">
          {{ row.nonReceiveAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
    </ElTable>

    <div class="flex justify-end py-[16px]">
      <ElPagination
        v-model:current-page="dialogFormData.pageNum"
        v-model:page-size="dialogFormData.pageSize"
        :total="dialogFormData.total"
        layout="total, prev, pager, next, sizes, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </ElDialog>

  <ElDrawer
    v-model="drawerVisible"
    size="700"
    body-class="border-t border-[#000]/[0.08]">
    <template #title>
      <p>
        客户历年回款详情
        <span class="text-[#0073FF]">
          （{{ drawerData.customerName || '--' }}）
        </span>
      </p>
    </template>
    <template #default>
      <div
        v-for="item in drawerData.yearData"
        :key="item.year"
        class="not-first:mt-[32px]">
        <ElDescriptions
          :title="item.year"
          :border="true"
          column="2"
          :label-width="160">
          <ElDescriptionsItem
            label="签订合同个数">
            {{ item.contractNum }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="签订合同金额（元）">
            {{ item.totalAmount.toLocaleString('zh-CN', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }) }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="已开票金额（元）">
            {{ item.totalInvoiceAmount.toLocaleString('zh-CN', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }) }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="已回款金额（元）">
            {{ item.totalReceiveAmount.toLocaleString('zh-CN', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }) }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="回款比例">
            {{ item.receiveAmountRatio }}%
          </ElDescriptionsItem>
          <ElDescriptionsItem label="未回款金额（元）">
            {{ item.nonReceiveAmount.toLocaleString('zh-CN', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }) }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>
    </template>
  </ElDrawer>
</template>

<script setup>
import { getClientPaymentData, getCustomerPaymentDetail } from '@/api/dashboard/client.js'
import { useElementSize } from '@vueuse/core'

const { year, analysisSubject, isAdmin, deptId } = defineProps({
  year: Number,
  analysisSubject: String,
  isAdmin: Boolean,
  deptId: [String, Number],
})

const option = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.06)',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    icon: 'circle',
    textStyle: {
      width: 80,
      overflow: 'truncate',
    },
  },
  grid: {
    top: 32,
    right: 0,
    bottom: 50,
    left: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#C1C5CC',
      },
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '单位：元',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
    },
  ],
  dataZoom: {
    type: 'slider',
    backgroundColor: '#FAFAFA',
    dataBackground: {
      lineStyle: {
        color: '#E4E4E4',
      },
      areaStyle: {
        color: '#F0F0F0',
        opacity: 1,
      },
    },
    selectedDataBackground: {
      lineStyle: {
        color: '#77C6F2',
      },
      areaStyle: {
        color: '#C3E1F2',
        opacity: 1,
      },
    },
    fillColor: '#C3E1F2',
    borderColor: '#EBEBEB',
    borderRadius: 2,
    moveHandleStyle: {
      opacity: 0,
    },
    brushStyle: {
      color: '#E1F1FA',
    },
  },
  series: [
    {
      name: '历史签订合同额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#165DFF',
      },
      barGap: 0,
    },
    {
      name: '历史已开票金额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#14C9C9',
      },
    },
    {
      name: '历史已回款金额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#F7BA1E',
      },
    },
    {
      name: '未回款金额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#722ED1',
      },
    },
    {
      name: '回款比例',
      type: 'line',
      yAxisIndex: 1,
      data: [],
      itemStyle: {
        color: '#3491FA',
      },
    },
  ],
})

const { width: searchWidth } = useElementSize(useTemplateRef('search'))
const searchFormRef = useTemplateRef('searchFormRef')

const dialogVisible = ref(false)
const dialogFormData = reactive({
  customerName: '',
  pageNum: 1,
  pageSize: 10,
  total: 0,
})
const dialogTableData = ref([])

function handleSearch() {
  init(true)
}

function handleReset() {
  searchFormRef.value.resetFields()
}

function handleSizeChange() {
  init(true)
}

function handleCurrentChange() {
  init(true)
}

const loading = ref(false)
function init(isSearch = false) {
  loading.value = true
  getClientPaymentData({
    analysisYear: year,
    analysisEntityName: isAdmin ? analysisSubject?.[analysisSubject.length - 1] : '',
    analysisEntityId: isAdmin ? undefined : deptId,
    customersName: isSearch ? dialogFormData.customerName : undefined,
    pageNum: isSearch ? dialogFormData.pageNum : 1,
    pageSize: isSearch ? dialogFormData.pageSize : 9999,
  }).then((res) => {
    if (isSearch) {
      dialogTableData.value = res.data.list
      dialogFormData.total = res.data.total
      dialogFormData.pageNum = res.data.page
    } else {
      option.value.xAxis.data = res.data.list.map(item => item.customersName)
      option.value.series[0].data = res.data.list.map(item => item.totalAmount)
      option.value.series[1].data = res.data.list.map(item => item.totalInvoiceAmount)
      option.value.series[2].data = res.data.list.map(item => item.totalReceiveAmount)
      option.value.series[3].data = res.data.list.map(item => item.nonReceiveAmount)
      option.value.series[4].data = res.data.list.map(item => item.receiveAmountRatio)
    }
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  init(false)
})

watch(() => [year, analysisSubject, deptId], () => {
  init(false)
})

const drawerVisible = ref(false)
const drawerData = ref({
  customerName: '',
  customerId: '',
  yearData: [],
})

function getRowDetail() {
  getCustomerPaymentDetail({
    analysisYear: year,
    analysisEntityName: isAdmin ? analysisSubject?.[analysisSubject.length - 1] : '',
    analysisEntityId: isAdmin ? undefined : deptId,
    customerId: drawerData.value.customerId,
  }).then((res) => {
    drawerData.value.yearData = res.data
  })
}

function handleRowClick(row) {
  drawerVisible.value = true
  drawerData.value.customerName = row.customersName
  drawerData.value.customerId = row.customerId
  drawerData.value.yearData = []
  getRowDetail()
}

defineExpose({
  showDialog() {
    dialogVisible.value = true
    init(true)
  },
})
</script>

<style lang="scss" scoped>
.client_payment {
  &_chart {
    width: 100%;
    height: 381px;
    margin-top: 12px;
  }
}
</style>
