<template>
  <Container :show-back="true">
    <template
      v-if="showTransformButton"
      #headerRight>
      <ElButton
        type="primary"
        @click="handleTransform">
        <template #icon>
          <i class="iconfont icon-VerticalAlignTop" />
        </template>
        转生态产品
      </ElButton>

      <ElDialog
        v-model="transformDialogVisible"
        title="转生态产品"
        width="500px">
        <ElForm
          ref="transformFormRef"
          :model="transformFormData"
          :rules="transformFormRules"
          label-position="left">
          <ElFormItem
            prop="companyBid"
            label="所属生态伙伴：">
            <ElSelect
              v-model="transformFormData.companyBid"
              :filterable="true"
              :remote="true"
              :remote-method="handleRemoteSearch"
              :loading="remoteSearchLoading"
              placeholder="请选择所属生态伙伴"
              :clearable="true"
              @change="handlePartnerChange">
              <ElOption
                v-for="item in partnerOptions"
                :key="item.companyBid"
                :label="item.companyName"
                :value="item.companyBid" />
            </ElSelect>
          </ElFormItem>
        </ElForm>
        <template #footer>
          <ElButton @click="handleTransformCancel">
            取消
          </ElButton>
          <ElButton
            type="primary"
            @click="handleTransformConfirm">
            确认
          </ElButton>
        </template>
      </ElDialog>
    </template>
    <DetailRender
      :id="id"
      ref="detailRenderRef"
      @init="handleDetailInit" />
  </Container>
</template>

<script setup>
import { getPartnerList } from '@/api/ecology/partner.js'
import { transformToEcologyProduct } from '@/api/ecology/temp-product.js'
import Container from '@/components/Container/index.vue'
import { getGroupModels } from '@wflow-pro/api/modelGroup'
import DetailRender from './render.vue'

const { proxy } = getCurrentInstance()

const route = useRoute()

const id = computed(() => {
  return route.query.productBid || ''
})

const transformDialogVisible = ref(false)
const transformFormRef = useTemplateRef('transformFormRef')
const transformFormData = ref({
  companyBid: '',
  companyName: '',
})
const transformFormRules = {
  companyBid: [{
    required: true,
    message: '所属生态伙伴不能为空',
    trigger: ['blur', 'change'],
  }],
}
function handleTransform() {
  transformDialogVisible.value = true
}

const partnerOptions = ref([])
const remoteSearchLoading = ref(false)
async function handleRemoteSearch(query) {
  try {
    remoteSearchLoading.value = true
    const res = await getPartnerList({
      partnerName: query,
      pageSize: 20,
    })
    partnerOptions.value = res.data.records
  } catch (error) {
    console.log(error)
  } finally {
    remoteSearchLoading.value = false
  }
}

function handlePartnerChange(value) {
  const item = partnerOptions.value.find(item => item.companyBid === value)
  transformFormData.value.companyName = item.companyName
}

function handleTransformCancel() {
  transformDialogVisible.value = false
  transformFormRef.value.resetFields()
}

const processDefId = ref('')
async function getProcessDefId() {
  try {
    const { data: groups } = await getGroupModels()

    const targetConfig = 'ecology-product-transform'

    // 使用数组方法查找匹配项
    const targetItem = groups
      .flatMap(group => group.items)
      .find(item => item.frontendConfig === targetConfig)

    if (targetItem) {
      processDefId.value = targetItem.processDefId
    } else {
      proxy.$modal.msgError('未获取到流程配置')
    }
  } catch (error) {
    console.error('获取流程配置失败:', error)
    proxy.$modal.msgError('获取流程配置失败')
  }
}

const detailRenderRef = useTemplateRef('detailRenderRef')
function handleTransformConfirm() {
  transformFormRef.value.validate((valid, fields) => {
    if (valid) {
      transformToEcologyProduct({
        ...transformFormData.value,
        processDefId: processDefId.value,
        productBid: id.value,
      }).then(() => {
        transformDialogVisible.value = false
        proxy.$modal.msgSuccess('审批通过后会转为生态产品')
        detailRenderRef.value.init()
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}

const productInfo = ref({})
function handleDetailInit(data) {
  productInfo.value = data
}
// 提交审批后不能再转生态产品
const showTransformButton = computed(() => {
  return [0, 4].includes(productInfo.value.status)
})

onMounted(() => {
  detailRenderRef.value.init()
  getProcessDefId()
})
</script>

<style scoped lang="scss">

</style>
