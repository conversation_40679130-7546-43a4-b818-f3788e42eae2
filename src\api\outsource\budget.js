import { get, post, put, remove } from '@/utils/alova.js'

const prefix = '/outsource'

/**
 * 部门年度预算列表
 * @returns
 */
export function getAnnualDeptBudget(data) {
  return post(`${prefix}/annualBudget/list`, data)
}
/**
 * 部门年度预算详情
 */
export function getAnnualDeptBudgetDetail(bid) {
  return get(`${prefix}/annualBudget/${bid}`)
}
/**
 * 部门年度预算人员预算列表
 */
export function getAnnualDeptStaffBudgetList(data) {
  return post(`${prefix}/annualBudget/staffBudgetList`, data)
}
/**
 * 创建部门年度预算
 */
export function addAnnualDeptBudget(data, config = { transformRes: false }) {
  return put(`${prefix}/annualBudget`, data, config)
}
/**
 * 更新部门年度预算
 */
export function updateAnnualDeptBudget(data, config = { transformRes: false }) {
  return post(`${prefix}/annualBudget`, data, config)
}
/**
 * 删除部门年度预算
 */
export function deleteAnnualDeptBudget(bid) {
  return remove(`${prefix}/annualBudget/delete/${bid}`)
}

/**
 * 项目预算列表
 */
export function getProjectBudgetList(data) {
  return post('/project/projectLibrary/queryOutBudgetList', data)
}
/**
 * 项目预算详情
 */
export function getProjectBudgetDetail(projectCode) {
  return post(`/project/projectLibrary/queryOutBudgetDetail/${projectCode}`)
}
/**
 * 项目预算实际使用预算
 */
export function getProjectUsedBudgetList(data) {
  return post(`${prefix}/annualBudget/staffBudgetList`, data)
}
/**
 * 项目预算占用预算
 */
export function getProjectDemandBudgetList(data) {
  return post(`${prefix}/projectBudget/demandList`, data)
}

/**
 * 人员实际预算使用明细
 */
export function getStaffBudgetDetail(data) {
  return get(`${prefix}/annualBudget/staffBudgetDetail`, data)
}
