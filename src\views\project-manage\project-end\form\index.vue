<template>
  <Container
    show-back
    :back-title="backTitle()">
    <template #headerRight>
      <div
        v-if="route.query.pageType !== 'view'"
        class="ml-auto">
        <ElButton
          type="info"
          @click="handleDraft">
          暂存
        </ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit">
          提交
        </ElButton>
      </div>
    </template>
    <EndForm
      :id="route.query.id"
      ref="EndFormRef"
      :form-type="route.query?.pageType" />
  </Container>
</template>

<script setup>
import Container from '@/components/Container/index.vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import EndForm from '../components/end-form.vue'

const route = useRoute()
const router = useRouter()
const EndFormRef = useTemplateRef('EndFormRef')

function backTitle() {
  const pageType = route.query?.pageType
  console.log(pageType, 'pageType')
  const titleMap = {
    form: '新增项目终止、挂起',
    edit: '编辑项目终止、挂起',
    view: '查看项目终止、挂起',
  }
  return titleMap[pageType] || '项目终止、挂起'
}

async function handleSubmit() {
  try {
    EndFormRef.value.onSave().then(() => {
      ElMessage.success('保存成功')
      router.back()
    }).catch((err) => {
      console.log(err)
    })
  } catch (error) {
    console.log(error)
  }
}
function handleDraft() {
  try {
    EndFormRef.value.onSaveDraft().then(() => {
      ElMessage.success('暂存成功')
      router.back()
    }).catch((err) => {
      console.log(err)
    })
  } catch (error) {
    console.log(error)
  }
}
</script>
