<template>
  <Title>项目变更说明</Title>
  <ElDescriptions
    border
    label-width="160"
    :column="2">
    <ElDescriptionsItem
      label="变更类型"
      :span="1">
      {{ formData.changeType }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="是否重大变更"
      :span="1">
      {{ formData.isImportantModification }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="变更原因"
      :span="2">
      {{ formData.changeReason }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="变更内容（变更前）"
      :span="2">
      {{ formData.changeBeforeContent }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="变更内容（变更后）"
      :span="2">
      {{ formData.changeAfterContent }}
    </ElDescriptionsItem>
    <ElDescriptionsItem
      label="变更影响分析"
      :span="2">
      {{ formData.changeImpactAnalysis }}
    </ElDescriptionsItem>
  </ElDescriptions>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const formData = defineModel()
</script>

<style lang="scss" scoped></style>
