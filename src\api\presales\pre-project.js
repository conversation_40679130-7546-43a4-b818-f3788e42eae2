import { get, post } from '@/utils/alova'

/**
 * 获取预立项信息分页列表
 */
export function getPreProjectPageList(data, config) {
  return post('/project/preinit/pre-est-project/pageList', data, config)
}

/**
 * 创建或更新预立项信息
 */
export function createOrUpdatePreProjectInfo(data, config = { transformRes: false }) {
  return post('/project/preinit/pre-est-project/createOrUpdate', data, config)
}

/**
 * 单个查询预立项信息
 */
export function singleQueryPreProjectInfo(id, config = { transformRes: false }) {
  return post(`/project/preinit/pre-est-project/details/${id}`, null, config)
}

/**
 * 批量删除预立项信息
 */
export function batchDeletePreProjectInfo(data, config = { transformRes: false }) {
  return post('/project/preinit/pre-est-project/removeBatch', data, config)
}

/**
 * 提交预立项信息，需审批
 */
export function submitPreProjectInfo(data, config = { transformRes: false }) {
  return post('/project/preinit/pre-est-project/submit', data, config)
}

/**
 * 撤回预立项
 */
export function cancelPreProject(data, config = { transformRes: false }) {
  return post('/project/preinit/pre-est-project/cancel', data, config)
}

/**
 * 获取预立项数量统计
 */
export function queryPreEstProjectCount(data, config = { transformRes: false }) {
  return post('/project/preinit/pre-est-project/queryPreEstProjectCount', data, config)
}

/**
 * 获取部门
 */
export function getDeptList(params, config) {
  return get('/system/dept/list', params, config)
}

/**
 * 获取产品库
 */
export function getProduct(params, config) {
  return post('/ecology/partner/product/all/list', params, config)
}

/**
 * 查看产品详情
 */
export function getProductDetail(params, config) {
  return get('/ecology/partner/product/details', params, config)
}
