<template>
  <div class="business_status">
    <div
      v-for="(item, index) in chartList"
      :key="index"
      class="business_status_chart">
      <div class="flex items-center mb-[12px]">
        <p class="text-[16px] text-[#000]/[.88] ">
          {{ item.name }}
        </p>
        <ElDivider direction="vertical" />
        <ElTag
          class="cursor-pointer"
          effect="light"
          :round="true"
          @click="showDialog(item.name)">
          <div class="flex items-center text-[12px]">
            <ElIcon
              :size="12"
              style="margin-right: 6px;">
              <View />
            </ElIcon>
            数据详情
          </div>
        </ElTag>
      </div>
      <div class="flex items-center mb-[25px]">
        <div class="flex-1 pl-[24px]">
          <p class="text-[#000]/[.45] text-[14px] mb-[4px]">
            目标值
          </p>
          <p class="text-[#000]/[.88] text-[24px] font-[600]">
            {{ item.targetValue || '-' }}
          </p>
        </div>
        <ElDivider
          style="height: 51px;margin: 0;"
          direction="vertical" />
        <div class="flex-1  pl-[24px]">
          <p class="text-[#000]/[.45] text-[14px] mb-[4px]">
            计划值
          </p>
          <p class="text-[#000]/[.88] text-[24px] font-[600]">
            {{ item.planValue || '-' }}
          </p>
        </div>
        <ElDivider
          style="height: 51px;margin: 0;"
          direction="vertical" />
        <div class="flex-1 pl-[24px]">
          <p class="text-[#000]/[.45] text-[14px] mb-[4px]">
            完成值
          </p>
          <p class="text-[#000]/[.88] text-[24px] font-[600]">
            {{ item.actualValue || '-' }}
          </p>
        </div>
      </div>
      <div class="flex items-center p-[6px]">
        <p class="mr-[12px]">
          实际完成率
        </p>
        <ElProgress
          :percentage="item.actualRate"
          color="#52C41A"
          :stroke-width="8"
          class="flex-1" />
      </div>
      <div class="flex items-center p-[6px]">
        <p class="mr-[12px]">
          计划完成率
        </p>
        <ElProgress
          :percentage="item.planRate"
          color="#1677FF"
          :stroke-width="8"
          class="flex-1" />
      </div>
    </div>
  </div>
  <ElDialog
    v-model="dialogData.visible"
    width="55%"
    :title="dialogData.title">
    <div class="w-full h-[450px]">
      <VChart
        :option="option"
        autoresize />
    </div>
  </ElDialog>
</template>

<script setup>
const chartList = ref([
  {
    name: '新增合同',
    targetValue: 100,
    planValue: 120,
    actualValue: 110,
    actualRate: 55,
    planRate: 60,
  },
  {
    name: '业务收入',
    targetValue: 100,
    planValue: 120,
    actualValue: 110,
    actualRate: 55,
    planRate: 60,
  },
  {
    name: '项目回款',
    targetValue: 100,
    planValue: 120,
    actualValue: 110,
    actualRate: 55,
    planRate: 60,
  },
  {
    name: '业务利润',
    targetValue: 100,
    planValue: 120,
    actualValue: 110,
    actualRate: 55,
    planRate: 60,
  },
])

const dialogData = reactive({
  title: '经营现状',
  visible: false,
})
const image = new URL('../../../assets/images/pie-logo.png', import.meta.url).href

const option = ref({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    bottom: 48,
    left: 'center',
    icon: 'circle',
  },
  graphic: {
    elements: [{
      type: 'image',
      left: 'center',
      top: 100,
      style: {
        image,
        width: 120,
        height: 120,
      },
    }],
  },
  series: [
    {
      name: '',
      type: 'pie',
      left: 'center',
      top: 40,
      width: 240,
      height: 240,
      radius: [65, 120],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 5,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: true,
        position: 'outside',
      },
      labelLine: {
        show: true,
        length: 7,
        length2: 30,
      },
      data: [
        { value: 1048, name: '市场拓展一部' },
        { value: 735, name: '市场拓展二部' },
        { value: 580, name: '数据集团' },
        { value: 484, name: '市场拓展三部' },
        { value: 300, name: '企业服务事业部' },
        { value: 300, name: '医疗健康事业部' },
        { value: 300, name: '经营管理部' },
        { value: 300, name: '产品研发部' },
        { value: 300, name: '集成交付部' },
        { value: 300, name: '襄阳公司' },
        { value: 300, name: '鄂州公司' },
        { value: 300, name: '光谷公司' },
        { value: 300, name: '仙桃公司' },
      ],
    },
  ],
})
function showDialog(name) {
  dialogData.title = `经营现状 - ${name}`
  dialogData.visible = true
}
</script>

<style lang="scss" scoped>
.business_status {
  display: flex;
  overflow: auto;
  margin: 0 -4px;
  margin-bottom: 18px;

  &_chart {
    flex: none;
    width: 400px;
    height: 198px;
    margin-right: 12px;
    padding: 12px;
    border-radius: 8px;
    background-color: rgb(0 0 0 / 2%);
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
