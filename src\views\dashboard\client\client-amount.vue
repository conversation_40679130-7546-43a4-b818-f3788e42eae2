<template>
  <div
    v-loading="loading"
    class="client_amount">
    <div class="client_amount_header">
      <p class="text-[#000]/[.45] text-[16px]">
        总合同额
      </p>
      <p class="text-[#000]/[.88] text-[28px]">
        {{ formatTotalAmount }}
        <span class="text-[16px]">
          万元
        </span>
      </p>
    </div>
    <div class="client_amount_chart">
      <VChart
        :option="option"
        autoresize />
    </div>
  </div>

  <ElDialog
    v-model="dialogVisible"
    title="客户合同额"
    width="1140px">
    <div
      ref="search"
      class="mb-[24px]">
      <ElPopover
        placement="bottom-start"
        trigger="click"
        :width="searchWidth"
        :show-arrow="false"
        :teleported="false">
        <template #reference>
          <ElButton>
            <i
              class="iconfont icon-sift"
              :style="{
                marginRight: '8px',
              }" />
            所有筛选
            <ElIcon
              :style="{
                marginLeft: '10px',
              }">
              <ArrowDown />
            </ElIcon>
          </ElButton>
        </template>
        <ElForm
          ref="searchFormRef"
          :model="dialogFormData">
          <ElRow>
            <ElCol :span="6">
              <ElFormItem
                label="客户名称"
                prop="customerName">
                <ElInput v-model="dialogFormData.customerName" />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <div>
          <ElButton
            type="primary"
            :loading="loading"
            @click="handleSearch">
            查询
          </ElButton>
          <ElButton @click="handleReset">
            重置
          </ElButton>
        </div>
      </ElPopover>
    </div>

    <ElTable
      v-loading="loading"
      :data="dialogTableData"
      :border="true"
      :show-summary="true"
      :summary-method="getSummaries">
      <ElTableColumn
        label="合同额排名"
        width="90"
        type="index" />
      <ElTableColumn
        label="甲方（主体信息）"
        prop="customersName"
        show-overflow-tooltip />
      <ElTableColumn
        label="合同金额（含税：万元）"
        prop="totalAmount">
        <template #default="{ row }">
          {{ row.totalAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="合同个数"
        prop="contractNum" />
      <ElTableColumn
        label="平均合同金额（万元）"
        prop="amountAvg">
        <template #default="{ row }">
          {{ row.amountAvg.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          }) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="客户合同额占比"
        prop="contractRatio">
        <template #default="{ row }">
          {{ row.contractRatio }}%
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="累计客户合同额占比"
        prop="cumulativeContractRatio">
        <template #default="{ row }">
          {{ row.cumulativeContractRatio }}%
        </template>
      </ElTableColumn>
    </ElTable>
  </ElDialog>
</template>

<script setup>
import { getClientAmountData } from '@/api/dashboard/client.js'
import { useElementSize } from '@vueuse/core'

const { year, analysisSubject, isAdmin, deptId } = defineProps({
  year: Number,
  analysisSubject: String,
  isAdmin: Boolean,
  deptId: [String, Number],
})

const totalAmount = ref(0)
const formatTotalAmount = computed(() => {
  return totalAmount.value.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
})

const option = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.06)',
      },
    },
  },
  legend: {
    top: 0,
    right: 0,
    icon: 'circle',
  },
  grid: {
    top: 32,
    right: 0,
    bottom: 50,
    left: 0,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#C1C5CC',
      },
    },
  },
  yAxis: [
    {
      type: 'value',
      name: '单位：万元',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E5E6EB',
        },
      },
    },
    {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
    },
  ],
  dataZoom: {
    type: 'slider',
    backgroundColor: '#FAFAFA',
    dataBackground: {
      lineStyle: {
        color: '#E4E4E4',
      },
      areaStyle: {
        color: '#F0F0F0',
        opacity: 1,
      },
    },
    selectedDataBackground: {
      lineStyle: {
        color: '#77C6F2',
      },
      areaStyle: {
        color: '#C3E1F2',
        opacity: 1,
      },
    },
    fillColor: '#C3E1F2',
    borderColor: '#EBEBEB',
    borderRadius: 2,
    moveHandleStyle: {
      opacity: 0,
    },
    brushStyle: {
      color: '#E1F1FA',
    },
  },
  series: [
    {
      name: '合同额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#165DFF',
      },
      barGap: 0,
      barWidth: 24,
    },
    {
      name: '平均合同额',
      type: 'bar',
      data: [],
      itemStyle: {
        color: '#14C9C9',
      },
      barWidth: 24,
    },
    {
      name: '占比',
      type: 'line',
      yAxisIndex: 1,
      data: [],
      itemStyle: {
        color: '#F7BA1E',
      },
    },

  ],
})

const { width: searchWidth } = useElementSize(useTemplateRef('search'))
const searchFormRef = useTemplateRef('searchFormRef')

const dialogVisible = ref(false)
const dialogFormData = reactive({
  customerName: '',
})
const dialogTableData = ref([])

function getSummaries(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    if (index === 1 || index === 4 || index === 5 || index === 6) {
      sums[index] = ''
      return
    }

    if (index === 2) {
      sums[index] = data.reduce((prev, curr) => {
        return prev + curr[column.property]
      }, 0).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
      return
    }

    if (index === 3) {
      sums[index] = data.reduce((prev, curr) => {
        return prev + curr[column.property]
      }, 0)
    }
  })
  return sums
}

function handleSearch() {
  init(true)
}

function handleReset() {
  searchFormRef.value.resetFields()
}

const loading = ref(false)
function init(isSearch = false) {
  loading.value = true
  getClientAmountData({
    analysisYear: year,
    analysisEntityName: isAdmin ? analysisSubject?.[analysisSubject.length - 1] : '',
    analysisEntityId: isAdmin ? undefined : deptId,
    customerName: isSearch ? dialogFormData.customerName : undefined,
  }).then((res) => {
    if (isSearch) {
      for (let i = 0; i < res.data.list.length; i++) {
        if (i === 0) {
          res.data.list[i].cumulativeContractRatio = res.data.list[i].contractRatio
        } else {
          res.data.list[i].cumulativeContractRatio = Number(
            (
              res.data.list[i].contractRatio + res.data.list[i - 1].cumulativeContractRatio
            ).toFixed(2),
          )
        }
      }
      dialogTableData.value = res.data.list
    } else {
      totalAmount.value = res.data.totalAmount
      option.value.xAxis.data = res.data.list.map(item => item.customersName)
      option.value.series[0].data = res.data.list.map(item => item.totalAmount)
      option.value.series[1].data = res.data.list.map(item => item.amountAvg)
      option.value.series[2].data = res.data.list.map(item => item.contractRatio)
    }
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  init(false)
})

watch(() => [year, analysisSubject, deptId], () => {
  init(false)
})

defineExpose({
  showDialog: () => {
    dialogVisible.value = true
    init(true)
  },
})
</script>

<style lang="scss" scoped>
.client_amount {
  &_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 56px;
    padding: 0 18px;
    border-radius: 8px;
    background-color: rgb(0 0 0 / 2%);
  }

  &_chart {
    width: 100%;
    height: 308px;
    margin-top: 12px;
  }
}
</style>
