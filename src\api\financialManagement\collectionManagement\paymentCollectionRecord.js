import request from '@/utils/request'
// 导入回款记录
export function importData(data) {
  return request({
    url: `/finance/receive/money/record/importData`,
    method: 'post',
    data,
  })
}

// 获取回款记录分页列表
export function getReceiveMoneyRecordPageList(params) {
  return request({
    url: `/finance/receive/money/record/pageList`,
    method: 'post',
    data: params,
  })
}

// 删除回款记录
export function delReceiveMoneyRecord(params) {
  return request({
    url: `/finance/receive/money/record/del`,
    method: 'get',
    params,
  })
}

// 回款记录详情
export function getReceiveMoneyRecordDetails(params) {
  return request({
    url: `/finance/receive/money/record/detail`,
    method: 'get',
    params,
  })
}

// 提交回款记录申请信息，需审批
export function submitReceiveMoneyRecord(params) {
  return request({
    url: `/finance/receive/money/record/submit`,
    method: 'post',
    data: params,
  })
}

// 根据合同编号查询累计金额（回款记录认领变更-已认领金额）
export function sumAmountBycontractNumber(params) {
  return request({
    url: `/finance/receive/money/record/sumAmountBycontractNumber`,
    method: 'get',
    params,
  })
}

// 根据项目编号查询累计金额（开票申请页面-累计回款）
export function sumAmountByprojectNumber(params) {
  return request({
    url: `/finance/receive/money/record/sumAmountByProjectCode`,
    method: 'get',
    params,
  })
}
