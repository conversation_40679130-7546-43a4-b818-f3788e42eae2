import { get, post } from '@/utils/alova.js'
import request from '@/utils/request.js'
/**
 * 商机终止/中止列表
 * @param {*} data
 * @returns
 */
export function businessInactivationList(data) {
  return post('/project/business/break/list', data)
}
/**
 * 新增(暂存)商机终止/中止
 */
export function businessInactivationAdd(data, config = { transformRes: false }) {
  return post('/project/business/break/add', data, config)
}
/**
 * 删除商机终止/中止
 */
export function businessInactivationDel(id, config = { transformRes: false }) {
  return post(`/project/business/break/del?id=${id}`, config)
}
/**
 * 商机详情
 */
export function businessInactivationDetail(id) {
  return get(`/project/business/break/detail?id=${id}`)
}
/**
 * 商机重启
 */
export function restartBusiness(id, config = { transformRes: false }) {
  return post(`/project/business/library/restart/${id}`, config)
}
/**
 * 商机终止/中止下载
 */
export function businessInactivationDownload(ids) {
  return request({
    method: 'get',
    url: `/project/business/break/download?ids=${ids}`,
    responseType: 'blob',
  })
}
/**
 * 文件预览
 */
export function businessInactivationPreview(url) {
  return request({
    method: 'get',
    url: `/common/preview?url=${url}`,
  })
}
