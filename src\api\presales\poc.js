import request from '@/utils/request'

/**
 * 分页查询POC工作申请列表
 */
export function getPocPage(data) {
  return request({
    url: '/project/poc/page',
    method: 'post',
    data,
  })
}
/**
 * 统计POC工作申请列表数据
 */
export function getPocCount(data) {
  return request({
    url: '/project/poc/count',
    method: 'post',
    data,
  })
}
/**
 * 新增或修改poc项目信息
 */
export function savePoc(data) {
  return request({
    url: '/project/poc/application',
    method: 'post',
    data,
  })
}
/**
 * 新增或修改poc项目信息
 */
export function pocDetail(id) {
  return request({
    url: `/project/poc/detail/${id}`,
    method: 'get',
  })
}
/**
 * 删除poc项目信息
 */
export function pocDelete(id) {
  return request({
    url: `/project/poc/delete/${id}`,
    method: 'post',
  })
}

export function getPocDropDownData() {
  return request({
    url: '/project/poc/getDropDownData',
    method: 'get',
  })
}

export function getProjectDetail(projectCode) {
  return request({
    url: '/project/business/library/get/byCode',
    method: 'get',
    params: {
      projectCode,
    },
  })
}

export function save_poc_change(data) {
  return request({
    url: '/project/poc/modify',
    method: 'post',
    data,
  })
}
/**
 * 通过交付主体的Code 查询对应的部门Id
 * @param {*} data
 * @returns
 */
export function onSearchDeptId(code) {
  return request({
    url: `/system/dept/list?sysCode=${code}`,
    method: 'get',
  })
}
