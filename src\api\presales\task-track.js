import request from '@/utils/request.js'

const prefix = '/project'

/**
 * 获取售前任务跟踪部门列表
 * @param {*} data
 * @returns
 */
export function getDeptList(data) {
  return request({
    url: `${prefix}/customer/analysis/get/deptList`,
    method: 'get',
    params: data,
  })
}

/**
 * 提交售前任务跟踪部门列表
 * @param {*} data
 * @returns
 */
export function submitDeptList(data) {
  return request({
    url: `${prefix}/customer/analysis/customize/deptList`,
    method: 'post',
    data,
  })
}

/**
 * 获取售前任务跟踪详情
 * @param {*} data
 * @returns
 */
export function getTaskTrackDetail(data) {
  return request({
    url: `${prefix}/customer/analysis/task/tracking`,
    method: 'post',
    data,
  })
}
