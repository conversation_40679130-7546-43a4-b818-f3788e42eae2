<template>
  <ElDialog
    v-model="model"
    title="选择应用"
    width="1000px"
    @open="onOpen">
    <ElTable
      v-loading="loading"
      style="margin-top: 20px"
      max-height="calc(70vh - 100px)"
      :data="tableData">
      <ElTableColumn
        label=""
        width="80">
        <template #default="{ row }">
          <ElButton
            link
            type="primary"
            @click="selectItem(row)">
            选择
          </ElButton>
        </template>
      </ElTableColumn>
      <ElTableColumn
        label="序号"
        type="index"
        :index="indexMethod"
        width="50"
        fixed="left"
        align="center" />
      <ElTableColumn
        label="应用系统名称"
        prop="appName"
        min-width="120"
        align="center" />
      <ElTableColumn
        label="客户名称"
        min-width="120"
        prop="customerName"
        align="center" />
    </ElTable>
    <div
      style="
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin-top: 10px;
        ">
      <div class="pagination-container">
        <ElPagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total,prev,pager,next,sizes,jumper"
          :total="pagination.total"
          @size-change="onSizeChange"
          @current-change="onSearch" />
      </div>
    </div>
  </ElDialog>
</template>

<script setup>
import { getCtCloudAppSystemPageList } from '@/api/ctyOrderManagement/systemLedger.js'
import { usePagination } from '@/utils/hooks.js'

const emit = defineEmits(['selectItem'])
const model = defineModel()
const tableData = ref([])

const { pagination, indexMethod } = usePagination()
const loading = ref(false)

function selectItem(row) {
  emit('selectItem', row)
  model.value = false
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getGzCloudAppSystemPageFun()
}

/**
  /**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  getGzCloudAppSystemPageFun()
}

function onOpen() {
  pagination.pageNum = 1 // 重置到第一页
  pagination.pageSize = 10
  getGzCloudAppSystemPageFun()
}

/**
 * 获取分页列表
 */
function getGzCloudAppSystemPageFun() {
  loading.value = true
  const searchData = {
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    params: {},
  }

  getCtCloudAppSystemPageList(searchData)
    .then((res) => {
      console.log(res, '---- getCtCloudAppSystemPageList')
      tableData.value = res.data.list
      pagination.total = res.data.total
    })
    .catch((err) => {
      console.log(err, '---- getCtCloudAppSystemPageList')
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

  <style lang="scss" scoped></style>
