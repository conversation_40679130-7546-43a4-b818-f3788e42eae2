import { post } from '@/utils/alova.js'
/**
 * 工时月度薪酬
 */

// 月度薪酬导入分页列表
export function getSalartList(data) {
  return post('/workhour/salary/pageList', data)
}

// 获取月度薪酬导入详情分页列表
export function getSalatyDetailList(data) {
  return post('/workhour/salary/details/pageList', data)
}

// 导入月度薪酬
export function importSalaryFile(data, config = { transformRes: false }) {
  return post('/workhour/salary/import', data, config)
}

// 导入月度薪酬文件
export function uploadSalaryFile(data, config = {
  transformRes: false,
  headers: {
    'content-type': 'multipart/form-data',
  },
}) {
  return post('/workhour/salary/upload/detail', data, config)
}

// 导入月度年金文件
export function uploadAnnuityFile(data, config = {
  transformRes: false,
  headers: {
    'content-type': 'multipart/form-data',
  },
}) {
  return post('/workhour/salary/upload/annuity', data, config)
}

// 导入月度公积金文件
export function uploadAccumulationFundFile(data, config = {
  transformRes: false,
  headers: {
    'content-type': 'multipart/form-data',
  },
}) {
  return post('/workhour/salary/upload/housingFund', data, config)
}

// 导入月度社保文件
export function uploadSocialSecurityFile(data, config = {
  transformRes: false,
  headers: {
    'content-type': 'multipart/form-data',
  },
}) {
  return post('/workhour/salary/upload/socialSecurity', data, config)
}
