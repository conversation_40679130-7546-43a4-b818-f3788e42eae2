import request, { get, post } from '@/utils/request'

/**
 * 获取公司基本信息
 * @param {*} data
 * @returns
 */
export function getCompanyInfo(data) {
  return request({
    url: '/ecology/audit/company/info',
    method: 'get',
    params: data,
  })
}

/**
 * 获取公司介绍附件
 * @param {*} data
 * @returns
 */
export function getIntroduceAttach(data) {
  return request({
    url: '/ecology/audit/company/introduce',
    method: 'get',
    params: data,
  })
}/**
  * 获取公司基本信息
  * @param {*} data
  * @returns
  */
export function getSolutionAttach(data) {
  return request({
    url: '/ecology/audit/company/solution',
    method: 'get',
    params: data,
  })
}/**
  * 发起终审评审
  * @param {*} data
  * @returns
  */
export function sendFinalReview(data) {
  return post('/ecology/audit/final/review', data)
}/**
  * 生态伙伴初审
  * @param {*} data
  * @returns
  */
export function sendPreliminaryAudit(data) {
  return post('/ecology/audit/preliminary', data)
}/**
  * 添加初审评审人
  * @param {*} data
  * @returns
  */
export function addReviewer(data) {
  return post('/ecology/audit/preliminary/addReviewer', data)
}/**
  * 初审通知生态伙伴
  * @param {*} data
  * @returns
  */
export function notifyPreliminary(data) {
  return post('/ecology/audit/preliminary/notify', data)
}/**
  * 获取初审评审人列表
  * @param {*} data
  * @returns
  */
export function getReviewerList(data) {
  return request({
    url: '/ecology/audit/preliminary/reviewerList',
    method: 'get',
    params: data,
  })
}/**
  * 初审评审人打分
  * @param {*} data
  * @returns
  */
export function reviewerScoring(data) {
  return post('/ecology/audit/preliminary/scoring', data)
}/**
  * 提交评审结果
  * @param {*} data
  * @returns
  */
export function submitScore(data) {
  return post('/ecology/audit/preliminary/submit', data)
}/**
  * 获取审核列表
  * @param {*} data
  * @returns
  */
export function getAuditList(data) {
  return request({
    url: '/ecology/audit/recordList',
    method: 'get',
    params: data,
  })
}

/**
 * 获取伙伴分类
 * @param {*} data
 * @returns
 */
export function getPartnerClass(data) {
  return request({
    url: '/ecology/company/class',
    method: 'get',
    params: data,
  })
}

/**
 * 查看评审结果
 * @param {*} data
 * @returns
 */
export function getAuditResult(data) {
  return request({
    url: '/ecology/audit/result',
    method: 'get',
    params: data,
  })
}

/**
 * 获取伙伴标签
 * @param {*} data
 * @returns
 */
export function getCompanyTag(data) {
  return request({
    url: '/ecology/audit/company/tag',
    method: 'get',
    params: data,
  })
}

/**
 * 生态伙伴终审保存-入库
 * @param {*} data
 * @returns
 */
export function finalReviewStore(data) {
  return post('/ecology/audit/final/review/store', data)
}

/**
 * 终审通知生态伙伴
 * @param {*} data
 * @returns
 */
export function finalReviewNotify(data) {
  return post('/ecology/audit/final/review/notify', data)
}

/**
 * 撤回审核信息
 * @param {*} data
 * @returns
 */
export function auditWithdraw(data) {
  return post('/ecology/audit/withdraw', data)
}

/**
 * 获取对接人审核列表
 * @param {*} data
 * @returns
 */
export function getAuditListContact(data) {
  return get('/ecology/audit/recordList/contact', data)
}
