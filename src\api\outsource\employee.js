import request from '@/utils/request'

const prefix = '/outsource'
const baseUrl = import.meta.env.VITE_APP_BASE_API
console.log(baseUrl)

/**
 * 人员管理列表
 */
export function getStaffList(query) {
  return request({
    url: `${prefix}/staff/list`,
    method: 'get',
    params: query,
  })
}
// 外包人员选择器（获取人员进场信息列表）
export function getDemandList(query) {
  return request({
    url: `${prefix}/demand/position/list`,
    method: 'get',
    params: query,
  })
}
export function getDetail(params) {
  return request({
    url: `/outsource/demand/detail?bid=${params}`,
    method: 'get',
  })
}
// 供应商列表
export function getSupplierSelectList(query) {
  return request({
    url: `/outsource/supplier/list`,
    method: 'get',
    params: query,
  })
}
// 保存人员
export function saveStaff(query) {
  return request({
    url: `${prefix}/staff/save`,
    method: 'post',
    data: query,
  })
}
// 查询人员
export function getStaffDetail(query) {
  return request({
    url: `${prefix}/staff/detail?bid=${query}`,
    method: 'get',
  })
}
// 撤回外包人员列表
export function withdrawnStaffList(query) {
  return request({
    url: `${prefix}/staff/withdrawn`,
    method: 'get',
    params: query,
  })
}
// 删除外包人员列表
export function deletStaffList(data) {
  return request({
    url: `${prefix}/staff/del/${data}`,
    method: 'post',
  })
}
// 到处
export function exportStaffList(query) {
  return request({
    url: `${prefix}/staff/export`,
    method: 'post',
    data: query,
  })
}
