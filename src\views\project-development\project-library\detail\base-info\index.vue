<template>
  <div style="padding: 15px;">
    <BaseInfo v-model="formData" />
    <BudgetInfo v-model="formData" />
    <OwnProductsAndServices v-model="formData" />
    <DevProjectRevenueForecast v-model="formData" />
    <BudgetExecDetection v-model="formData" />
  </div>
</template>

<script setup>
import { getProjectLibraryBaseInfoDetail } from '@/api/project-development/project-library.js'
import BaseInfo from './baseInfo.vue'
import BudgetExecDetection from './budgetExecDetection.vue'
import BudgetInfo from './budgetInfo.vue'
import DevProjectRevenueForecast from './devProjectRevenueForecast.vue'
import OwnProductsAndServices from './ownProductsAndServices.vue'

const props = defineProps({
  projectCode: {
    default: '',
  },
})

const formData = ref({
  budgetInfo: {
    outsourcingCost: [],
    jobLaborCost: [],
    manageCost: [],
    budgetOverview: {
      manageCost: [],
    },
  },
  incomeDetails: [],
  selfOwnedProductsServicesDetail: [],
})

onMounted(async () => {
  if (props.projectCode) {
    const res = await getProjectLibraryBaseInfoDetail(props.projectCode)
    formData.value = res
  }
})
</script>

<style lang="scss" scoped>

</style>
