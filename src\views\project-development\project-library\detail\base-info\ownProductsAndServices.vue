<template>
  <Title style="margin-top: 20px;">
    自有产品及服务
  </Title>
  <ElTable
    border
    style="width: 100%;"
    :data="form.selfOwnedProductsServicesDetail"
    show-summary
    :summary-method="rjcpSummaryMethod">
    <ElTableColumn
      label="产品名称"
      prop="name"
      align="center" />
    <ElTableColumn
      label="产品分类"
      prop="category"
      align="center" />
    <ElTableColumn
      label="类目"
      prop="categoryName"
      align="center" />
    <ElTableColumn
      label="标准价格（元）"
      prop="standardPrice"
      align="center" />
    <ElTableColumn
      label="数量"
      prop="quantity"
      align="center" />
    <ElTableColumn
      label="折扣"
      prop="discount"
      align="center" />
    <ElTableColumn
      label="折扣单价（元）"
      prop="discountPrice"
      align="center" />
    <ElTableColumn
      label="历史成交价格（元）"
      prop="historicalDealPrice"
      align="center" />
    <ElTableColumn
      label="选品理由"
      prop="selectProductReason"
      align="center" />
  </ElTable>
</template>

<script setup>
import Title from '@/components/Title/index.vue'

const form = defineModel()

function rjcpSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    if (index === 1) {
      sums[index] = data.reduce((sum, row) => {
        const quantity = Number(row.quantity) || 0
        const discountPrice = Number(row.discountPrice) || 0
        return sum + quantity * discountPrice
      }, 0)
    }
  })
  return sums
}
</script>

<style lang="scss" scoped>

</style>
