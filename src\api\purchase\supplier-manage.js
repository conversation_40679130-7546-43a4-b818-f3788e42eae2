import { get, post, put, remove } from '@/utils/alova'

/**
 * 获取供应商分页列表
 */
export function getSupplierPageList(data, config) {
  return post('/purchaseManage/supplier/pageList', data, config)
}

/**
 * 创建供应商
 */
export function createSupplierInfo(data, config = { transformRes: false }) {
  return post('/purchaseManage/supplier/create', data, config)
}

/**
 * 单个查询供应商信息
 */
export function singleQuerySupplierInfo(bid, config = { transformRes: false }) {
  return get(`/purchaseManage/supplier/detail/${bid}`, null, config)
}

/**
 * 删除供应商信息
 */
export function deleteSupplierInfo(bid, config = { transformRes: false }) {
  return remove(`/purchaseManage/supplier/delete/${bid}`, null, config)
}

/**
 * 编辑供应商信息
 */
export function updateSupplierInfo(data, config = { transformRes: false }) {
  return put('/purchaseManage/supplier/updateByBid', data, config)
}

/**
 * 查询并比较来源为创建和生态库的相同供应商
 */
export function compareSupplierInfo(params, config = { transformRes: false }) {
  return get('/purchaseManage/supplier/mergeComparison', params, config)
}

/**
 * 合并来源为创建和生态库的相同供应商
 */
export function mergeSupplierInfo(creditCode, config = { transformRes: false }) {
  return post(`/purchaseManage/supplier/mergeData/${creditCode}`, null, config)
}

/**
 * 详情页招采发起分页列表
 */
export function getProcurementPageList(params, config) {
  return get('/purchaseManage/supplier/procurementPageList', params, config)
}

/**
 * 详情页定标审批分页列表
 */
export function getCalibratePageList(params, config) {
  return get('/purchaseManage/supplier/calibratePageList', params, config)
}

/**
 * 详情页供应商审批单分页列表
 */
export function getSupplierApprovalPageList(params, config) {
  return get('/purchaseManage/supplier/supplierApprovalPageList', params, config)
}
