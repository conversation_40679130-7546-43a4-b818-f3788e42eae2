import { getFlowIdByModuleId } from '@/utils/hooks'
import request from '@wflow-pro/api/request.js'
// 获取用户待办
export function getUserTodoList(params) {
  return request({
    url: `wflow/process/todoTask/todoList`,
    method: 'post',
    data: params,
  })
}

// 获取抄送我的流程
export function getCcMeList(params) {
  return request({
    url: `wflow/process/todoTask/ccMeList`,
    method: 'post',
    data: params,
  })
}

// 获取我已处理的所有审批实例
export function getIdoList(params) {
  return request({
    url: `wflow/process/todoTask/idoList`,
    method: 'post',
    data: params,
  })
}

// 获取我办理的已经完结的所有审批实例
export function getDoneList(params) {
  return request({
    url: `wflow/process/todoTask/finishedList`,
    method: 'post',
    data: params,
  })
}

// 获取用户发起的实例
export function getUserSubmittedList(params) {
  return request({
    url: `wflow/process/todoTask/mySubmittedList`,
    method: 'post',
    data: params,
  })
}

// 获取流程授权列表
export function getProcessAuthList(params) {
  return request({
    url: `wflow/process/authorize/pageList`,
    method: 'post',
    data: params,
  })
}

// 新增流程授权
export function createProcessAuth(data) {
  return request({
    url: `wflow/process/authorize/create`,
    method: 'post',
    data,
  })
}

// 编辑流程授权
export function editProcessAuth(id, data) {
  return request({
    url: `/wflow/process/authorize/update/${id}`,
    method: 'post',
    data,
  })
}

// 终止流程授权
export function endProcessAuth(id, data) {
  return request({
    url: `/wflow/process/authorize/switchEnable/${id}`,
    method: 'post',
    data,
  })
}

export function getProcessList() {
  return request({
    url: '/wflow/model/group/list',
    method: 'get',
  })
}

export async function getCurrentProcessId(frontendConfig) {
  try {
    const { data } = await request({
      url: '/wflow/model/group/list',
      method: 'get',
    })
    const { processDefId } = await getFlowIdByModuleId(frontendConfig, data)
    return processDefId
  } catch (e) {
    return Promise.reject(e)
  }
}
