<template>
  <Title style="margin-top: 20px;">
    自有产品及服务
  </Title>
  <div style="float: right; margin-bottom: 10px">
    <ElButton
      type="primary"
      icon="plus"
      @click="onAddDialog()">
      新建
    </ElButton>
  </div>
  <ElTable
    border
    style="width: 100%;"
    :data="form.baseInfo.selfOwnedProductsServicesDetail"
    show-summary
    :summary-method="rjcpSummaryMethod">
    <ElTableColumn
      label="产品名称"
      prop="name"
      align="center" />
    <ElTableColumn
      label="产品分类"
      prop="category"
      align="center" />
    <ElTableColumn
      label="类目"
      prop="categoryName"
      align="center" />
    <ElTableColumn
      label="标准价格（元）"
      prop="standardPrice"
      align="center" />
    <ElTableColumn
      label="数量"
      prop="quantity"
      align="center" />
    <ElTableColumn
      label="折扣"
      prop="discount"
      align="center" />
    <ElTableColumn
      label="折扣单价（元）"
      prop="discountPrice"
      align="center" />
    <ElTableColumn
      label="历史成交价格（元）"
      prop="historicalDealPrice"
      align="center" />
    <ElTableColumn
      label="选品理由"
      prop="selectProductReason"
      align="center" />
    <ElTableColumn
      label="操作"
      prop="operate"
      align="center"
      fixed="right">
      <template #default="{ row }">
        <ElButton
          link
          type="danger"
          @click="onDeleteDialog(row)">
          删除
        </ElButton>
        <ElButton
          link
          type="warning"
          @click="onEditDialog(row)">
          编辑
        </ElButton>
      </template>
    </ElTableColumn>
  </ElTable>
  <RjcpDialog
    v-model="showDialog"
    :info="rjcpDialogInfo"
    @confirm="onDialogConfirm"
    @cancel="resetDialogForm" />
</template>

<script setup>
import Title from '@/components/Title/index.vue'
import { generateUniqueKey } from '@/utils/hooks.js'
import RjcpDialog from '@/views/project-development/project-application/form/components/rjcp-dialog.vue'
import { ElMessageBox } from 'element-plus'

const form = defineModel()

const showDialog = ref(false)

const rjcpDialogInfo = ref({})

function onDialogConfirm({ formData }) {
  // 修改
  if (formData?.id || formData?.uuid) {
    const index = form.value.baseInfo.selfOwnedProductsServicesDetail.findIndex((item) => {
      if (item.uuid) {
        return item.uuid === formData.uuid
      }
      if (item.id) {
        return item.id === formData.id
      }
      return false
    })
    form.value.baseInfo.selfOwnedProductsServicesDetail.splice(
      index,
      1,
      formData,
    )
    resetDialogForm()
  } else {
    form.value.baseInfo.selfOwnedProductsServicesDetail.push({
      ...formData,
      uuid: generateUniqueKey(),
    })
  }
}

function rjcpSummaryMethod(param) {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    if (index === 1) {
      sums[index] = data.reduce((sum, row) => {
        const quantity = Number(row.quantity) || 0
        const discountPrice = Number(row.discountPrice) || 0
        return sum + quantity * discountPrice
      }, 0)
    }
  })
  return sums
}

function resetDialogForm() {
  rjcpDialogInfo.value = {}
}

function onAddDialog() {
  rjcpDialogInfo.value = {}
  showDialog.value = true
}

function onEditDialog(row) {
  rjcpDialogInfo.value = row
  showDialog.value = true
}

function onDeleteDialog(formData) {
  ElMessageBox({
    title: '警告',
    type: 'warning',
    message: '确定要删除该条记录吗?',
    showCancelButton: true,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        const index = form.value.baseInfo.selfOwnedProductsServicesDetail.findIndex((item) => {
          if (item.uuid) {
            return item.uuid === formData.uuid
          }
          if (item.id) {
            return item.id === formData.id
          }
          return false
        })
        form.value.baseInfo.selfOwnedProductsServicesDetail.splice(
          index,
          1,
        )
      }
      done()
    },
  })
}
</script>

<style lang="scss" scoped>

</style>
