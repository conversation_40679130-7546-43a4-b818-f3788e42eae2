<template>
  <DefaultContainer v-loading="loading">
    <div
      v-if="!props.id"
      class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          资金计划详情
        </div>
      </div>
    </div>
    <div class="content">
      <div class="sub-title">
        基础信息
      </div>
      <ElDescriptions
        border
        :column="3"
        style="margin-bottom: 36px"
        label-width="300">
        <ElDescriptionsItem
          v-for="item in descriptionList1"
          :key="item.value"
          width="398">
          <template #label>
            <div>{{ item.label }}</div>
          </template>
          {{ detail[item.value] }}
        </ElDescriptionsItem>
      </ElDescriptions>
      <div class="sub-title">
        概览
      </div>
      <Overview :month="detail.fillMonth" />
      <div class="sub-title">
        资金计划明细
      </div>
      <div class="table-header">
        <!-- 表头显示隐藏控制面板组件 -->
        <TableHeaderSetting
          v-model:checked-columns="checkedColumns"
          :columns="columns"
          :always-visible-columns="alwaysVisibleColumns" />
        <ElInput
          v-model="search"
          placeholder="搜索资金计划明细"
          style="width: 200px;margin-left: 10px;"
          clearable
          @change="searchProject" />
        <ElButton
          type="primary"
          style="margin-left: 10px;"
          @click="exportData">
          导出
        </ElButton>
      </div>
      <ElTable
        :data="filteredPlanProjectList"
        :span-method="arraySpanMethod"
        border
        stripe
        style="width: 95%; margin-top: 10px">
        <!-- 只读/展示列加v-if -->
        <ElTableColumn
          v-if="isColumnVisible('warnMessage')"
          prop="warnMessage"
          label="预警消息"
          width="120"
          fixed="left">
          <template #default="{ row }">
            <span
              style="color: red">
              {{ row.warnMessage }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          v-if="isColumnVisible('deliveryEntity')"
          prop="deliveryEntity"
          label="交付主体"
          width="120"
          fixed="left" />
        <ElTableColumn
          v-if="isColumnVisible('marketEntity')"
          prop="marketEntity"
          label="市场主体"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('projectName')"
          prop="projectName"
          label="项目名称"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('projectCode')"
          prop="projectCode"
          label="项目编号"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('projectManager')"
          prop="projectManager"
          label="项目经理"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('marketingManager')"
          prop="marketingManager"
          label="市场经理"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('salesContractName')"
          prop="salesContractName"
          label="销售合同"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('customerName')"
          prop="customerName"
          label="客户"
          width="120" />
        <ElTableColumn
          prop="contractAmount"
          label="合同额"
          width="120" />
        <ElTableColumn
          prop="receivedCash"
          label="已回款现款"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('receivedUnexpiredTicket')"
          prop="receivedUnexpiredTicket"
          label="已收到未到期票证"
          width="140" />
        <ElTableColumn
          v-if="isColumnVisible('firstCashCollection')"
          prop="firstCashCollection"
          :label="`${monthTableData[0]}月回款现款`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('firstCollectionTicket')"
          prop="firstCollectionTicket"
          :label="`${monthTableData[0]}月回款票证`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('secondCashCollection')"
          prop="secondCashCollection"
          :label="`${monthTableData[1]}月回款现款`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('secondCollectionTicket')"
          prop="secondCollectionTicket"
          :label="`${monthTableData[1]}月回款票证`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('thirdCashCollection')"
          prop="thirdCashCollection"
          :label="`${monthTableData[2]}月回款现款`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('thirdCollectionTicket')"
          prop="thirdCollectionTicket"
          :label="`${monthTableData[2]}月回款票证`"
          width="120" />
        <!-- 采购合同 start -->
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.purchaseContractName')"
          prop="purchaseContract.purchaseContractName"
          label="采购合同"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.customerName')"
          prop="purchaseContract.customerName"
          label="供应商"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.contractAmount')"
          prop="purchaseContract.contractAmount"
          label="合同额"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.paidInCash')"
          prop="purchaseContract.paidInCash"
          label="已付款现款"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.issuedNotExpiredTicket')"
          prop="purchaseContract.issuedNotExpiredTicket"
          label="已开具未到期票证"
          width="140" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.firstExpensesCash')"
          prop="purchaseContract.firstExpensesCash"
          :label="`${monthTableData[0]}月支出现款`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.firstExpensesTicket')"
          prop="purchaseContract.firstExpensesTicket"
          :label="`${monthTableData[0]}月支出票证`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.secondExpensesCash')"
          prop="purchaseContract.secondExpensesCash"
          :label="`${monthTableData[1]}月支出现款`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.secondExpensesTicket')"
          prop="purchaseContract.secondExpensesTicket"
          :label="`${monthTableData[1]}月支出票证`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.thirdExpensesCash')"
          prop="purchaseContract.thirdExpensesCash"
          :label="`${monthTableData[2]}月支出现款`"
          width="120" />
        <ElTableColumn
          v-if="isColumnVisible('purchaseContract.thirdExpensesTicket')"
          prop="purchaseContract.thirdExpensesTicket"
          :label="`${monthTableData[2]}月支出票证`"
          width="120" />
      </ElTable>

      <PlanDetails
        :search-data="{
          planProjectList: detail.planProjectList,
          monthTableData,
        }" />
    </div>
  </DefaultContainer>
</template>

<script setup>
import { getFundPlanDetail, getFundPlanProjectList } from '@/api/financialManagement/monthlyFundingPlan.js'
import DefaultContainer from '@/components/DefaultContainer'
import { Back } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import Overview from './Overview.vue'
import PlanDetails from './PlanDetails.vue'

import TableHeaderSetting from './TableHeaderSetting.vue'

const props = defineProps({
  id: {
    type: String,
  },
})

const detail = ref({})

const router = useRouter()
const loading = ref(false)

const descriptionList1 = [
  { label: '填报月份', value: 'fillMonth' },
  { label: '计划起始月度', value: 'planStartMonth' },
  { label: '计划结束月度', value: 'planEndMonth' },
  { label: '填报人', value: 'applicationByName' },
  { label: '填报部门', value: 'fillDepartment' },
]

const monthTableData = computed(() => {
  const start = detail.value.fillMonth // 例如 '2025-11'
  if (!start)
    return []
  let [year, month] = start.split('-').map(Number)
  const arr = []
  for (let i = 1; i <= 3; i++) {
    month++
    if (month > 12) {
      year++
      month = 1
    }
    arr.push(`${year}-${String(month).padStart(2, '0')}`)
  }
  return arr
})

// 行合并的映射表，记录每个项目的合并信息
const spanMap = ref({})

// 表格行合并方法
function arraySpanMethod({ row, column, rowIndex }) {
  // 根据列的属性判断是否需要合并
  // 采购合同相关的列（以 purchaseContract. 开头的属性）不需要合并
  // 其他项目相关的列需要合并
  const shouldMerge = !column.property || !column.property.startsWith('purchaseContract.')

  if (shouldMerge) {
    const projectKey = row.projectCode || row.projectName || rowIndex // 使用项目编号或项目名称作为合并标识
    if (spanMap.value[projectKey]) {
      const spanInfo = spanMap.value[projectKey]
      if (rowIndex === spanInfo.startIndex) {
        return {
          rowspan: spanInfo.count,
          colspan: 1,
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        }
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

// 计算行合并信息
function calculateSpanMap(data) {
  const map = {}
  const countMap = {}

  data.forEach((item, index) => {
    const key = item.projectCode || item.projectName || index
    if (!countMap[key]) {
      countMap[key] = { count: 0, startIndex: index }
    }
    countMap[key].count++
  })

  Object.keys(countMap).forEach((key) => {
    map[key] = countMap[key]
  })

  spanMap.value = map
}

const search = ref('')
const filteredPlanProjectList = computed(() => {
  if (!search.value) {
    return detail.value.planProjectList || []
  }

  const searchLower = search.value.toLowerCase()

  return (detail.value.planProjectList || []).filter((item) => {
    // 检查所有字段是否包含搜索关键词
    return Object.entries(item).some(([key, value]) => {
      console.log(key, value)
      // 跳过null和undefined值
      if (value == null)
        return false

      // 处理对象类型的值（如purchaseContract）
      if (typeof value === 'object') {
        return Object.entries(value).some(([subKey, subValue]) => {
          console.log(subKey, subValue)
          if (subValue == null)
            return false
          return String(subValue).toLowerCase().includes(searchLower)
        })
      }

      // 处理基本类型的值
      return String(value).toLowerCase().includes(searchLower)
    })
  })
})

const id = router.currentRoute.value.query.id || props.id
onMounted(() => {
  getFundPlanDetailData()
})

async function getFundPlanDetailData() {
  loading.value = true
  const res = await getFundPlanDetail({ id })
  const res2 = await getFundPlanProjectList({ planId: id })
  if (res.code === 200) {
    detail.value = res.data
  }
  if (res2.code === 200) {
    const list = res2.data
    detail.value.planProjectList = getProjectList(list, false)
    // 计算行合并信息
    calculateSpanMap(detail.value.planProjectList)
  }
  loading.value = false
}

function getProjectList(list, isSubmit) {
  const newList = []
  if (!isSubmit) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.purchaseContractList.length > 0) {
        for (let j = 0; j < item.purchaseContractList.length; j++) {
          const obj = {
            ...item,
          }
          delete obj.purchaseContractList
          const purchaseContract = item.purchaseContractList[j]
          obj.purchaseContract = purchaseContract
          newList.push(obj)
        }
      } else {
        const obj = {
          ...item,
        }
        delete obj.purchaseContractList
        obj.purchaseContract = null
        newList.push(obj)
      }
    }
    return newList
  } else {
    // 按照项目 projectCode 合并传入list 并将采购合同合并进 purchaseContractList
    const projectMap = new Map()
    for (const item of list) {
      if (!projectMap.has(item.projectCode)) {
        const { purchaseContract, ...rest } = item
        projectMap.set(item.projectCode, {
          ...rest,
          purchaseContractList: [],
        })
      }
      if (item.purchaseContract) {
        projectMap.get(item.projectCode).purchaseContractList.push(item.purchaseContract)
      }
    }
    return Array.from(projectMap.values())
  }
}

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(detail))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})

// 只读/展示列配置
const columns = [
  { key: 'warnMessage', label: '预警消息' },
  { key: 'deliveryEntity', label: '交付主体' },
  { key: 'marketEntity', label: '市场主体' },
  { key: 'projectName', label: '项目名称' },
  { key: 'projectCode', label: '项目编号' },
  { key: 'projectManager', label: '项目经理' },
  { key: 'marketingManager', label: '市场经理' },
  { key: 'salesContractName', label: '销售合同' },
  { key: 'customerName', label: '客户' },
  { key: 'contractAmount', label: '合同额' },
  { key: 'receivedCash', label: '已回款现款' },
  { key: 'purchaseContract.purchaseContractName', label: '采购合同' },
  { key: 'purchaseContract.customerName', label: '供应商' },
  { key: 'purchaseContract.contractAmount', label: '采购合同合同额' },
  { key: 'purchaseContract.paidInCash', label: '已付款现款' },
]
// 只维护checkedColumns
const checkedColumns = ref(columns.map(col => col.key))
// 不可隐藏的输入列
const alwaysVisibleColumns = [
  'receivedUnexpiredTicket',
  'firstCashCollection',
  'firstCollectionTicket',
  'secondCashCollection',
  'secondCollectionTicket',
  'thirdCashCollection',
  'thirdCollectionTicket',
  'purchaseContract.issuedNotExpiredTicket',
  'purchaseContract.firstExpensesCash',
  'purchaseContract.firstExpensesTicket',
  'purchaseContract.secondExpensesCash',
  'purchaseContract.secondExpensesTicket',
  'purchaseContract.thirdExpensesCash',
  'purchaseContract.thirdExpensesTicket',
]
// 判断列是否显示
function isColumnVisible(key) {
  if (alwaysVisibleColumns.includes(key))
    return true
  return checkedColumns.value.includes(key)
}

/**
 * 操作方法
 * @param {string} type - 操作类型
 */

const { proxy } = getCurrentInstance()

function exportData() {
  const params = {
    // planProjectIds: [...new Set(filteredPlanProjectList.value.map(item => item.id))] || [],
    planProjectIds: [...new Set(filteredPlanProjectList.value.map(item => item.id))].join(',') || '',
  }
  proxy.download(
    'finance/fund_plan/download',
    {
      ...params,
    },
    `销项票台账_${new Date().getTime()}.xlsx`,
  )
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 8px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  position: relative;
  margin-top: 20px;

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .remark-attachment {
    &:hover {
      color: #409eff;
      cursor: pointer;
    }
  }
}

.file-text {
  cursor: pointer;

  &:hover {
    color: #409eff;
    cursor: pointer;
  }
}

.file-text-link {
  margin-bottom: 16px;
  color: #409eff;
  cursor: pointer;
}
</style>
