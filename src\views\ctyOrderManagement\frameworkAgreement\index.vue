<template>
  <DefaultContainer v-loading="isLoading">
    <!-- 折叠面板组件，用于查询条件 -->
    <Collapse>
      <template #header />
      <!-- 查询表单 -->
      <ElForm
        ref="formRef"
        :model="formData"
        label-width="120px"
        label-position="left">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElFormItem
              prop="protocolCode"
              label="协议编号">
              <ElInput
                v-model="formData.protocolCode"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="protocolName"
              label="协议名称">
              <ElInput
                v-model="formData.protocolName"
                clearable
                placeholder="请输入" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="protocolStartTime"
              label="协议起始日期">
              <ElDatePicker
                v-model="formData.protocolStartTime"
                clearable
                type="datetimerange"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem
              prop="protocolEndTime"
              label="协议终止日期">
              <ElDatePicker
                v-model="formData.protocolEndTime"
                clearable
                type="datetimerange"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </Collapse>

    <!-- 操作按钮区域 -->
    <div class="operation-area">
      <ElButton
        type="primary"
        @click="onSearch">
        查询
      </ElButton>
      <ElButton @click="onReset">
        重置
      </ElButton>
      <ElButton
        type="primary"
        @click="handle('add')">
        新建
      </ElButton>
    </div>
    <!-- 数据表格 -->
    <ElTable
      border
      :data="tableData"
      style="margin-top: 20px"
      @row-dblclick="onRowDbClick">
      <ElTableColumn
        type="index"
        :index="indexMethod"
        label="序号"
        width="60"
        fixed="left"
        align="center" />
      <ElTableColumn
        prop="protocolCode"
        label="协议编号"
        min-width="120" />
      <ElTableColumn
        prop="protocolName"
        label="协议名称"
        min-width="120" />
      <ElTableColumn
        prop="protocolStartTime"
        label="协议起始日期"
        min-width="120" />
      <ElTableColumn
        prop="protocolEndTime"
        label="协议终止日期"
        min-width="120" />
      <ElTableColumn
        prop="applicationTime"
        label="申请日期"
        min-width="140" />
      <ElTableColumn
        label="操作"
        width="110"
        fixed="right">
        <template #default="scope">
          <ElButton
            v-if="!scope.row.isItBound"
            type="primary"
            link
            @click="handle('edit', scope.row)">
            编辑
          </ElButton>
          <ElPopconfirm
            title="确定删除吗？"
            @confirm="handle('delete', scope.row)">
            <template #reference>
              <ElButton
                v-if="isCtCloudLeader && !scope.row.isItBound"
                v-hasPermi="['frameworkAgreement:delete']"
                type="primary"
                link>
                删除
              </ElButton>
            </template>
          </ElPopconfirm>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <ElPagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="pagination.total"
        @size-change="onSizeChange"
        @current-change="onSearch" />
    </div>
  </DefaultContainer>
</template>

<script setup>
import { deleteFrameworkPurchaseProtocol, getFrameworkPurchaseProtocolPageList } from '@/api/ctyOrderManagement/frameworkAgreement.js'
import { checkCtCloudLeader } from '@/api/ctyOrderManagement/public.js'
import Collapse from '@/components/Collapse/index.vue'
import DefaultContainer from '@/components/DefaultContainer/index.vue'
import { usePagination } from '@/utils/hooks.js'
import { ElMessage } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

/**
 * 分页相关
 * pagination: 分页参数对象
 * indexMethod: 表格序号计算方法
 */
const { pagination, indexMethod } = usePagination()

const router = useRouter()

/**
 * 表单数据与引用
 */
const formData = reactive({}) // 查询表单数据
const formRef = ref() // 表单引用，用于重置

/**
 * 表格数据与加载状态
 */
const tableData = ref([]) // 表格数据
const loadingIndex = ref(0) // 加载计数器，用于处理多个并发请求的loading状态

/**
 * 计算属性：是否显示加载状态
 * 当loadingIndex > 0时显示加载状态
 */
const isLoading = computed(() => {
  return loadingIndex.value !== 0
})

/**
 * 生命周期钩子：组件挂载完成后初始化
 */
onMounted(() => {
  init()
})

/**
 * 获取应用系统信息分页列表
 */
function getPage() {
  loadingIndex.value++ // 开始加载，计数器加1
  const searchData = {
    page: {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    },
    params: {},
  }
  searchData.params = {
    ...formData,
  }
  if (formData.protocolStartTime) {
    searchData.params.protocolStartTimeStart = formData.protocolStartTime[0]
    searchData.params.protocolStartTimeEnd = formData.protocolStartTime[1]
    delete searchData.params.protocolStartTime
  }
  if (formData.protocolEndTime) {
    searchData.params.protocolEndTimeStart = formData.protocolEndTime[0]
    searchData.params.protocolEndTimeEnd = formData.protocolEndTime[1]
    delete searchData.params.protocolEndTime
  }
  getFrameworkPurchaseProtocolPageList(searchData)
    .then((res) => {
      console.log(res, '---- getFrameworkPurchaseProtocolPageList')
      tableData.value = res.data.list
      pagination.total = res.data.total
      loadingIndex.value-- // 加载完成，计数器减1
    })
    .catch((err) => {
      console.log(err, '---- getFrameworkPurchaseProtocolPageList')
      loadingIndex.value-- // 加载出错，计数器减1
    })
}

/**
 * 初始化方法：组件挂载后加载应用系统列表数据
 */
const isCtCloudLeader = ref(false) // 是否楚天云负责人
function init() {
  checkCtCloudLeader({})
    .then((res) => {
      console.log(res, '---- checkCtCloudLeader')
      isCtCloudLeader.value = res.data
    })
    .catch((err) => {
      console.log(err, '---- checkCtCloudLeader')
    })
  getPage()
}

/**
 * 表格行双击事件处理
 * @param {object} row - 行数据
 */
function onRowDbClick(row) {
  router.push({
    path: '/ctyOrderManagement/frameworkAgreement/details',
    query: {
      id: row.id,
    },
  })
}

/**
 * 分页大小改变事件处理
 * @param {number} pageSize - 新的页面大小
 */
function onSizeChange(pageSize) {
  pagination.pageSize = pageSize
  getPage()
}

/**
 * 查询方法
 * 根据表单条件查询数据
 */
function onSearch() {
  getPage()
}

/**
 * 重置方法
 * 重置表单并查询第一页数据
 */
function onReset() {
  formRef.value.resetFields() // 重置表单字段
  pagination.pageNum = 1 // 重置到第一页
  onSearch() // 执行查询
}

/**
 * 操作方法
 * @param {string} type - 操作类型：add-新增应用系统，edit-编辑应用系统，delete-删除应用系统
 * @param {object} row - 行数据对象，包含应用系统信息
 */
function handle(type, row) {
  console.log(type, row, '---- handle')
  switch (type) {
    case 'add':
      console.log('新建应用系统')
      router.push({
        path: '/ctyOrderManagement/frameworkAgreement/form',
        query: {
          type: 'add',
        },
      })
      break
    case 'edit':
      console.log('编辑应用系统')
      router.push({
        path: '/ctyOrderManagement/frameworkAgreement/form',
        query: {
          id: row.id,
          type: 'edit',
        },
      })
      break
    case 'delete':
      console.log('删除应用系统')
      deleteFrameworkPurchaseProtocol({
        id: row.id,
      })
        .then((res) => {
          console.log(res, '---- deleteFrameworkPurchaseProtocol')
          if (res.code === 200) {
            ElMessage.success('删除成功')
            getPage()
          } else {
            ElMessage.warning(res.msg)
          }
        })
      break
    default:
      console.log('未知操作类型')
      break
  }
}
</script>

<style lang="scss" scoped>
/* 操作区域样式 */
.operation-area {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
