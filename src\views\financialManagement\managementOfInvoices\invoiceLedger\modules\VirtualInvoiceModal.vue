<template>
  <ElDialog
    v-model="dialogVisible"
    custom-class="virtual-invoice-modal"
    :title="title"
    :before-close="handleClose"
    width="1000px"
    :loading="loading">
    <ElForm
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px">
      <ElRow :gutter="8">
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="项目名称"
            prop="projectName">
            <ElSelect
              v-model="form.projectName"
              placeholder="请选择"
              @visible-change="
                (change) => change && (modelIsShow.selectProjectModal = true)
              " />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="项目编号"
            prop="projectCode">
            <ElInput
              v-model="form.projectCode"
              disabled
              placeholder="自动输入，不可修改" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="合同名称"
            prop="contractName">
            <ElSelect
              v-model="form.contractName"
              placeholder="请选择"
              :disabled="!form.projectCode"
              @visible-change="
                (change) => change && (modelIsShow.selectContractModal = true)
              " />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="合同编号"
            prop="contractNumber">
            <ElInput
              v-model="form.contractNumber"
              disabled
              placeholder="自动输入，不可修改" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="客户名称"
            prop="purchaser">
            <ElSelect
              v-model="form.purchaser"
              placeholder="请选择"
              @visible-change="
                (change) => change && (modelIsShow.selectMerchantModal = true)
              " />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="乙方(经营主体)"
            prop="seller">
            <SelectBusinessEntity v-model="form.seller" />
          </ElFormItem>
        </ElCol>
        <div class="sub-title">
          开票信息
        </div>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="发票类型"
            prop="invoiceType">
            <ElSelect
              v-model="form.invoiceType"
              placeholder="请选择">
              <ElOption
                value="1"
                label="增值税电子普通发票" />
              <ElOption
                value="2"
                label="增值税电子专用发票" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <!-- <el-col :span="12">

            <ElFormItem class="form-item" label="发票号码" prop="invoiceNumber">
                <ElInput v-model="form.invoiceNumber" placeholder="请输入" />
            </ElFormItem>
        </el-col> -->
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="发票金额(含税:元)"
            prop="invoiceAmountIncludeTax">
            <ElInputNumber
              v-model="form.invoiceAmountIncludeTax"
              placeholder="请输入"
              :min="0"
              :step="0.01"
              :precision="2"
              controls-position="right"
              style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="税率"
            prop="taxRate">
            <ElSelect
              v-model="form.taxRate"
              placeholder="请选择">
              <ElOption
                label="3%"
                :value="3" />
              <ElOption
                label="0%"
                :value="0" />
              <ElOption
                label="11%"
                :value="11" />
              <ElOption
                label="9%"
                :value="9" />
              <ElOption
                label="13%"
                :value="13" />
              <ElOption
                label="10%"
                :value="10" />
              <ElOption
                label="17%"
                :value="17" />
              <ElOption
                label="6%"
                :value="6" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="发票金额(不含税:元)"
            prop="invoiceAmount">
            <ElInput
              v-model="form.invoiceAmount"
              disabled
              placeholder="自动计算" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            class="form-item"
            label="税额"
            prop="taxAmount">
            <ElInput
              v-model="form.taxAmount"
              disabled
              placeholder="自动计算" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            class="form-item"
            label="备注"
            prop="remark">
            <ElInput
              v-model="form.remark"
              type="textarea"
              rows="3"
              placeholder="请输入" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton @click="handleClose">
        取消
      </ElButton>
      <ElButton
        type="primary"
        @click="submit">
        提交
      </ElButton>
    </template>
    <SelectProjectModal
      v-model="modelIsShow.selectProjectModal"
      @select-item="onChangeProject" />
    <SelectContractModal
      v-model="modelIsShow.selectContractModal"
      :project-code="searchProjectCode"
      @select-item="onChangeContract" />
    <SelectMerchantModal
      v-model="modelIsShow.selectMerchantModal"
      :fill-status="3"
      @select-item="onChangeMerchantInvoice" />
  </ElDialog>
</template>

<script setup>
import { createOrUpdateVirtualInvoice, getInvoiceLedgerDetail } from '@/api/financialManagement/managementOfInvoices/invoiceLedger'
import SelectBusinessEntity from '@/views/financialManagement/modules/SelectBusinessEntity.vue'
import SelectContractModal from '@/views/financialManagement/modules/SelectContractModal.vue'
import SelectMerchantModal from '@/views/financialManagement/modules/SelectMerchantModal.vue'
import SelectProjectModal from '@/views/financialManagement/modules/SelectProjectModal.vue'
import { ElMessage } from 'element-plus'
import { computed, defineEmits, ref, watch } from 'vue'

const emit = defineEmits(['ok'])

const loading = ref(false)
const dialogVisible = ref(false)
const title = ref('新增虚拟发票')
const form = ref({
  projectName: '',
  projectCode: '',
  contractName: '',
  contractNumber: '',
  purchaser: '',
  seller: '',
  invoiceType: '',
  invoiceNumber: '',
  invoiceAmountIncludeTax: '',
  taxRate: '',
  invoiceAmount: '',
  taxAmount: '',
  remark: '',
  isVirtualInvoice: 1,
  isOffset: 0,
})
const formRef = ref()
const rules = ref({
  projectName: [{ required: true, message: '请选择项目名称', trigger: 'change' }],
  projectCode: [{ required: true, message: '请选择项目编号', trigger: 'change' }],
  contractName: [{ required: true, message: '请选择合同名称', trigger: 'change' }],
  contractNumber: [{ required: true, message: '请选择合同编号', trigger: 'change' }],
  purchaser: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
  seller: [{ required: true, message: '请选择乙方(经营主体)', trigger: 'change' }],
  invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
  invoiceAmountIncludeTax: [{ required: true, message: '请输入发票金额(含税:元)', trigger: 'change' }],
  taxRate: [{ required: true, message: '请选择税率', trigger: 'change' }],
})

const modelIsShow = ref({
  selectProjectModal: false,
  selectContractModal: false,
  selectMerchantModal: false,
})

const searchProjectCode = computed(() => {
  return form.value.projectCode ? form.value.projectCode.split('-D')[0] : ''
})

function handleClose() {
  formRef.value?.resetFields()
  form.value = {
    projectName: '',
    projectCode: '',
    contractName: '',
    contractNumber: '',
    purchaser: '',
    seller: '',
    invoiceType: '',
    invoiceNumber: '',
    invoiceAmountIncludeTax: '',
    taxRate: '',
    invoiceAmount: '',
    taxAmount: '',
    remark: '',
    isVirtualInvoice: 1,
    isOffset: 0,
  }
  dialogVisible.value = false
}

function add() {
  title.value = '新增虚拟发票'
  dialogVisible.value = true
}

async function edit(row) {
  if (!row?.id) {
    ElMessage.warning('缺少必要参数')
    return
  }

  title.value = '编辑虚拟发票'
  loading.value = true
  dialogVisible.value = true

  try {
    const res = await getInvoiceLedgerDetail({ id: row.id })
    if (res.code === 200) {
      form.value = {
        ...res.data,
        isVirtualInvoice: 1,
        isOffset: 0,
      }
    } else {
      ElMessage.warning(res.message || '获取详情失败')
      dialogVisible.value = false
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
    dialogVisible.value = false
  } finally {
    loading.value = false
  }
}

async function submit() {
  if (loading.value)
    return

  try {
    const valid = await formRef.value.validate()
    if (!valid)
      return

    loading.value = true
    const res = await createOrUpdateVirtualInvoice(form.value)
    if (res.code === 200) {
      ElMessage.success('操作成功')
      emit('ok')
      handleClose()
    } else {
      ElMessage.warning(res.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

function onChangeProject(project) {
  if (project) {
    form.value.projectCode = project.projectCode
    form.value.projectName = project.projectName || project.bolProjectName
    form.value.projectMainCode = project.projectNumber
    onChangeContract()
  } else {
    form.value.projectCode = ''
    form.value.projectName = ''
    onChangeContract()
  }
}

function onChangeMerchantInvoice(businessEntity) {
  if (businessEntity) {
    form.value.purchaser = businessEntity.name
    form.value.purchaserCode = businessEntity.code
  }
}

function onChangeContract(contract) {
  if (contract) {
    form.value.contractName = contract.name
    form.value.contractNumber = contract.code
  } else {
    form.value.contractName = ''
    form.value.contractNumber = ''
  }
}

watch(
  [() => form.value.taxRate, () => form.value.invoiceAmountIncludeTax],
  ([newTaxRate, newAmount]) => {
    if (newTaxRate && newAmount) {
      const taxRate = Number(newTaxRate) / 100
      form.value.invoiceAmount = Number((newAmount / (1 + taxRate)).toFixed(2))
      form.value.taxAmount = Number((newAmount - form.value.invoiceAmount).toFixed(2))
    } else {
      form.value.invoiceAmount = ''
      form.value.taxAmount = ''
    }
  },
)

defineExpose({
  add,
  edit,
})
</script>

<style lang="scss" scoped>
.sub-title {
  width: 100%;
  margin-top: 15px;
  margin-bottom: 20px;
  padding-left: 70px;
  color: rgb(0 0 0 / 88%);
  font-weight: 600;
  font-size: 18px;
}
</style>
