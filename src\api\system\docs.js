import request from '@/utils/request'
// 分页接口
export function getDocsList(data) {
  return request({
    url: '/system/doc/pageList',
    method: 'post',
    data,
  })
}

export function getDocsSelectList() {
  return request({
    url: '/system/doc/allList',
    method: 'post'
  })
}

export function getDocsTemplateDetail(bid) {
  return request({
    url: '/system/doc/details/' + bid,
    method: 'post',
  })
}

export function saveDocs(data) {
  return request({
    url: '/system/doc/create',
    method: 'post',
    data,
  })
}

export function updateDocs(bid, data) {
  return request({
    url: '/system/doc/update/' + bid,
    method: 'post',
    data,
  })
}

export function removeDocs(ids) {
  return request({
    url: '/system/doc/removeBatch',
    method: 'post',
    data: {
      bidList: ids
    },
  })
}

export function getDocsAllList() {
  return request({
    url: '/project/doc/allList',
    method: 'post',
  })
}

export function getElectronicDocsPageList(data) {
  return request({
    url: `/project/doc/electronic/pageList`,
    method: 'post',
    data,
  })
}

export function getPaperDocsPageList(data) {
  return request({
    url: `/project/doc/paper/pageList`,
    method: 'post',
    data,
  })
}

// 项目管理文档分页接口
export function getProjectDocsList(data) {
  return request({
    url: '/project/doc/pageList',
    method: 'post',
    data,
  })
}

// 项目管理文档创建接口
export function createProjectDocs(data) {
  return request({
    url: '/project/doc/create',
    method: 'post',
    data,
  })
}

export function getDocsDetail(id) {
  return request({
    url: '/project/doc/details/' + id,
    method: 'post',
  })
}

/**
 * 删除项目管理文档
 * @param {*} ids
 * @returns
 */
export function removeProjectDocs(ids) {
  return request({
    url: `/project/doc/removeBatch`,
    method: 'post',
    data: { idList: ids },
  })
}

// 项目管理文档修改接口
export function updateProjectDocs(id, data) {
  return request({
    url: '/project/doc/update/' + id,
    method: 'post',
    data,
  })
}

// 项目管理文档提交接口
export function submitProjectDocs(data) {
  return request({
    url: '/project/doc/submit',
    method: 'post',
    data,
  })
}