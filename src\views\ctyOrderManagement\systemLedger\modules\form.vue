<template>
  <DefaultContainer v-loading="loading">
    <div class="header">
      <div class="left">
        <div
          class="back-icon"
          @click="router.back()">
          <ElIcon>
            <Back />
          </ElIcon>
        </div>
        <div class="title">
          系统台账
        </div>
      </div>
      <div
        v-if="!props.id"
        class="right">
        <ElButton @click="router.back()">
          取消
        </ElButton>
        <ElButton
          :loading="saveLoading"
          type="primary"
          @click="onSubmit()">
          提交
        </ElButton>
      </div>
    </div>
    <div class="content">
      <ElForm
        ref="formRef"
        :inline="true"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="180px"
        :disabled="props.id">
        <ElDivider />
        <!-- <div class="sub-title">
          明细信息
        </div> -->
        <ElFormItem
          class="form-item"
          label="应用系统名称(楚天云)"
          prop="appName">
          <ElInput
            v-model="form.appName"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="客户名称"
          prop="customerCode">
          <ElSelect
            v-model="form.customerName"
            placeholder="请选择"
            @click="customerDialogRef.customerDialogVisible = true" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="应用系统名称(数产)"
          prop="gzAppName">
          <ElInput
            v-model="form.gzAppName"
            placeholder="请输入" />
        </ElFormItem>
        <ElFormItem
          class="form-item"
          label="申请日期"
          prop="createTime">
          <ElInput
            v-model="form.createTime"
            disabled
            placeholder="请输入" />
        </ElFormItem>
      </ElForm>
    </div>
    <CustomerDialog
      ref="customerDialogRef"
      @ok="onChangeMerchantInvoice" />
  </DefaultContainer>
</template>

<script setup>
import {
  createOrUpdateCtCloudAppSystem,
  getCtCloudAppSystemDetail,
} from '@/api/ctyOrderManagement/systemLedger.js'

import DefaultContainer from '@/components/DefaultContainer/index.vue'
import CustomerDialog from '@/components/dialog/customer-dialog.vue'
import { deepClone } from '@/utils'
import { Back } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
  },
  instanceId: {
    type: String,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  forms: {
    type: Object,
    default: () => ({}),
  },
})

const router = useRouter()
const formRef = ref(null)
const form = ref({
  createTime: new Date().toISOString().split('T')[0],
})

const customerDialogRef = ref(null)

const rules = ref({
  appName: [
    { required: true, message: '请输入应用系统名称', trigger: 'blur' },
  ],
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
  ],
  gzAppName: [
    { required: true, message: '请输入应用系统名称(数产)', trigger: 'blur' },
  ],
})

const id = ref('')
const loading = ref(false)
const saveLoading = ref(false)

onMounted(() => {
  if (props.id || router.currentRoute.value.query.id) {
    id.value = props.id || router.currentRoute.value.query.id
    getFormData()
  }
})

async function getFormData() {
  loading.value = true
  try {
    const res = await getCtCloudAppSystemDetail({
      id: id.value,
    })
    console.log(res)
    form.value = deepClone(res.data)
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

function onChangeMerchantInvoice(businessEntity) {
  console.log(businessEntity, '--- businessEntity')
  form.value.customerName = businessEntity.name
  form.value.customerCode = businessEntity.code
}

function onSubmit() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const data = deepClone(form.value)

      console.log(data, '--- data')
      saveLoading.value = true
      try {
        const res = await createOrUpdateCtCloudAppSystem(data)
        if (res.code === 200) {
          ElMessage.success('提交成功')
          router.back()
        }
      } catch (error) {
        console.log(error)
      } finally {
        saveLoading.value = false
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
    font-size: 20px;

    .back-icon {
      margin-top: 6px;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
  min-height: 200px !important; /* 设置最小高度 */

  .sub-title {
    width: 100%;
    margin-bottom: 20px;
    color: rgb(0 0 0 / 88%);
    font-weight: 600;
    font-size: 20px;
  }

  .plan-item {
    width: 100%;

    .form-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-top: 10px;
    margin-bottom: 40px;
    border: 1px dashed #1677ff;
    border-radius: 6px;
    color: #1677ff;
    font-size: 14px;
    cursor: pointer;
  }

  .form-item {
    width: 360px;
  }

  .pagination {
    display: flex;
    justify-content: end;
    margin-top: 12px;
  }
}

.attachment_wrapper {
  width: 95%;
  border: 1px var(--el-border-color) var(--el-border-style);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
}
</style>

<style lang="scss">
.custom_upload {
  margin: 10px 0;

  .el-upload.el-upload--text {
    pointer-events: none;
  }

  ul {
    min-height: 100px;
    margin-top: 16px;
    padding-top: 4px;
    border-top: 1px var(--el-border-color) var(--el-border-style);
  }
}
</style>
