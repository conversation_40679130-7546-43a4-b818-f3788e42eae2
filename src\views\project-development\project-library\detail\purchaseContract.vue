<template>
  <TableContainer title="项目关联采购合同列表">
    <template #toolbar>
      <div>
        <ElButton
          @click="exportHandle">
          导出
        </ElButton>
      </div>
    </template>
    <template #default="{ contentHeight }">
      <ElTable
        v-loading="loading"
        border
        :data="data"
        row-key="bid"
        :max-height="contentHeight"
        @selection-change="handleSelect">
        <ElTableColumn
          type="selection"
          reserve-selection
          width="55" />
        <ElTableColumn
          prop="merchantType"
          label="合同名称" />
        <ElTableColumn
          prop="purchaseProjectName"
          label="合同编号" />
        <ElTableColumn
          prop="supplierCode"
          label="签署时间" />
        <ElTableColumn
          prop="applicantDate"
          label="合同金额(元)" />
        <ElTableColumn
          prop="applicantDate"
          label="是否框架合同" />
        <ElTableColumn
          prop="applicantDate"
          label="父子关系" />
        <ElTableColumn
          prop="applicantDate"
          label="经办人" />
        <ElTableColumn
          prop="applicantDate"
          label="经办部门" />
        <ElTableColumn
          prop="applicantDate"
          label="合同状态" />
        <ElTableColumn
          prop="applicantDate"
          label="合同标签" />
        <ElTableColumn
          prop="applicantDate"
          label="我方主体名称" />
        <ElTableColumn
          prop="applicantDate"
          label="我方主体身份" />
        <ElTableColumn
          prop="applicantDate"
          label="对方主体名称" />
      </ElTable>
    </template>
    <template #footer>
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="Number(total)" />
    </template>
  </TableContainer>
</template>

<script setup>
import { getProjectLibraryPurchaseContractList } from '@/api/project-development/project-library.js'
import TableContainer from '@/components/Container/table-container.vue'
import { usePagination } from 'alova/client'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

const { loading, data, pageSize, page, total } = usePagination((pageNum, pageSize) => getProjectLibraryPurchaseContractList({
  page: {
    orders: [
      {
        asc: true,
        field: '',
      },
    ],
    pageNum,
    pageSize,
  },
  params: {
    attnName: '',
    code: '',
    contractAuditStatus: '',
    contractLabelNames: '',
    contractNature: '',
    contractStatus: '',
    endAmount: '',
    frameworkContract: '',
    handlingDepartmentName: '',
    merchantName: '',
    name: '',
    ourIdentity: '',
    ourName: '',
    parentTelationship: '',
    projectCode: '',
    projectName: '',
    purchaseContractType: 'purchase',
    signingEndTime: '',
    signingStartTime: '',
    startAmount: '',
  },
}), {
  total: res => res.total,
  data: res => res.list,
  initialPage: 1, // 初始页码，默认为1
  initialPageSize: 10, // 初始每页数据条数，默认为10
})

const ids = ref([])
function handleSelect(selection) {
  ids.value = selection.map(item => item.bid)
}

async function exportHandle() {
  if (ids.value.length <= 0) {
    ElMessage.warning('请至少选择一条数据')
  } else {
    proxy.download('/project/prd/project/library/detail/contract/export', { bids: ids.value, isAll: 0, params: {} }, `研发项目项目库采购合同_${new Date().getTime()}.xlsx`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
