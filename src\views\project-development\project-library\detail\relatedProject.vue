<template>
  <TableContainer title="产出产品关联交付项目列表">
    <template #toolbar>
      <div>
        <ElButton
          @click="exportHandle">
          导出
        </ElButton>
      </div>
    </template>
    <template #default="{ contentHeight }">
      <ElTable
        v-loading="loading"
        border
        :data="data"
        row-key="bid"
        :max-height="contentHeight"
        @selection-change="handleSelect">
        <ElTableColumn
          type="selection"
          reserve-selection
          width="55" />
        <ElTableColumn
          prop="status"
          label="阶段状态">
          <template #default="{ row }">
            <div class="status-row">
              <div :class="`status-icon status-icon-${row.status}`" />
              <div>
                {{ getStatusLabel(row.status) }}
              </div>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="bolProjectName"
          label="项目名称" />
        <ElTableColumn
          prop="bolProjectCode"
          label="项目编号" />
        <ElTableColumn
          prop="applicantDate"
          label="关联自有产品" />
        <ElTableColumn
          prop="projectManager"
          label="项目经理" />
        <ElTableColumn
          prop="deliveryEntity"
          label="项目归属部门" />
        <ElTableColumn
          prop="applicationDate"
          label="立项日期" />
      </ElTable>
    </template>
    <template #footer>
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total,prev,pager,next,sizes,jumper"
        :total="Number(total)" />
    </template>
  </TableContainer>
</template>

<script setup>
import { getProjectLibraryList } from '@/api/project-development/project-library.js'
import TableContainer from '@/components/Container/table-container.vue'
import { usePagination } from 'alova/client'
import { ElMessage } from 'element-plus'

const props = defineProps({
  projectCode: {
    default: '',
  },
})

const { proxy } = getCurrentInstance()

const { project_repo_stage_status } = proxy.useDict('project_repo_stage_status')

const { loading, data, pageSize, page, total } = usePagination((pageNum, pageSize) => getProjectLibraryList({
  page: {
    pageNum,
    pageSize,
  },
  params: {
    projectCode: props.projectCode,
  },
}), {
  total: res => res.total,
  data: res => res.list,
  initialPage: 1, // 初始页码，默认为1
  initialPageSize: 10, // 初始每页数据条数，默认为10
})

const ids = ref([])
function handleSelect(selection) {
  ids.value = selection.map(item => item.bid)
}
function exportHandle() {
  if (ids.value.length <= 0) {
    ElMessage.warning('请至少选择一条数据')
  }
}

// 根据状态值获取状态标签名称
function getStatusLabel(status) {
  return (
    project_repo_stage_status.value.find(
      item => Number(item.value) === Number(status),
    )?.label || ''
  )
}
</script>

<style lang="scss" scoped>
.status-row {
  display: flex;
  align-items: center;

  .status-icon {
    width: 6px;
    height: 6px;
    margin-right: 6px;
    border-radius: 50%;
  }

  .status-icon-0 {
    background: #c1bfbf;
  }

  .status-icon-1 {
    background: #faad14;
  }

  .status-icon-2 {
    background: #1677ff;
  }

  .status-icon-3 {
    background: #52c41a;
  }

  .status-icon-4 {
    background: #ff7875;
  }

  .status-icon-5 {
    background: #722ed1;
  }

  .status-icon-6 {
    background: rgb(0 0 0 / 25%);
  }

  .status-icon-7 {
    background: #eb2f96;
  }
}
</style>
