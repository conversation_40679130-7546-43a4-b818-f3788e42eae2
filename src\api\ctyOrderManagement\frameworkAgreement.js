import request from '@/utils/request'

// 新增、编辑 框架采购协议
export function createOrUpdateFrameworkPurchaseProtocol(data) {
  return request({
    url: '/cloudorder/ct-cloud-framework-purchase-protocol/createOrUpdate',
    method: 'post',
    data,
  })
}

// 删除框架采购协议
export function deleteFrameworkPurchaseProtocol(params) {
  return request({
    url: '/cloudorder/ct-cloud-framework-purchase-protocol/del',
    method: 'get',
    params,
  })
}

// 获取框架采购协议详情
export function getFrameworkPurchaseProtocolDetail(params) {
  return request({
    url: '/cloudorder/ct-cloud-framework-purchase-protocol/detail',
    method: 'get',
    params,
  })
}

// 框架采购协议分页列表
export function getFrameworkPurchaseProtocolPageList(data) {
  return request({
    url: '/cloudorder/ct-cloud-framework-purchase-protocol/pageList',
    method: 'post',
    data,
  })
}
