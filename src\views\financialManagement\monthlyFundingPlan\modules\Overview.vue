<template>
  <ElTable
    :data="overviewData"
    border
    style="width: 95%; margin-bottom: 32px;"
    :span-method="overviewSpanMethod">
    <ElTableColumn
      prop="category"
      label="指标"
      width="120"
      align="center" />
    <ElTableColumn
      prop="type"
      label=""
      width="60"
      align="center" />
    <ElTableColumn
      v-for="month in months"
      :key="month"
      :prop="month"
      :label="month"
      align="center" />
  </ElTable>
</template>

<script setup>
const props = defineProps({
  month: {
    type: String,
    default: '',
  },
})
// 动态生成月份表头
const months = computed(() => {
  const start = props.month // 例如 '2025-11'
  if (!start)
    return []
  let [year, month] = start.split('-').map(Number)
  const arr = []
  for (let i = 1; i <= 3; i++) {
    month++
    if (month > 12) {
      year++
      month = 1
    }
    arr.push(`${year}-${String(month).padStart(2, '0')}`)
  }
  return arr
})

// 概览表格数据和表头
const overviewData = [
  { 'category': '合同累计', 'type': '收', '2025-06': 20000, '2025-07': 20000, '2025-08': 20000 },
  { 'category': '合同累计', 'type': '支', '2025-06': 10000, '2025-07': 10000, '2025-08': 10000 },
  { 'category': '填报累计', 'type': '收', '2025-06': 20000, '2025-07': 20000, '2025-08': 20000 },
  { 'category': '填报累计', 'type': '支', '2025-06': 10000, '2025-07': 10000, '2025-08': 10000 },
]
// 合并单元格方法
function overviewSpanMethod({ rowIndex, columnIndex }) {
  // 合并"指标"列
  if (columnIndex === 0) {
    if (rowIndex % 2 === 0) {
      return { rowspan: 2, colspan: 1 }
    } else {
      return { rowspan: 0, colspan: 0 }
    }
  }
  return { rowspan: 1, colspan: 1 }
}
</script>
