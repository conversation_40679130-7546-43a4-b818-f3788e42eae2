<template>
  <ElDialog
    v-model="visible"
    :title="`产品及解决方案附件${{
      add: '新增',
      edit: '编辑',
    }[dialogType]}`"
    width="804px"
    @open="handleOpen"
    @close="handleClose">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      label-position="top">
      <ElRow :gutter="24">
        <ElCol :span="12">
          <ElFormItem
            prop="solutionName"
            label="产品及解决方案名称：">
            <ElInput
              v-model="formData.solutionName"
              placeholder="请输入产品及解决方案名称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="companyName"
            label="所属生态伙伴：">
            <ElInput
              v-model="formData.companyName"
              placeholder="请输入所属生态伙伴" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="partnerClass"
            label="伙伴分类：">
            <ElCascader
              v-model="formData.partnerClass"
              style="width: 100%;"
              placeholder="请选择伙伴分类"
              :options="partnerOptions"
              :props="{
                label: 'value',
              }" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="deployment"
            label="部署方式：">
            <ElInput
              v-model="formData.deployment"
              placeholder="请输入部署方式" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="isDomesticallyAdapted"
            label="是否国产适配：">
            <ElSelect
              v-model="formData.isDomesticallyAdapted"
              placeholder="请选择是否国产适配">
              <ElOption
                label="是"
                :value="1" />
              <ElOption
                label="否"
                :value="0" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="businessModel"
            label="商务模式：">
            <ElInput
              v-model="formData.businessModel"
              placeholder="请输入商务模式" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="伙伴标签"
            prop="partnerTags">
            <div class="flex gap-[8px] flex-wrap">
              <ElTag
                v-for="tag in formData.partnerTags"
                :key="tag"
                closable
                :disable-transitions="false"
                @close="handleTagClose(tag)">
                {{ tag }}
              </ElTag>
              <ElInput
                v-if="tagInputVisible"
                ref="tagInputRef"
                v-model="tagInputValue"
                size="small"
                @keyup.enter="handleTagInputConfirm"
                @blur="handleTagInputConfirm" />
              <ElButton
                v-else
                size="small"
                @click="showTagInput">
                + New Tag
              </ElButton>
            </div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="单位"
            prop="unit">
            <ElSelect v-model="formData.unit">
              <ElOption
                v-for="item in unitOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="productPackage"
            label="标准产品包：">
            <ElInput
              v-model="formData.productPackage"
              placeholder="请输入标准产品包"
              :suffix-icon="
                () => {
                  return '元'
                }
              " />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="productImplementation"
            label="产品实施：">
            <ElInput
              v-model="formData.productImplementation"
              placeholder="请输入产品实施"
              :suffix-icon="
                () => {
                  return '元'
                }
              " />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="isProductSoft"
            label="产品是否软著：">
            <ElSelect
              v-model="formData.isProductSoft"
              placeholder="请选择">
              <ElOption
                label="是"
                :value="1" />
              <ElOption
                label="否"
                :value="0" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            prop="productDevelopment"
            label="产品定制化开发：">
            <ElInput
              v-model="formData.productDevelopment"
              placeholder="请输入产品定制化开发"
              :suffix-icon="
                () => {
                  return '元'
                }
              " />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem
            prop="fileUrl"
            label="附件：">
            <FileUpload
              v-model="formData.fileUrl"
              :file-size="20"
              :file-type="['ppt', 'pptx']"
              :limit="1"
              :is-show-tip="false"
              class="w-full">
              <ElButton>
                <ElIcon class="mr-[10px]">
                  <i class="iconfont icon-UploadOutlined" />
                </ElIcon>
                上传文件
              </ElButton>
            </FileUpload>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">
          取消
        </ElButton>
        <ElButton
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm">
          确认
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup>
import { createTempProduct, editTempProduct } from '@/api/ecology/temp-product.js'
import FileUpload from '@/components/FileUpload/index.vue'

const { type: dialogType, rowData, partnerOptions } = defineProps({
  type: {
    type: String,
    default: 'add',
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
  partnerOptions: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['confirm'])

const visible = defineModel('visible', {
  type: Boolean,
  default: false,
})

const { proxy } = getCurrentInstance()

const formRef = useTemplateRef('formRef')
const formData = reactive({
  solutionName: '', // 产品及解决方案名称
  companyName: '', // 所属生态伙伴
  partnerClass: [], // 伙伴分类
  deployment: '', // 部署方式
  isDomesticallyAdapted: '', // 是否国产适配
  businessModel: '', // 商务模式
  partnerTags: [], // 伙伴标签
  unit: [], // 单位
  productPackage: '', // 标准产品包
  productImplementation: '', // 产品实施
  isProductSoft: '', // 产品是否软著
  productDevelopment: '', // 产品定制化开发
  fileUrl: '', // 附件
})
const formRules = reactive({
  solutionName: [
    { required: true, message: '产品及解决方案名称不能为空', trigger: ['change', 'blur'] },
  ],
  companyName: [
    { required: true, message: '所属生态伙伴不能为空', trigger: ['change', 'blur'] },
  ],
  partnerClass: [
    { required: true, message: '伙伴分类不能为空', trigger: ['change', 'blur'] },
  ],
  deployment: [
    { required: true, message: '部署方式不能为空', trigger: ['change', 'blur'] },
  ],
  isDomesticallyAdapted: [
    { type: 'number', required: true, message: '是否国产适配不能为空', trigger: ['change', 'blur'] },
  ],
  businessModel: [
    { required: true, message: '商务模式不能为空', trigger: ['change', 'blur'] },
  ],
  partnerTags: [
    { required: true, message: '伙伴标签不能为空', trigger: ['change', 'blur'] },
  ],
  unit: [
    { required: true, message: '单位不能为空', trigger: ['change', 'blur'] },
  ],
  productPackage: [
    { required: true, message: '标准产品包不能为空', trigger: ['change', 'blur'] },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '标准产品包格式不正确', trigger: ['change', 'blur'] },
  ],
  productImplementation: [
    { required: true, message: '产品实施不能为空', trigger: ['change', 'blur'] },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '产品实施格式不正确', trigger: ['change', 'blur'] },
  ],
  isProductSoft: [
    { type: 'number', required: true, message: '产品是否软著不能为空', trigger: ['change', 'blur'] },
  ],
  productDevelopment: [
    { required: true, message: '产品定制化开发不能为空', trigger: ['change', 'blur'] },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '产品定制化开发格式不正确', trigger: ['change', 'blur'] },
  ],
  fileUrl: [
    { required: true, message: '附件不能为空', trigger: ['change', 'blur'] },
  ],
})

function handleOpen() {
  formRef.value.resetFields()

  if (dialogType === 'edit') {
    for (const key in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        if (!rowData[key]) {
          continue
        }

        if (key === 'partnerClass') {
          formData.partnerClass = typeof rowData.partnerClass === 'string' ? JSON.parse(rowData.partnerClass) : rowData.partnerClass
        } else if (key === 'partnerTags') {
          formData.partnerTags = typeof rowData.partnerTags === 'string' ? JSON.parse(rowData.partnerTags) : rowData.partnerTags
        } else if (['productPackage', 'productImplementation', 'productDevelopment'].includes(key)) {
          // 转成两位小数
          formData[key] = Number(rowData[key]).toFixed(2)
        } else {
          formData[key] = rowData[key]
        }
      }
    }
  }
}

function handleClose() {
  formRef.value.resetFields()
  visible.value = false
}

const tagInputValue = ref('')
const tagInputVisible = ref(false)
const tagInputRef = useTemplateRef('tagInputRef')

function handleTagClose(tag) {
  formData.partnerTags.splice(formData.partnerTags.indexOf(tag), 1)
}

function showTagInput() {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.input?.focus()
  })
}

function handleTagInputConfirm() {
  if (tagInputValue.value) {
    formData.partnerTags.push(tagInputValue.value)
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

const unitOptions = ref([
  { label: '台', value: '台' },
  { label: '件', value: '件' },
  { label: '套', value: '套' },
  { label: '月', value: '月' },
  { label: '项', value: '项' },
  { label: '张', value: '张' },
  { label: '年', value: '年' },
  { label: '人', value: '人' },
  { label: '天', value: '天' },
  { label: '个', value: '个' },
])

function handleCancel() {
  handleClose()
}

const confirmLoading = ref(false)
function handleConfirm() {
  formRef.value.validate((valid, fields) => {
    if (valid) {
      confirmLoading.value = true
      if (dialogType === 'add') {
        createTempProduct({
          ...formData,
          partnerClass: JSON.stringify(formData.partnerClass),
          partnerTags: JSON.stringify(formData.partnerTags),
          fileName: formData.fileUrl.slice(formData.fileUrl.lastIndexOf('/') + 1),
        }).then(() => {
          proxy.$modal.msgSuccess('新增成功')
          emit('confirm', formData)
          handleClose()
        }).finally(() => {
          confirmLoading.value = false
        })
      }

      if (dialogType === 'edit') {
        editTempProduct({
          ...formData,
          productBid: rowData.productBid,
          partnerClass: JSON.stringify(formData.partnerClass),
          partnerTags: JSON.stringify(formData.partnerTags),
          fileName: formData.fileUrl.slice(formData.fileUrl.lastIndexOf('/') + 1),
        }).then(() => {
          proxy.$modal.msgSuccess('编辑成功')
          emit('confirm', formData)
          handleClose()
        }).finally(() => {
          confirmLoading.value = false
        })
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style lang="scss" scoped>

</style>
