<template>
  <Container
    show-back
    :back-title="backTitle()">
    <template #headerRight>
      <div
        v-if="route.query.pageType !== 'view'"
        class="ml-auto">
        <ElButton
          type="info"
          @click="handleDraft">
          暂存
        </ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit">
          提交
        </ElButton>
      </div>
    </template>
    <FinfishForm
      :id="route.query.id"
      ref="FinfishFormRef"
      :form-type="route.query?.pageType" />
  </Container>
</template>

<script setup>
import Container from '@/components/Container/index.vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import FinfishForm from '../components/finfish-form.vue'

const route = useRoute()
const router = useRouter()
const FinfishFormRef = useTemplateRef('FinfishFormRef')

function backTitle() {
  const pageType = route.query?.pageType
  console.log(pageType, 'pageType')
  const titleMap = {
    form: '新增项目结项',
    edit: '编辑项目结项',
    view: '查看项目结项',
  }
  return titleMap[pageType] || '项目结项'
}
async function handleSubmit() {
  try {
    FinfishFormRef.value.onSave().then(() => {
      ElMessage.success('保存成功')
      router.back()
    }).catch((err) => {
      console.log(err)
    })
  } catch (error) {
    console.log(error)
  }
}
function handleDraft() {
  try {
    FinfishFormRef.value.onSaveDraft().then(() => {
      ElMessage.success('暂存成功')
      router.back()
    }).catch((err) => {
      console.log(err)
    })
  } catch (error) {
    console.log(error)
  }
}
</script>
