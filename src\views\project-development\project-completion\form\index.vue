<template>
  <Container show-back>
    <div class="wrapper">
      <ElForm
        ref="formRef"
        label-position="top"
        :model="formData.baseInfo"
        :rules="rules">
        <BaseInfo v-model="formData" />
        <FinancialSettlementInfo
          v-if="isFromApprovePage"
          v-model="formData" />
        <ProjectClosingStatement v-model="formData" />
      </ElForm>
    </div>
    <template #headerRight>
      <div style="display: flex;flex: auto; justify-content: flex-end;">
        <ElButton
          type="primary"
          plain
          @click="temporarilyStoreForm">
          暂存
        </ElButton>
        <ElButton
          type="primary"
          @click="submitForm">
          提交
        </ElButton>
      </div>
    </template>
  </Container>
</template>

<script setup>
import { getProjectCompletionDetailByBid, startProjectCompletion } from '@/api/project-development/project-completion.js'
import * as wFlowApis from '@/api/wflow-pro.js'
import Container from '@/components/Container/index.vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import BaseInfo from './baseInfo.vue'
import FinancialSettlementInfo from './financialSettlementInfo.vue'
import ProjectClosingStatement from './projectClosingStatement.vue'

const props = defineProps({
  id: {
    default: '',
  },
})

const router = useRouter()
const route = useRoute()

const formRef = useTemplateRef('formRef')

const formData = ref({
  baseInfo: {},
})

const rules = reactive({
  prdProjectName: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  supplementaryInstruction: [{ required: true, message: '请输入', trigger: 'change' }],
  completionExplainAttachment: [{ required: true, message: '请上传附件', trigger: 'change' }],
  actualOutsourcingCostAmount: [{ required: true, message: '请输入', trigger: 'change' }],
  actualJobLaborCostAmount: [{ required: true, message: '请输入', trigger: 'change' }],
  actualProjectManagerCostAmount: [{ required: true, message: '请输入', trigger: 'change' }],
})

async function temporarilyStoreForm() {
  formData.value.options = 0 // 0表示暂存
  // 暂存时新拷贝一份数据，不改动原始数据，防止多次点击多次修改附件原始数据
  const data = JSON.parse(JSON.stringify(formData.value))
  // 附件
  data.baseInfo.completionExplainAttachment = data.baseInfo.completionExplainAttachment ? data.baseInfo.completionExplainAttachment.map(item => item.url || item.response.data) : []
  data.baseInfo.financialActualAttachment = data.baseInfo.financialActualAttachment ? data.baseInfo.financialActualAttachment.map(item => item.url || item.response.data) : []
  const res = await startProjectCompletion(data)
  if (res.code === 200) {
    ElMessage.success('暂存成功')
    router.push('/project-development/project-completion/list')
  }
}

function submitForm() {
  formRef.value.validate().then(async () => {
    const processDefId = await wFlowApis.getCurrentProcessId('project-completion')
    formData.value.processDefId = processDefId
    formData.value.options = 1 // 1表示提交
    // 提交时新拷贝一份数据，不改动原始数据，防止多次点击多次修改附件原始数据
    const data = JSON.parse(JSON.stringify(formData.value))
    // 附件
    data.baseInfo.completionExplainAttachment = data.baseInfo.completionExplainAttachment ? data.baseInfo.completionExplainAttachment.map(item => item.url || item.response.data) : []
    data.baseInfo.financialActualAttachment = data.baseInfo.financialActualAttachment ? data.baseInfo.financialActualAttachment.map(item => item.url || item.response.data) : []
    const res = await startProjectCompletion(data)
    if (res.code === 200) {
      ElMessage.success('提交成功')
      router.push('/project-development/project-completion/list')
    }
  }).catch((err) => {
    console.log('err', err)
  })
}

onMounted(async () => {
  if (route.query.id) {
    const res = await getProjectCompletionDetailByBid(route.query.id)
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.completionExplainAttachment = res.completionExplainAttachment.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    res.financialActualAttachment = res.financialActualAttachment.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  } else if (props.id) {
    const res = await getProjectCompletionDetailByBid(props.id)
    // 返回回来的附件信息转换为文件上传组件可以显示的数据结构
    res.completionExplainAttachment = res.completionExplainAttachment.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    res.financialActualAttachment = res.financialActualAttachment.map(item => ({ name: item.match(/[^/\\?#]+$/)[0], url: item }))
    formData.value.baseInfo = res
  }
})

const isFromApprovePage = computed(() => {
  const { instanceId, nodeId } = route.query
  return instanceId && nodeId
})

defineExpose({
  // 获取表单数据
  getFormData: () => {
    return readonly(unref(formData))
  },
  // 审批流程
  saveFormData: async () => {
    try {
      return Promise.resolve(readonly(unref(formData)))
    } catch (error) {
      return Promise.reject(new Error(error))
    }
  },
})
</script>

<style lang="scss" scoped>
.wrapper {
  flex: auto;
  overflow-y: auto;
  padding: 10px 18px;
  border-radius: 10px;
  background-color: #fff;
}
</style>
